{"realm": "connected-platform", "enabled": true, "privateKey": "MIICXAIBAAKBgQCrVrCuTtArbgaZzL1hvh0xtL5mc7o0NqPVnYXkLvgcwiC3BjLGw1tGEGoJaXDuSaRllobm53JBhjx33UNv+5z/UMG4kytBWxheNVKnL6GgqlNabMaFfPLPCF8kAgKnsi79NMo+n6KnSY8YeUmec/p2vjO2NjsSAVcWEQMVhJ31LwIDAQABAoGAfmO8gVhyBxdqlxmIuglbz8bcjQbhXJLR2EoS8ngTXmN1bo2L90M0mUKSdc7qF10LgETBzqL8jYlQIbt+e6TH8fcEpKCjUlyq0Mf/vVbfZSNaVycY13nTzo27iPyWQHK5NLuJzn1xvxxrUeXI6A2WFpGEBLbHjwpx5WQG9A+2scECQQDvdn9NE75HPTVPxBqsEd2z10TKkl9CZxu10Qby3iQQmWLEJ9LNmy3acvKrE3gMiYNWb6xHPKiIqOR1as7L24aTAkEAtyvQOlCvr5kAjVqrEKXalj0Tzewjweuxc0pskvArTI2Oo070h65GpoIKLc9jf+UA69cRtquwP93aZKtW06U8dQJAF2Y44ks/mK5+eyDqik3koCI08qaC8HYq2wVl7G2QkJ6sbAaILtcvD92ToOvyGyeE0flvmDZxMYlvaZnaQ0lcSQJBAKZU6umJi3/xeEbkJqMfeLclD27XGEFoPeNrmdx0q10Azp4NfJAY+Z8KRyQCR2BEG+oNitBOZ+YXF9KCpH3cdmECQHEigJhYg+ykOvr1aiZUMFT72HU0jnmQe2FVekuG+LJUt2Tm7GtMjTFoGpf0JwrVuZN39fOYAlo+nTixgeW7X8Y=", "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrVrCuTtArbgaZzL1hvh0xtL5mc7o0NqPVnYXkLvgcwiC3BjLGw1tGEGoJaXDuSaRllobm53JBhjx33UNv+5z/UMG4kytBWxheNVKnL6GgqlNabMaFfPLPCF8kAgKnsi79NMo+n6KnSY8YeUmec/p2vjO2NjsSAVcWEQMVhJ31LwIDAQAB", "requiredCredentials": ["password"], "sslRequired": "external", "roles": {"realm": [{"name": "Application"}, {"name": "OrganizationAdmin"}, {"name": "ClientUser"}, {"name": "PartnerUser"}, {"name": "ClientAdmin", "composite": true, "composites": {"realm": ["OrganizationAdmin", "ClientUser"]}}, {"name": "DistributorUser"}, {"name": "DistributorAdmin", "composite": true, "composites": {"realm": ["OrganizationAdmin", "DistributorUser"]}}]}, "users": [{"enabled": true, "username": "service-account-organization-management", "serviceAccountClientId": "organization-management", "clientRoles": {"realm-management": ["view-authorization", "view-users", "view-realm", "view-clients", "view-identity-providers", "manage-authorization", "manage-users", "manage-realm", "manage-clients"], "organization-management": ["uma_protection"]}}], "clients": [{"clientId": "organization-management", "secret": "org-management-secret", "enabled": true, "baseUrl": "http://localhost:8080", "adminUrl": "http://localhost:8080", "redirectUris": ["http://localhost:8000/*"], "directAccessGrantsEnabled": true, "authorizationServicesEnabled": true, "defaultClientScopes": ["email", "organization", "profile", "roles"]}, {"clientId": "organization-management-ui", "baseUrl": "http://localhost:8000", "adminUrl": "http://localhost:8000", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["http://localhost:8000/*"], "directAccessGrantsEnabled": true, "publicClient": true, "defaultClientScopes": ["email", "roles", "profile", "organization", "organization-management"]}], "clientScopes": [{"name": "organization-management", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true"}, "protocolMappers": [{"name": "Audience for organizations", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "consentRequired": false, "config": {"included.client.audience": "organization-management", "id.token.claim": "false", "access.token.claim": "true", "userinfo.token.claim": "false"}}]}, {"name": "contacts", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true"}, "protocolMappers": []}, {"name": "organizations", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true"}, "protocolMappers": [{"name": "Organization Paths", "protocol": "openid-connect", "protocolMapper": "script-organizations-mapper.js", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "claim.name": "organizations", "jsonType.label": "JSON", "userinfo.token.claim": "true"}}]}, {"name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"name": "organization", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"name": "Organization ID", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"aggregate.attrs": "true", "userinfo.token.claim": "true", "user.attribute": "organization_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "organization.id", "jsonType.label": "int"}}, {"name": "Organization Type", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"aggregate.attrs": "true", "userinfo.token.claim": "true", "user.attribute": "organization_type", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "organization.type", "jsonType.label": "string"}}]}, {"name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"name": "application-management", "protocol": "openid-connect", "attributes": {"include.in.token.scope": true, "display.on.consent.screen": false}, "protocolMappers": [{"name": "Application ID", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "application_id", "id.token.claim": "false", "access.token.claim": "true", "claim.name": "application.id", "jsonType.label": "int"}}, {"name": "application-management audience", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "consentRequired": false, "config": {"id.token.claim": "false", "access.token.claim": "true", "included.custom.audience": "application-management"}}]}], "scopeMappings": [{"clientScope": "application-management", "roles": ["Application"]}], "defaultDefaultClientScopes": ["organization", "profile", "roles", "email"], "identityProviders": [{"alias": "okta", "providerId": "oidc", "config": {"clientId": "client-id", "clientSecret": "**********", "authorizationUrl": "https://example.com/v1/authorize", "tokenUrl": "https://example.com/v1/token", "clientAuthMethod": "client_secret_post"}}]}