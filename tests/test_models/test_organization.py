import datetime

import pytest
import sqlalchemy.exc
from sqlalchemy.exc import IntegrityError

from app.enums import OrganizationTypeEnum
from app.models import Organization
from app.models.organization import Client, Distributor, Partner


def test_root_distributor(db):
    root = Distributor(name="Acme")
    db.add(root)
    db.commit()
    db.refresh(root)
    assert root.type == OrganizationTypeEnum.DISTRIBUTOR


def test_root_distributor_client(db):
    distributor = Distributor(name="Acme")
    client_org = Client(name="ClientA", parent=distributor)
    db.add_all([distributor, client_org])
    db.commit()
    db.refresh(client_org)
    assert distributor.type == OrganizationTypeEnum.DISTRIBUTOR
    assert client_org.parent == distributor
    assert client_org.type == OrganizationTypeEnum.CLIENT
    assert client_org.parent_type == distributor.type
    assert client_org.parent_id == distributor.id
    assert distributor.clients == [client_org]


def test_sub_distributor(db):
    root = Distributor(name="Acme")
    sub_distributor = Distributor(
        name="Acme-Sub",
        parent=root,
    )
    db.add_all([root, sub_distributor])
    db.commit()
    db.expire_all()
    assert sub_distributor.type == OrganizationTypeEnum.DISTRIBUTOR
    assert sub_distributor.parent == root
    assert sub_distributor.parent_id == root.id
    assert sub_distributor.parent_type == OrganizationTypeEnum.DISTRIBUTOR
    assert root.type == OrganizationTypeEnum.DISTRIBUTOR
    assert root.sub_distributors == [sub_distributor]


def test_clients(db):
    root = Distributor(name="Acme")
    root_client_a = Client(name="Acme-Client-A", parent=root)
    root_client_b = Client(name="Acme-Client-B", parent=root)
    sub_distributor = Distributor(name="Acme-Sub", parent=root)
    sub_client_a = Client(
        name="Acme-Sub-Client-A",
        parent=sub_distributor,
    )
    sub_client_b = Client(
        name="Acme-Sub-Client-B",
        parent=sub_distributor,
    )
    db.add_all(
        [
            root,
            root_client_a,
            root_client_b,
            sub_distributor,
            sub_client_a,
            sub_client_b,
        ]
    )
    db.commit()
    db.expire_all()

    assert root_client_a.parent == root
    assert root_client_b.parent == root
    assert root.children == [
        root_client_a,
        root_client_b,
        sub_distributor,
    ]
    assert root.sub_distributors == [sub_distributor]
    assert root.clients == [
        root_client_a,
        root_client_b,
    ]

    assert sub_distributor.parent == root
    assert sub_client_a.parent == sub_distributor
    assert sub_client_b.parent == sub_distributor
    assert sub_distributor.children == [
        sub_client_a,
        sub_client_b,
    ]


def test_can_create_partner_organization(db):
    distributor = Distributor(name="Next")
    partner = Partner(name="Acme", parent=distributor)
    db.add_all([distributor, partner])
    db.commit()
    retrieved = db.query(Partner).filter_by(name="Acme").first()
    assert retrieved


def test_client_without_distributor(db):
    client_org = Client(name="Acme")
    db.add(client_org)
    with pytest.raises(IntegrityError):
        db.commit()


def test_partner_without_distributor(db):
    partner = Partner(name="Acme")
    db.add(partner)
    with pytest.raises(IntegrityError):
        db.commit()


@pytest.mark.parametrize(
    "model",
    [
        Client,
        Partner,
    ],
)
def test_client_as_distributor(db, model):
    distributor = Distributor(name="Acme")
    non_distributor = model(name="Org A", parent=distributor)
    db.add_all([distributor, non_distributor])
    db.commit()
    db.refresh(non_distributor)

    client_b = Organization(
        name="Org B",
        type=non_distributor.type,
        parent_id=non_distributor.id,
        parent_type=non_distributor.type,
    )
    db.add(client_b)
    with pytest.raises(IntegrityError):
        db.commit()


def test_ck_parent_type_if_parent_id(db):
    d = Distributor(name="Acme")
    db.add(d)
    db.commit()
    db.refresh(d)
    c = Distributor(
        name="Acme-C",
        parent_id=d.id,
    )
    db.add(c)
    with pytest.raises(IntegrityError):
        db.commit()


def test_ck_parent_id_if_parent_type(db):
    d = Distributor(name="Acme")
    db.add(d)
    db.commit()
    c = Distributor(
        name="Acme-C",
        parent_type=d.type,
    )
    db.add(c)
    with pytest.raises(IntegrityError):
        db.commit()


def test_create_at_field_default(db):
    d = Distributor(name="Acme")
    db.add(d)
    db.commit()
    db.refresh(d)
    assert isinstance(d.created_at, datetime.datetime)
    assert d.created_at.tzinfo == datetime.timezone.utc


def test_create_at_field_timezone_awareness(db):
    d = Client(name="Acme", created_at=datetime.datetime.now())
    db.add(d)
    with pytest.raises(sqlalchemy.exc.StatementError) as exc_info:
        db.commit()
    assert "tzinfo is required" in exc_info.value.args[0]


def test_organization_path(db):
    d = Distributor(name="distributor")
    client_a = Client(name="client A", parent=d)
    sub_d = Distributor(name="sub-distributor", parent=d)
    client_b = Client(name="client B", parent=sub_d)
    organizations = [d, client_a, sub_d, client_b]
    db.add_all(organizations)
    db.commit()
    for org in organizations:
        db.refresh(org)
    assert d.path == f"/{d.name}"
    assert client_a.path == f"/{d.name}/{client_a.name}"
    assert sub_d.path == f"/{d.name}/{sub_d.name}"
    assert client_b.path == f"/{d.name}/{sub_d.name}/{client_b.name}"
