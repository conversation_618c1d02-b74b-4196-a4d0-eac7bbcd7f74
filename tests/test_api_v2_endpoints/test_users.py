import uuid
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON>ck, patch

import pytest
from fastapi import <PERSON>TTP<PERSON>x<PERSON>, Request
from starlette import status

from app import schemas
from app.api.api_v2.endpoints.users import (
    create_organization_user,
    read_organization_users,
    read_users,
)
from app.common.ordering import Ordering
from app.common.pagination import PaginatedResponse, Pagination
from app.enums import OrganizationRoleEnum, OrganizationScopeEnum, OrganizationTypeEnum
from app.models import Organization
from app.oso import oso
from app.schemas.auth import RealmAccess
from tests.factories import DistributorFactory


@pytest.fixture
def mock_user_info():
    return schemas.UserInfo(
        id=str(uuid.uuid4()),
        username="test-user",
        email="<EMAIL>",
        roles=["admin"],
        email_verified=True,
        typ="Bearer",
        organization=schemas.OrganizationSimple(
            id=1, type=OrganizationTypeEnum.CLIENT, name="Test Org", parent_id=1
        ),
        realm_access=RealmAccess(roles=[OrganizationRoleEnum.DISTRIBUTOR_ADMIN]),
        sub=str(uuid.uuid4()),
    )


@pytest.fixture
def mock_organization():
    return DistributorFactory.build()


@pytest.fixture
def mock_user_create_v2():
    return schemas.UserCreateV2(
        email="<EMAIL>",
        firstName="New",
        lastName="User",
        roles=[OrganizationRoleEnum.DISTRIBUTOR_ADMIN],
        createdTimestamp=17826930209,
    )


@pytest.fixture
def mock_keycloak_user():
    return schemas.KeycloakUser(
        id=str(uuid.uuid4()),
        username="<EMAIL>",
        email="<EMAIL>",
        firstName="New",
        lastName="User",
        emailVerified=True,
        enabled=True,
        roles=["user"],
        createdTimestamp=17826930209,
    )


@pytest.fixture
def mock_redis_service(self, monkeypatch):
    """Mock RedisService to avoid actual Redis connections"""
    mock_service = Mock()
    mock_instance = mock_service.return_value
    mock_instance.get_user.return_value = {
        "id": str(uuid.uuid4()),
        "username": "test_user",
        "email": "<EMAIL>",
        "groups": "test_external_id",
    }

    # Patching RedisService
    monkeypatch.setattr("app.api.api_v2.endpoints.users.RedisService", mock_service)
    return mock_instance


@pytest.fixture
def update_user_data(self):
    """User update data for tests"""
    return {
        "first_name": "Updated",
        "last_name": "User",
        "email": "<EMAIL>",
        "enabled": True,
        "roles": [OrganizationRoleEnum.CLIENT_ADMIN.value],
    }


class TestCreateOrganizationUser:
    @patch("app.api.api_v2.endpoints.users.validate_user_in")
    @patch("app.api.api_v2.endpoints.users.add_user")
    @patch("app.api.api_v2.endpoints.users.fetch_user_roles")
    @patch("app.api.api_v2.endpoints.users.send_email")
    @patch("app.api.api_v2.endpoints.users.get_keycloak_user_response")
    @patch("app.api.api_v2.endpoints.users.RedisService")
    def test_create_organization_user_success(
        self,
        mock_redis_service,
        mock_get_keycloak_user_response,
        mock_send_email,
        mock_fetch_user_roles,
        mock_add_user,
        mock_validate_user_in,
        mock_organization,
        mock_user_info,
        mock_user_create_v2,
        mock_keycloak_user,
    ):
        mock_db = MagicMock()
        mock_iam = MagicMock()
        mock_audit = MagicMock()
        mock_mail_api = MagicMock()
        mock_request = MagicMock(spec=Request)
        mock_request.client.host = "127.0.0.1"
        mock_validate_user_in.return_value = mock_organization
        mock_user = MagicMock()
        mock_user_idp = MagicMock()
        mock_add_user.return_value = (mock_user, mock_user_idp)

        mock_redis_instance = mock_redis_service.return_value
        mock_redis_instance.get_user.return_value = {"roles": ["user"]}

        mock_fetch_user_roles.return_value = ["user"]
        mock_get_keycloak_user_response.return_value = mock_keycloak_user

        result = create_organization_user(
            db=mock_db,
            user_info=mock_user_info,
            iam=mock_iam,
            mail_api=mock_mail_api,
            org_id=mock_organization.id,
            user_in=mock_user_create_v2,
            request=mock_request,
            audit_service=mock_audit,
        )

        mock_validate_user_in.assert_called_once_with(
            mock_db, mock_user_info, mock_organization.id, mock_user_create_v2
        )
        mock_add_user.assert_called_once_with(
            mock_db,
            mock_iam,
            mock_user_info,
            mock_user_create_v2,
            mock_organization,
        )
        mock_redis_instance.get_user.assert_called_once_with(user_id=mock_user.id)
        mock_fetch_user_roles.assert_called_once_with(
            mock_iam, mock_user.id, mock_redis_instance.get_user.return_value
        )
        mock_send_email.assert_not_called()
        mock_get_keycloak_user_response.assert_called_once_with(
            mock_user, mock_fetch_user_roles.return_value, mock_user_idp
        )
        assert result == mock_keycloak_user


class TestReadUsers:
    @patch("app.api.api_v2.endpoints.users.oso.authorize_request")
    @patch("app.api.api_v2.endpoints.users.fetch_all_users")
    def test_read_users_success(
        self, mock_fetch_all_users, mock_authorize_request, mock_user_info
    ):
        mock_db = MagicMock()
        mock_iam = MagicMock()
        mock_request = MagicMock()
        mock_audit_service = MagicMock()
        mock_pagination = MagicMock(spec=Pagination)
        mock_ordering = MagicMock(spec=Ordering)
        expected_result = PaginatedResponse(
            page=1,
            pageSize=10,
            lastPage=1,
            totalCount=1,
            results=[
                schemas.KeyCloakUserDetails(
                    id=str(uuid.uuid4()),
                    username="<EMAIL>",
                    email="<EMAIL>",
                    firstName="User",
                    lastName="One",
                    emailVerified=True,
                    enabled=True,
                    createdTimestamp=1234567890,
                    lastLoginTimestamp=1234567890,
                )
            ],
            total=1,
        )
        mock_fetch_all_users.return_value = expected_result

        result = read_users(
            request=mock_request,
            db=mock_db,
            iam=mock_iam,
            pagination=mock_pagination,
            user_info=mock_user_info,
            search="test",
            ordering=mock_ordering,
            audit_service=mock_audit_service,
        )

        mock_authorize_request.assert_called_once_with(mock_user_info, mock_request)
        mock_fetch_all_users.assert_called_once_with(
            db=mock_db,
            iam=mock_iam,
            pagination=mock_pagination,
            search="test",
            ordering=mock_ordering,
            audit_service=mock_audit_service,
        )
        assert result == expected_result

    @patch("app.api.api_v2.endpoints.users.oso.authorize_request")
    def test_read_users_not_found(self, mock_authorize_request, mock_user_info):
        mock_request = MagicMock()
        mock_authorize_request.side_effect = oso.not_found_error

        with pytest.raises(HTTPException) as exc_info:
            read_users(
                request=mock_request,
                db=MagicMock(),
                iam=MagicMock(),
                pagination=MagicMock(),
                user_info=mock_user_info,
                search=None,
                ordering=MagicMock(),
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    @patch("app.api.api_v2.endpoints.users.oso.authorize_request")
    def test_read_users_forbidden(self, mock_authorize_request, mock_user_info):
        mock_request = MagicMock()
        mock_authorize_request.side_effect = oso.forbidden_error

        with pytest.raises(HTTPException) as exc_info:
            read_users(
                request=mock_request,
                db=MagicMock(),
                iam=MagicMock(),
                pagination=MagicMock(),
                user_info=mock_user_info,
                search=None,
                ordering=MagicMock(),
            )

        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN


class TestReadOrganizationUsers:
    @patch("app.api.api_v2.endpoints.users.get_object_or_404")
    @patch("app.api.api_v2.endpoints.users.oso.authorize")
    @patch("app.api.api_v2.endpoints.users.fetch_organization_users")
    def test_read_organization_users_success(
        self,
        mock_fetch_organization_users,
        mock_authorize,
        mock_get_object_or_404,
        mock_organization,
        mock_user_info,
    ):
        mock_db = MagicMock()
        mock_iam = MagicMock()
        mock_pagination = MagicMock(spec=Pagination)
        mock_ordering = MagicMock(spec=Ordering)
        mock_audit_service = MagicMock()
        mock_get_object_or_404.return_value = mock_organization
        expected_result = PaginatedResponse(
            page=1,
            pageSize=10,
            lastPage=1,
            totalCount=1,
            results=[
                schemas.KeyCloakUserDetails(
                    id=str(uuid.uuid4()),
                    username="<EMAIL>",
                    email="<EMAIL>",
                    firstName="Org",
                    lastName="User",
                    emailVerified=True,
                    enabled=True,
                    createdTimestamp=1234567890,
                    lastLoginTimestamp=1234567890,
                )
            ],
            total=1,
        )

        mock_fetch_organization_users.return_value = expected_result

        result = read_organization_users(
            org_id=mock_organization.id,
            search="test",
            pagination=mock_pagination,
            db=mock_db,
            iam=mock_iam,
            user_info=mock_user_info,
            ordering=mock_ordering,
            audit_service=mock_audit_service,
        )

        mock_get_object_or_404.assert_called_once_with(
            mock_db, Organization, False, id=mock_organization.id
        )
        mock_authorize.assert_called_once_with(
            mock_user_info, OrganizationScopeEnum.READ, mock_organization
        )
        mock_fetch_organization_users.assert_called_once_with(
            mock_iam,
            mock_organization,
            mock_pagination,
            "test",
            mock_ordering,
            audit_service=mock_audit_service,
        )
        assert result == expected_result

    @patch("app.api.api_v2.endpoints.users.get_object_or_404")
    @patch("app.api.api_v2.endpoints.users.oso.authorize")
    def test_read_organization_users_not_found(
        self,
        mock_authorize,
        mock_get_object_or_404,
        mock_organization,
        mock_user_info,
    ):
        mock_get_object_or_404.return_value = mock_organization
        mock_authorize.side_effect = oso.not_found_error

        with pytest.raises(HTTPException) as exc_info:
            read_organization_users(
                org_id=mock_organization.id,
                search=None,
                pagination=MagicMock(),
                db=MagicMock(),
                iam=MagicMock(),
                user_info=mock_user_info,
                ordering=MagicMock(),
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND


# @pytest.mark.no_keycloak
class TestUpdateOrganizationUser:
    def test_update_user_success(
        self,
        client,
        api_url,
        login_as,
        client_admin,
        client_org,
        mock_redis_service,
        update_user_data,
        monkeypatch,
    ):
        """Test successful user update"""

        user_id = uuid.uuid4()
        mock_redis_service.get_user.return_value = {
            "id": str(user_id),
            "username": "test_user",
            "email": "<EMAIL>",
            "groups": str(client_org.external_id),
        }

        mock_update_user = Mock()
        mock_update_user.return_value = {
            "id": str(user_id),
            "username": "test_user",
            "email": update_user_data["email"],
            "firstName": update_user_data["first_name"],
            "lastName": update_user_data["last_name"],
            "enabled": update_user_data["enabled"],
            "roles": update_user_data["roles"],
        }
        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.update_user", mock_update_user
        )

        mock_validate = Mock(return_value=client_org)
        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.validate_user_in", mock_validate
        )

        login_as(client_admin)
        response = client.put(
            api_url("update_organization_user", org_id=client_org.id, user_id=user_id),
            json=update_user_data,
        )

        assert response.status_code == status.HTTP_200_OK, response.content

        user_data = response.json()
        assert user_data["id"] == str(user_id)
        assert user_data["email"] == update_user_data["email"]
        assert user_data["firstName"] == update_user_data["first_name"]
        assert user_data["lastName"] == update_user_data["last_name"]
        assert user_data["enabled"] == update_user_data["enabled"]

        mock_validate.assert_called_once()
        mock_redis_service.get_user.assert_called_once_with(user_id=user_id)
        mock_update_user.assert_called_once()

    def test_update_user_not_found(
        self,
        client,
        api_url,
        login_as,
        client_admin,
        client_org,
        mock_redis_service,
        update_user_data,
        monkeypatch,
    ):
        """Test when user is not found in Redis"""
        user_id = uuid.uuid4()
        mock_redis_service.get_user.return_value = None

        mock_validate = Mock(return_value=client_org)
        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.validate_user_in", mock_validate
        )

        login_as(client_admin)
        response = client.put(
            api_url("update_organization_user", org_id=client_org.id, user_id=user_id),
            json=update_user_data,
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND, response.content
        mock_validate.assert_called_once()
        mock_redis_service.get_user.assert_called_once_with(user_id=user_id)

    def test_update_user_wrong_organization(
        self,
        client,
        api_url,
        login_as,
        client_admin,
        client_org,
        mock_redis_service,
        update_user_data,
        monkeypatch,
    ):
        """Test when user belongs to a different organization"""
        user_id = uuid.uuid4()
        mock_redis_service.get_user.return_value = {
            "id": str(user_id),
            "username": "test_user",
            "email": "<EMAIL>",
            "groups": "different_external_id",
        }

        mock_validate = Mock(return_value=client_org)
        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.validate_user_in", mock_validate
        )

        login_as(client_admin)
        response = client.put(
            api_url("update_organization_user", org_id=client_org.id, user_id=user_id),
            json=update_user_data,
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND, response.content
        mock_validate.assert_called_once()
        mock_redis_service.get_user.assert_called_once_with(user_id=user_id)

    def test_update_user_validation_error(
        self,
        client,
        api_url,
        login_as,
        client_admin,
        client_org,
        update_user_data,
        monkeypatch,
    ):
        """Test when validate_user_in raises an error"""
        user_id = uuid.uuid4()

        def mock_validate(*args, **kwargs):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized"
            )

        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.validate_user_in", mock_validate
        )

        login_as(client_admin)
        response = client.put(
            api_url("update_organization_user", org_id=client_org.id, user_id=user_id),
            json=update_user_data,
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN, response.content

    def test_update_user_service_error(
        self,
        client,
        api_url,
        login_as,
        client_admin,
        client_org,
        mock_redis_service,
        update_user_data,
        monkeypatch,
    ):
        """Test when update_user service function raises an error"""
        user_id = uuid.uuid4()
        mock_redis_service.get_user.return_value = {
            "id": str(user_id),
            "username": "test_user",
            "email": "<EMAIL>",
            "groups": str(client_org.external_id),
        }
        mock_validate = Mock(return_value=client_org)
        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.validate_user_in", mock_validate
        )

        def mock_update_user(*args, **kwargs):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid data"
            )

        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.update_user", mock_update_user
        )

        login_as(client_admin)
        response = client.put(
            api_url("update_organization_user", org_id=client_org.id, user_id=user_id),
            json=update_user_data,
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.content

    def test_update_user_invalid_data(
        self,
        client,
        api_url,
        login_as,
        client_admin,
        client_org,
    ):
        """Test with invalid request data"""

        user_id = uuid.uuid4()
        invalid_data = {"first_name": "", "roles": ["INVALID_ROLE"]}

        login_as(client_admin)
        response = client.put(
            api_url("update_organization_user", org_id=client_org.id, user_id=user_id),
            json=invalid_data,
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_update_user_unauthorized(
        self,
        client,
        api_url,
        login_as,
        client_user,
        client_org,
        update_user_data,
        monkeypatch,
    ):
        """Test when user is not authorized to update users"""
        user_id = uuid.uuid4()

        def mock_validate(*args, **kwargs):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized"
            )

        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.validate_user_in", mock_validate
        )

        login_as(client_user)
        response = client.put(
            api_url("update_organization_user", org_id=client_org.id, user_id=user_id),
            json=update_user_data,
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN, response.content

    def test_update_user_org_not_found(
        self,
        client,
        api_url,
        login_as,
        client_admin,
        update_user_data,
        monkeypatch,
    ):
        """Test when organization is not found"""
        user_id = uuid.uuid4()
        non_existent_org_id = 99999

        def mock_validate(*args, **kwargs):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Organization not found"
            )

        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.validate_user_in", mock_validate
        )

        login_as(client_admin)
        response = client.put(
            api_url(
                "update_organization_user", org_id=non_existent_org_id, user_id=user_id
            ),
            json=update_user_data,
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND, response.content

    def test_update_user_distributor(
        self,
        client,
        api_url,
        login_as,
        client_admin,
        update_user_data,
        monkeypatch,
    ):
        """Test when organization is not found"""
        user_id = uuid.uuid4()
        non_existent_org_id = 99999

        def mock_validate(*args, **kwargs):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Bad Request"
            )

        monkeypatch.setattr(
            "app.api.api_v2.endpoints.users.validate_user_in", mock_validate
        )

        login_as(client_admin)
        response = client.put(
            api_url(
                "update_organization_user", org_id=non_existent_org_id, user_id=user_id
            ),
            json=update_user_data,
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND, response.content
