import factory
from factory import faker

from app.enums import OrganizationTypeEnum
from app.models import Client, Distributor, Domain, PMNCode
from app.models.organization import Partner

from .base import BaseFactory
from .utils import static_utc_now


class OrganizationFactory(BaseFactory):
    name = faker.Faker("pystr")
    type = factory.Iterator(OrganizationTypeEnum)
    created_at = factory.LazyAttribute(lambda o: o.now())

    class Params:
        now = static_utc_now


class DistributorFactory(OrganizationFactory):
    type = OrganizationTypeEnum.DISTRIBUTOR

    class Meta:
        model = Distributor


class ClientFactory(OrganizationFactory):
    type = OrganizationTypeEnum.CLIENT
    parent = factory.SubFactory(DistributorFactory)

    class Meta:
        model = Client


class PartnerFactory(OrganizationFactory):
    type = OrganizationTypeEnum.PARTNER
    parent = factory.SubFactory(DistributorFactory)

    class Meta:
        model = Partner


class PMNCodeFactory(BaseFactory):
    class Meta:
        model = PMNCode

    """
    bothify is faker's provider method to replace characters with random
    letters and numbers. Here '?' signifies letter and '#' signifies number
    """
    pmn_code = factory.Faker("bothify", text="???##")
    org_id = factory.LazyAttribute(lambda o: OrganizationFactory().id)


class DomainFactory(BaseFactory):
    class Meta:
        model = Domain

    domain_name = factory.Faker("domain_name")
    org_id = factory.LazyAttribute(lambda o: OrganizationFactory().id)
