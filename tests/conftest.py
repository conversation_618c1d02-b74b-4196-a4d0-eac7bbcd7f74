import os
import uuid
from collections.abc import Generator
from copy import copy
from operator import itemgetter

import httpx
import pytest
from alembic import command
from alembic.config import Config
from fastapi.testclient import TestClient
from keycloak_client import KeycloakAdmin
from pydantic import EmailStr, SecretStr
from pytest_lazyfixture import <PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import orm
from sqlalchemy.engine import Connection

from app import schemas
from app.adapters.core_api.in_memory import InMemory<PERSON>oreAP<PERSON>
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.in_memory import IAMStore, InMemoryIAM
from app.adapters.iam.keycloak import KeycloakIAM
from app.adapters.iam.schemas import ClientScope, Role, UserCreate
from app.adapters.platform_api_client.audit_service import AbstractAuditAPI
from app.adapters.platform_api_client.schemas import AuditLogModel
from app.api import deps
from app.constants import APP_DIR
from app.core.config import settings
from app.core.utils import get_keycloak
from app.db.base import Base
from app.db.session import SessionLocal, engine
from app.enums import OrganizationRoleEnum, OrganizationTypeEnum
from app.main import app
from app.models import Client, Distributor, Organization
from app.models.organization import Partner
from app.schemas.keycloak import UserWithCredentials
from app.schemas.organization import OrganizationNameStr
from app.services import org_management
from tests.common import Session
from tests.utils.keycloak import delete_test_realm, import_test_realm

# DATABASE


@pytest.fixture(scope="session", autouse=True)
def setup_db():
    safe_conditions = [
        "test" in settings.DATABASE_URI.path,
        settings.DATABASE_URI.host in ["localhost", "postgres"],
    ]
    if not all(safe_conditions):
        raise ValueError("Please ensure you not running tests on production!")

    Base.metadata.create_all(bind=engine)
    stamp_alembic_head()
    yield
    Base.metadata.drop_all(bind=engine)


def stamp_alembic_head():
    alembic_cfg_file = APP_DIR / ".." / "alembic.ini"
    alembic_cfg = Config(alembic_cfg_file)
    cwd = os.getcwd()
    os.chdir(alembic_cfg_file.parent)
    try:
        command.stamp(alembic_cfg, "head")
    finally:
        os.chdir(cwd)


@pytest.fixture(scope="session")
def connection() -> Connection:
    with engine.connect() as connection:
        SessionLocal.configure(bind=connection)
        yield connection


@pytest.fixture(scope="function", autouse=True)
def _transaction_wrap(connection):
    transaction = connection.begin()
    yield connection
    transaction.rollback()


@pytest.fixture(scope="function")
def db() -> Generator[orm.Session, None, None]:
    session = Session()
    yield session
    session.close()


# API


@pytest.fixture(scope="function")
def client(connection, iam) -> Generator[TestClient, None, None]:
    app.dependency_overrides[deps.get_bind] = lambda: connection
    app.dependency_overrides[deps.get_iam] = lambda: iam
    with TestClient(app) as c:
        yield c


@pytest.fixture()
def api_url():
    def get_api_url(name: str, **params: str):
        return app.url_path_for(name, **params)

    return get_api_url


@pytest.fixture(scope="function")
def patch_dependency():
    original_overrides = copy(app.dependency_overrides)

    def _patch_dependency(target, new):
        app.dependency_overrides[target] = new

    try:
        yield _patch_dependency
    finally:
        app.dependency_overrides = original_overrides


@pytest.fixture()
def core_api() -> InMemoryCoreAPI:
    return InMemoryCoreAPI()


@pytest.fixture()
def patch_core_api(patch_dependency, core_api):
    patch_dependency(deps.get_core_api, lambda: core_api)


@pytest.fixture()
def patch_service_info(patch_dependency):
    def _patch_service_info(service_info: schemas.ServiceInfo):
        patch_dependency(deps.get_service_info, lambda: service_info)
        patch_dependency(deps.get_actor, lambda: service_info)

    return _patch_service_info


# KEYCLOAK


@pytest.fixture(scope="session")
def test_realm():
    if settings.KEYCLOAK_URL.host not in [
        "keycloak",
        "localhost",
    ]:
        raise ValueError("Please ensure you not running tests on production!")

    try:
        delete_test_realm()
    except httpx.HTTPStatusError as e:
        if e.response.status_code != 404:
            raise e
    realm_data = import_test_realm()
    yield realm_data
    delete_test_realm()


@pytest.fixture
def kc(test_realm) -> Generator[KeycloakAdmin, None, None]:
    keycloak: KeycloakAdmin
    with get_keycloak() as keycloak:
        scopes = list(map(itemgetter("name"), test_realm.get("clientScopes", [])))
        roles = list(map(itemgetter("name"), keycloak.realm_roles.get_many()))
        users = list(map(itemgetter("username"), test_realm.get("users", [])))
        groups = list(map(itemgetter("path"), test_realm.get("groups", [])))
        password_policy = test_realm.get("passwordPolicy", "")

        yield keycloak

        for scope in keycloak.client_scopes.get_many():
            if scope["name"] not in scopes:
                keycloak.client_scopes.delete(scope["id"])

        for role in keycloak.realm_roles.get_many():
            if role["name"] not in roles:
                keycloak.realm_roles.delete(role["id"])

        for user in keycloak.users.get_many():
            if user["username"] not in users:
                keycloak.users.delete(user["id"])

        for group in keycloak.groups.get_many():
            if group["path"] not in groups:
                keycloak.groups.delete(group["id"])

        realm = keycloak.realm.get()
        realm.update(passwordPolicy=password_policy)
        keycloak.realm.update(realm)


# APPLICATION


@pytest.fixture
def keycloak_iam(kc) -> AbstractIAM:
    return KeycloakIAM(kc)


@pytest.fixture
def iam(keycloak_iam) -> AbstractIAM:
    return keycloak_iam


@pytest.fixture()
def root_organization(db, iam) -> Distributor:
    organization_in = schemas.OrganizationCreate(
        name=OrganizationNameStr("root-organization"),
    )
    organization_db = org_management.create_organization(
        db,
        iam,
        organization_in=organization_in,
        org_type=OrganizationTypeEnum.DISTRIBUTOR,
    )
    organization = org_management.get_object(
        db, Organization, False, id=organization_db.id
    )

    yield organization

    org_management.remove_root_organization(
        db,
        iam,
        organization_name=organization_db.name,
        delete_users=True,
    )


@pytest.fixture()
def client_org(db, iam, root_organization) -> Client:
    result = org_management.create_organization(
        db,
        iam,
        organization_in=schemas.OrganizationCreate(name=OrganizationNameStr("foo")),
        parent=root_organization,
        org_type=OrganizationTypeEnum.CLIENT,
    )
    organization = org_management.get_object(db, Organization, False, id=result.id)
    return organization


@pytest.fixture()
def create_client_org(db, iam):
    def _create_client(name: str, parent: Distributor) -> Client:
        result = org_management.create_organization(
            db,
            iam,
            organization_in=schemas.OrganizationCreate(name=OrganizationNameStr(name)),
            org_type=OrganizationTypeEnum.CLIENT,
            parent=parent,
        )
        organization = org_management.get_object(db, Organization, False, id=result.id)
        return organization

    return _create_client


@pytest.fixture
def create_partner_org(db, iam, root_organization):
    def _create_partner(name: str, parent: Distributor = root_organization) -> Partner:
        result = org_management.create_organization(
            db,
            iam,
            organization_in=schemas.OrganizationCreate(name=OrganizationNameStr(name)),
            org_type=OrganizationTypeEnum.PARTNER,
            parent=parent,
        )
        organization = org_management.get_object(db, Organization, False, id=result.id)
        return organization

    return _create_partner


@pytest.fixture()
def create_distributor_org(db, iam):
    def _create_distributor(name: str, parent: Distributor | None = None) -> Client:
        result = org_management.create_organization(
            db,
            iam,
            organization_in=schemas.OrganizationCreate(name=OrganizationNameStr(name)),
            org_type=OrganizationTypeEnum.DISTRIBUTOR,
            parent=parent,
        )
        organization = org_management.get_object(db, Organization, False, id=result.id)
        return organization

    return _create_distributor


@pytest.fixture()
def create_user(iam):
    def _create_user(
        *,
        email: str = None,
        password: str = "*******",
        **kwargs,
    ) -> schemas.keycloak.UserWithCredentials:
        data = dict(
            username=str(uuid.uuid4()),
            email=EmailStr(email or f"{uuid.uuid4().hex[:10]}@example.com"),
            email_verified=True,
            enabled=True,
        )
        data.update(kwargs)

        user_in = UserCreate.parse_obj(data)
        user = iam.users.add(user_in)
        if password:
            iam.users.set_password(user.id, SecretStr(password), temporary=False)
        return schemas.keycloak.UserWithCredentials(
            id=user.id,
            email=user.email,
            username=user.username,
            firstName=user.first_name,
            lastName=user.last_name,
            attributes=user.attributes,
            enabled=user.enabled,
            emailVerified=user.email_verified,
            password=SecretStr(password),
        )

    return _create_user


@pytest.fixture()
def unbound_user(create_user):
    return create_user(
        email=EmailStr("<EMAIL>"),
        username="regular_user",
        password="foo-bar-pass",
    )


@pytest.fixture()
def distributor_admin(
    root_organization,
    create_org_user,
) -> schemas.keycloak.UserWithCredentials:
    return create_org_user(
        root_organization,
        email="<EMAIL>",
        username="admin-user",
        password="pa55word!",
        role=OrganizationRoleEnum.DISTRIBUTOR_ADMIN,
    )


@pytest.fixture()
def distributor_user(
    root_organization,
    create_org_user,
) -> schemas.keycloak.UserWithCredentials:
    return create_org_user(
        root_organization,
        email="<EMAIL>",
        username="distributor-user",
        password="pa55word!",
        role=OrganizationRoleEnum.DISTRIBUTOR_USER,
    )


@pytest.fixture()
def client_user(client_org, create_org_user):
    return create_org_user(client_org, role=OrganizationRoleEnum.CLIENT_USER)


@pytest.fixture()
def client_admin(client_org, create_org_user):
    return create_org_user(client_org, role=OrganizationRoleEnum.CLIENT_ADMIN)


@pytest.fixture()
def create_org_user(iam, create_user, db):
    def _create_org_user(
        organization: Organization,
        *,
        role: OrganizationRoleEnum,
        username: str = None,
        email: str = None,
        password: str = "*******",
        **kwargs,
    ):
        username = username or email or f"{organization.name}-{role.value.lower()}"
        user = create_user(
            username=username,
            password=password,
            email=EmailStr(email or f"{username}@example.com"),
            **kwargs,
        )
        org_db = org_management.get_object(db, Organization, False, id=organization.id)
        org_management.add_user_to_organization(
            iam,
            organization=org_db,
            user_id=user.id,
            organization_role=role,
        )
        return user

    return _create_org_user


@pytest.fixture()
def create_dummy_role(iam):
    maybe_role_name: str | None = None

    def _create_dummy_role(name: str = "dummy-role"):
        nonlocal maybe_role_name
        role = iam.roles.add(Role(name=name))
        maybe_role_name = name
        return role

    yield _create_dummy_role

    if maybe_role_name is not None:
        iam.roles.remove(maybe_role_name)


@pytest.fixture()
def create_dummy_scope(iam):
    maybe_scope_name: str | None = None

    def _create_dummy_scope(name: str = "dummy-scope") -> ClientScope:
        nonlocal maybe_scope_name
        scope = iam.client_scopes.add(ClientScope(name=name))
        maybe_scope_name = name
        return scope

    yield _create_dummy_scope

    if maybe_scope_name is not None:
        iam.client_scopes.remove(maybe_scope_name)


@pytest.fixture
def login_as(patch_dependency, iam, db):
    def _login_as(user: UserWithCredentials):
        roles = iam.users.get_roles(user.id)
        organization = org_management.get_user_org(db, iam, user_id=user.id)
        payload = dict(
            sub=user.id,
            username=user.username,
            email=user.email,
            email_verified=user.emailVerified,
            realm_access=dict(roles=roles),
            organization=dict(id=organization.id, type=organization.type)
            if organization
            else None,
            typ="Bearer",
            given_name=user.firstName,
            family_name=user.lastName,
            name=user.username,
        )
        patch_dependency(deps.introspect_token, lambda: payload)

    return _login_as


@pytest.fixture
def login_as_core_service(patch_service_info):
    patch_service_info(schemas.ServiceInfo(client_id=settings.OIDC_CLIENT_ID))


@pytest.fixture
def random_uuid() -> uuid.UUID:
    return uuid.uuid4()


@pytest.fixture
def random_email(random_uuid) -> EmailStr:
    return EmailStr(f"{random_uuid.hex[:10]}@example.com")


@pytest.fixture
def iam_store() -> IAMStore:
    return IAMStore()


@pytest.fixture
def in_memory_iam(iam_store) -> InMemoryIAM:
    return InMemoryIAM(iam_store)


def pytest_addoption(parser):
    parser.addoption(
        "--with-keycloak",
        action="store_const",
        default=False,
        const=True,
        help="Run tests with Keycloak integration",
    )


@pytest.hookimpl(tryfirst=True)
def pytest_runtest_setup(item):
    """Checks whether tests should be skipped based on config options."""

    def has_keylcoak_dependency() -> bool:
        if kc.__name__ in item.fixturenames:
            return True
        elif hasattr(item, "callspec"):
            call_spec = getattr(item, "callspec")
            for v in call_spec.params.values():
                if isinstance(v, LazyFixture):
                    if v.name == keycloak_iam.__name__:
                        return True

        return False

    if has_keylcoak_dependency():
        if item.config.getoption("--with-keycloak"):
            return
        pytest.skip("Keycloak integration test skipped")


@pytest.fixture
def create_audit():
    return AuditLogModel(
        user="<EMAIL>",
        ipAddress="192.0.01",
        event="POST",
        request="organization/report",
        source="organization",
        payload={"id": "key"},
    )


@pytest.fixture
def patch_audit_api(patch_dependency):
    def _patch(fake_audit_api: AbstractAuditAPI):
        patch_dependency(
            deps.get_audit_service,
            lambda: fake_audit_api,
        )

    return _patch
