import json
from unittest.mock import Magic<PERSON>ock

import httpx
import pytest
import respx
from platform_api_client import PlatformAPIClient
from starlette import status

from app.adapters.platform_api_client.audit_service import AuditAPI
from app.adapters.platform_api_client.schemas import AuditResponse
from app.core.config import settings

router = respx.mock(assert_all_called=False, assert_all_mocked=True)
router.get(
    url=settings.AUDIT_LOG_URI + AuditAPI.GET_AUDIT,
    name="audit",
)


@pytest.fixture
def api_client():
    mock_transport = httpx.MockTransport(router.handler)
    with httpx.Client(transport=mock_transport) as client:
        yield client


@pytest.fixture
def audit_api(api_client) -> AuditAPI:
    client = PlatformAPIClient(transport=api_client)
    return AuditAPI(client, settings.AUDIT_LOG_URI)


class TestAuditAPI:
    @router
    def test_get_audit(
        self,
        audit_api,
    ):
        audit = {
            "user": "<EMAIL>",
            "event": "GET",
            "request": "audit/log",
            "payload": {"id": "a14a3774-867a-4486-bcef-5a21df87257c"},
            "ipAddress": "127.0.0.1",
            "source": "user",
            "created_date": "2025-05-19T04:50:55.527000",
        }
        mock_paginated_response = {
            "page": 1,
            "page_size": 50,
            "total_count": 1,
            "last_page": 1,
            "results": [audit],
        }
        router["audit"].return_value = httpx.Response(
            status_code=status.HTTP_200_OK,
            content=json.dumps(mock_paginated_response),
        )
        expected_response = AuditResponse(**audit)
        audit_api._client.parse_payload = MagicMock(return_value=expected_response)
        result = audit_api.get_audit(
            id="a14a3774-867a-4486-bcef-5a21df87257c", event="GET"
        )
        assert result == AuditResponse(**audit)
