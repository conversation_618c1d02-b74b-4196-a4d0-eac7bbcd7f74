import uuid

import pytest
from keycloak_client import KeycloakException
from pydantic import EmailStr, SecretStr
from starlette import status

from app.adapters import iam
from app.adapters.iam import keycloak, schemas
from app.adapters.iam.exc import RoleNotFound, UserNotFound
from app.adapters.iam.schemas import FederatedIdentity, User
from app.services.authz_password import BasePasswordPolicy
from app.typedefs import Username
from tests.utils.keycloak import access_token_for


@pytest.fixture
def user_api(kc) -> keycloak.KeycloakUserAPI:
    return keycloak.KeycloakUserAPI(kc)


@pytest.fixture
def roles_api(kc) -> keycloak.KeycloakRoleAPI:
    return keycloak.KeycloakRoleAPI(kc)


class TestAddUser:
    @pytest.fixture(scope="class")
    def user_in(self):
        return schemas.UserCreate(
            username=Username("bob"),
            email=EmailStr("<EMAIL>"),
            first_name="<PERSON>",
            last_name="Sponge",
            enabled=True,
            email_verified=True,
            attributes=dict(mobilePhone=["+380931234567"]),
        )

    def test_user_created_in_keycloak(self, user_api, user_in):
        user = user_api.add(user_in)
        expected = dict(
            id=str(user.id),
            username=user.username,
            email=user.email,
            enabled=user.enabled,
            emailVerified=user.email_verified,
            attributes=user_in.attributes,
        )
        user_data = user_api.ka.users.get(str(user.id))
        actual = {k: v for k, v in user_data.items() if k in expected}
        assert actual == expected

    def test_returned_user_is_ok(self, user_api, user_in):
        user = user_api.add(user_in)
        assert user.dict(include=user_in.__fields_set__) == user_in.dict()


class TestGetUserById:
    def test_if_user_not_exists_return_none(self, user_api, random_uuid):
        result = user_api.get_by_id(random_uuid)
        assert result is None

    def test_if_user_exists_return_user(self, user_api, unbound_user):
        user = user_api.get_by_id(unbound_user.id)
        assert user
        assert user.email == unbound_user.email


class TestGetUserByEmail:
    def test_if_user_not_exists_return_none(self, user_api, random_email):
        result = user_api.get_by_email(random_email)
        assert result is None

    def test_if_user_exists_return_user(self, user_api, unbound_user):
        user = user_api.get_by_email(unbound_user.email)
        assert user
        assert user.id == unbound_user.id


class TestUpdateUser:
    def test_if_user_not_exists_raises_user_not_found(self, user_api, random_uuid):
        user_in = schemas.UserUpdate(
            id=random_uuid,
            email=EmailStr("<EMAIL>"),
            username=Username("bob"),
            enabled=True,
            email_verified=True,
        )
        with pytest.raises(UserNotFound):
            user_api.update(user_in)

    def test_user_attributes_updated(self, user_api, unbound_user, random_uuid):
        existing_user = user_api.get_by_id(unbound_user.id)

        user_in = existing_user.copy(deep=True)
        user_in.attributes = dict(bar=[str(uuid)])
        updated = user_api.update(user_in.to_update())

        assert updated.attributes == user_in.attributes
        keycloak_user = user_api.ka.users.get(str(existing_user.id))
        assert keycloak_user["attributes"] == user_in.attributes

    def test_add_federated_identity(self, user_api, distributor_user):
        existing_user = user_api.get_by_id(distributor_user.id)

        user_in = existing_user.copy(deep=True)
        identity = FederatedIdentity(
            identity_provider="okta", user_id="foo", user_name="bar"
        )
        user_api.add_federated_identity(user_in.id, identity)
        assert user_api.get_by_id(user_in.id).federated_identities == [identity]

    @pytest.mark.xfail(reason='Realm setting "editUsernameAllowed" set to false.')
    def test_update_username(self, user_api, unbound_user):
        existing_user = user_api.get_by_id(unbound_user.id)
        new_username = f"new{uuid.uuid4()}"
        user_in = existing_user.copy(deep=True, update=dict(username=new_username))
        updated = user_api.update(user_in.to_update())
        assert updated.username == new_username


class TestRemoveUser:
    def test_if_user_exists_user_removed_from_keycloak(self, user_api, unbound_user):
        user_api.remove(unbound_user.id)
        with pytest.raises(KeycloakException) as exc_info:
            user_api.ka.users.get(str(unbound_user.id))
        assert exc_info.value.response.status_code == status.HTTP_404_NOT_FOUND

    def test_if_user_not_exists_raises_user_not_found(self, user_api, random_uuid):
        with pytest.raises(UserNotFound):
            user_api.remove(random_uuid)


class TestListUsers:
    def test_without_users_returns_empty_list(self, user_api):
        users = user_api.get_many()
        assert users == []

    def test_without_filters_is_ok(self, user_api, unbound_user, distributor_user):
        users = user_api.get_many()
        assert {users[0].email, users[1].email} == {
            unbound_user.email,
            distributor_user.email,
        }

    def test_filter_by_email_is_ok(self, user_api, unbound_user, distributor_user):
        users = user_api.get_many(email=unbound_user.email)
        assert len(users) == 1, users
        assert users[0].email == unbound_user.email

    def test_pagination_is_ok(
        self,
        user_api,
        kc,
        unbound_user,
        distributor_admin,
        distributor_user,
    ):
        users = []
        users.extend(user_api.get_many(offset=0, limit=1))
        assert len(users) == 1, users

        users.extend(user_api.get_many(offset=1, limit=1))
        assert len(users) == 2

        users.extend(user_api.get_many(offset=2, limit=1))
        assert len(users) == 3, users

        expected = {unbound_user.email, distributor_admin.email, distributor_user.email}
        assert {users[0].email, users[1].email, users[2].email} == expected

    def test_users_without_email_are_skipped(
        self, user_api, unbound_user, distributor_user, kc, random_uuid
    ):
        user_id = kc.users.create(dict(username=str(random_uuid)))
        users = user_api.get_many()
        assert len(users) == 2
        assert uuid.UUID(user_id) not in (u.id for u in users)


class TestSetPassword:
    def test_if_user_exists_password_is_set(self, user_api, unbound_user):
        new_password = SecretStr("pass")
        user_api.set_password(unbound_user.id, password=new_password, temporary=False)
        unbound_user.password = new_password
        assert access_token_for(unbound_user)

    def test_if_user_not_exists_raises_user_not_found(self, user_api, random_uuid):
        with pytest.raises(UserNotFound):
            user_api.set_password(
                random_uuid, password=SecretStr("pass"), temporary=False
            )

    def test_temporary_password_is_set(self, user_api, unbound_user):
        new_password = SecretStr("pass")
        user_api.set_password(unbound_user.id, password=new_password, temporary=True)
        unbound_user.password = new_password
        user = user_api.ka.users.get(str(unbound_user.id))
        required_actions = user.get("requiredActions")
        assert "UPDATE_PASSWORD" in required_actions

    def test_password_policy_exception(self, user_api, unbound_user):
        realm = user_api.ka.realm.get()
        realm.update(passwordPolicy="length(8)")
        user_api.ka.realm.update(realm)

        with pytest.raises(iam.exc.BadPassword) as exc_info:
            user_api.set_password(unbound_user.id, SecretStr("pass"), temporary=False)
        assert BasePasswordPolicy.create("length", 8).description in str(exc_info.value)


class TestGetGroups:
    def test_groups_are_returned(self, kc, user_api, unbound_user):
        foo_id = kc.groups.create(dict(name="foo"))
        bar_id = kc.groups.create_child(foo_id, payload=dict(name="bar"))
        kc.users.create_group_membership(unbound_user.id, group_id=foo_id)
        kc.users.create_group_membership(unbound_user.id, group_id=bar_id)

        groups = user_api.get_groups(unbound_user.id)
        assert len(groups) == 2, groups
        assert {"foo", "bar"} == {g.name for g in groups}

    def test_group_properties_and_attributes(self, kc, user_api, unbound_user):
        attributes = {"one": ["two"]}
        foo_id = kc.groups.create(dict(name="foo", attributes=attributes))
        kc.users.create_group_membership(unbound_user.id, group_id=foo_id)

        groups = user_api.get_groups(unbound_user.id)
        assert groups[0].name == "foo"
        assert groups[0].path == "/foo"
        assert groups[0].attributes == attributes


class TestAddUserRoles:
    def test_roles_are_added_to_keycloak(self, user_api, roles_api, unbound_user):
        role_a = roles_api.add(schemas.Role(name="a"))
        role_b = roles_api.add(schemas.Role(name="b"))

        user_api.add_roles(unbound_user.id, [role_a.name, role_b.name])

        keycloak_roles = user_api.ka.users.get_realm_roles(str(unbound_user.id))
        assert {r["name"] for r in keycloak_roles} >= {role_a.name, role_b.name}

    def test_if_role_doesnt_exist_raises_role_not_found(
        self, user_api, unbound_user, random_uuid
    ):
        with pytest.raises(RoleNotFound):
            user_api.add_roles(unbound_user.id, [str(random_uuid)])

    def test_if_user_doesnt_exists_raises_user_not_found(
        self, user_api, roles_api, random_uuid
    ):
        roles_api.add(schemas.Role(name="boo"))
        with pytest.raises(UserNotFound):
            user_api.add_roles(random_uuid, ["boo"])


def test_user_from_keycloak(random_uuid):
    keycloak_user = keycloak.KeycloakUser.parse_obj(
        {
            "id": str(random_uuid),
            "username": "bob",
            "email": "<EMAIL>",
            "enabled": True,
            "emailVerified": False,
            "createdTimestamp": 0,
            "federatedIdentities": [
                {"identityProvider": "okta", "userId": "foo", "userName": "bar"},
                {"identityProvider": "github", "userId": "baz", "userName": "foo"},
            ],
        }
    )
    user = keycloak.from_keycloak(keycloak_user, User)
    assert user.email == keycloak_user.email
    assert user.enabled == keycloak_user.enabled
    assert user.created_timestamp == keycloak_user.created_timestamp
    assert len(user.federated_identities) == 2
    assert user.federated_identities[0].identity_provider == "okta"
    assert user.federated_identities[1].identity_provider == "github"


class TestGetUserCredentials:
    def test_if_user_exists_returns_credentials(self, user_api, unbound_user, kc):
        credential = {
            "id": str(uuid.uuid4()),
            "type": "password",
            "createdDate": 1700000000000,
            "userLabel": "login-password",
        }
        kc.users.get_user_credential = lambda user_id: [credential]

        credentials = user_api.get_user_credentials(unbound_user.id)
        assert isinstance(credentials, list)
        assert credentials[0].id == uuid.UUID(credential["id"])
        assert credentials[0].type == credential["type"]

    def test_if_user_not_exists_raises_user_not_found(self, user_api, random_uuid):
        with pytest.raises(UserNotFound):
            user_api.get_user_credentials(random_uuid)


class TestRemoveUserCredential:
    def test_if_user_exists_removes_credential(self, user_api, unbound_user, kc):
        credential_id = uuid.uuid4()

        called = {"removed": False}

        def mock_remove(user_id, cred_id):
            if user_id == str(unbound_user.id) and cred_id == str(credential_id):
                called["removed"] = True

        kc.users.remove_user_credential = mock_remove

        user_api.remove_user_credential(unbound_user.id, credential_id)
        assert called["removed"]

    def test_if_user_not_exists_raises_user_not_found(self, user_api, random_uuid):
        with pytest.raises(UserNotFound):
            user_api.remove_user_credential(random_uuid, uuid.uuid4())
