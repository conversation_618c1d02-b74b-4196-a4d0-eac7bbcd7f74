import uuid

import pytest

from app.adapters.iam import schemas
from app.adapters.iam.exc import (
    ClientAlreadyExists,
    ClientNotFound,
    ClientScopeNotFound,
    UserNotFound,
)
from app.adapters.iam.keycloak import KeycloakClientAPI
from app.adapters.iam.schemas import <PERSON><PERSON><PERSON><PERSON>, ServiceAccountUser


@pytest.fixture
def clients_api(kc) -> KeycloakClientAPI:
    return KeycloakClientAPI(kc)


@pytest.fixture
def client_in() -> schemas.ClientBase:
    return schemas.ClientBase(
        client_id=str(uuid.uuid4()), name=str(uuid.uuid4()), enabled=True
    )


@pytest.fixture
def existing_client(clients_api, client_in):
    return clients_api.add(client_in)


class TestAddClient:
    def test_client_created_in_keycloak(self, clients_api, client_in):
        clients_api.add(client_in)
        client_data = clients_api.ka.clients.get_by_client_id(client_in.client_id)
        assert (
            client_data.items()
            >= {
                "clientId": client_in.client_id,
                "name": client_in.name,
                "enabled": client_in.enabled,
                "publicClient": False,
                "standardFlowEnabled": False,
                "directAccessGrantsEnabled": False,
                "serviceAccountsEnabled": True,
                "fullScopeAllowed": False,
            }.items()
        )

    def test_client_secret_generated_in_keycloak(
        self,
        clients_api,
        client_in,
    ):
        clients_api.add(client_in)
        client_data = clients_api.ka.clients.get_by_client_id(client_in.client_id)
        secret = clients_api.ka.clients.get_secret(client_data["id"])
        assert isinstance(secret, dict)
        assert secret.get("value") is not None

    def test_if_client_id_exists_raises_error(self, clients_api, client_in):
        clients_api.add(client_in)
        with pytest.raises(ClientAlreadyExists) as exc_info:
            clients_api.add(client_in)
        assert str(exc_info.value) == client_in.client_id


class TestGetClient:
    def test_if_client_exists_it_is_returned(self, clients_api, existing_client):
        result = clients_api.get(existing_client.client_id)
        assert result == existing_client

    def test_if_client_doesnt_exist_returns_none(self, clients_api):
        result = clients_api.get(str(uuid.uuid4()))
        assert result is None


class TestUpdateClient:
    def test_if_client_exists_it_is_updated(self, clients_api, existing_client):
        update = ClientBase.parse_obj(existing_client.dict())
        update.enabled = not existing_client.enabled
        update.name = str(uuid.uuid4())
        updated = clients_api.update(update)
        assert updated.client_id == existing_client.client_id
        assert updated.name == update.name
        assert updated.enabled == update.enabled

    def test_if_client_doesnt_exist_raises_not_found(self, clients_api, client_in):
        with pytest.raises(ClientNotFound):
            clients_api.update(client_in)


class TestRemoveClient:
    def test_if_client_exists_it_is_removed(
        self,
        clients_api,
        existing_client,
    ):
        clients_api.remove(existing_client.client_id)
        result = clients_api.get(existing_client.client_id)
        assert result is None

    def test_if_client_doesnt_exist_raises_not_found(self, clients_api):
        with pytest.raises(ClientNotFound):
            clients_api.remove(str(uuid.uuid4()))


class TestAddDefaultClientScopes:
    def test_if_client_doesnt_exist_raises_client_not_found(self, clients_api):
        with pytest.raises(ClientNotFound):
            clients_api.add_default_client_scopes(str(uuid.uuid4()), ["foo"])

    def test_if_scope_doesnt_exist_raises_scope_not_found(
        self, clients_api, existing_client
    ):
        with pytest.raises(ClientScopeNotFound):
            clients_api.add_default_client_scopes(
                existing_client.client_id, scopes=[str(uuid.uuid4())]
            )

    def test_happy_path_is_ok(self, clients_api, existing_client, create_dummy_scope):
        scope_name = str(uuid.uuid4())
        create_dummy_scope(scope_name)
        clients_api.add_default_client_scopes(
            existing_client.client_id, scopes=[scope_name]
        )
        client = clients_api.get(existing_client.client_id)
        assert scope_name in client.default_client_scopes


class TestAddOptionalClientScopes:
    def test_if_client_doesnt_exist_raises_client_not_found(self, clients_api):
        with pytest.raises(ClientNotFound):
            clients_api.add_optional_client_scopes(str(uuid.uuid4()), ["foo"])

    def test_if_scope_doesnt_exist_raises_scope_not_found(
        self, clients_api, existing_client
    ):
        with pytest.raises(ClientScopeNotFound):
            clients_api.add_optional_client_scopes(
                existing_client.client_id, scopes=[str(uuid.uuid4())]
            )

    def test_happy_path_is_ok(self, clients_api, existing_client, create_dummy_scope):
        scope_name = str(uuid.uuid4())
        create_dummy_scope(scope_name)
        clients_api.add_optional_client_scopes(
            existing_client.client_id, scopes=[scope_name]
        )
        client = clients_api.get(existing_client.client_id)
        assert scope_name in client.optional_client_scopes


class TestGetServiceAccountUser:
    def test_if_client_doesnt_exist_raises_not_found(self, clients_api):
        with pytest.raises(ClientNotFound):
            clients_api.get_service_account_user(str(uuid.uuid4()))

    def test_service_account_user_returned(self, clients_api, existing_client):
        assert existing_client.service_accounts_enabled
        user = clients_api.get_service_account_user(existing_client.client_id)
        assert user is not None


class TestUpdateServiceAccountUser:
    def test_if_user_doesnt_exist_raises_user_not_found(self, clients_api):
        user = ServiceAccountUser(
            id=uuid.uuid4(),
            enabled=True,
            email_verified=True,
        )
        with pytest.raises(UserNotFound):
            clients_api.update_service_account_user(user)

    def test_if_user_exists_updated_successfully(self, clients_api, existing_client):
        user = clients_api.get_service_account_user(existing_client.client_id)
        update = user.copy(deep=True)
        update.enabled = not user.enabled
        update.email_verified = not user.email_verified
        update.attributes = {"dummy": ["bar"]}

        clients_api.update_service_account_user(update)

        assert existing_client.service_accounts_enabled
        user = clients_api.get_service_account_user(existing_client.client_id)
        assert user == update


class TestGetSecret:
    def test_if_client_doesnt_exist_raises_client_not_found(self, clients_api):
        with pytest.raises(ClientNotFound):
            clients_api.get_secret(str(uuid.uuid4()))

    def test_happy_path_is_ok(self, clients_api, existing_client):
        secret = clients_api.get_secret(existing_client.client_id)
        assert secret
