import pytest

from app.adapters.iam import keycloak, schemas
from app.adapters.iam.exc import RoleAlreadyExists, RoleNotFound


@pytest.fixture
def roles_api(kc) -> keycloak.KeycloakRoleAPI:
    return keycloak.KeycloakRoleAPI(kc)


class TestAddRole:
    @pytest.fixture
    def role_in(self, random_uuid) -> schemas.Role:
        return schemas.Role(
            name=str(random_uuid),
            description=str(random_uuid) + "desc",
            attributes=dict(type=["organization"]),
        )

    def test_role_created_in_keycloak(self, roles_api, role_in):
        roles_api.add(role_in)
        expected = dict(
            name=role_in.name,
            attributes=role_in.attributes,
            description=role_in.description,
        )
        role = roles_api.ka.realm_roles.get_by_name(role_in.name)
        assert role.items() >= expected.items()

    def test_returned_role_is_ok(self, roles_api, role_in):
        role = roles_api.add(role_in)
        assert role == role_in

    def test_if_role_with_name_exist_raises_role_already_exists(
        self, roles_api, role_in
    ):
        roles_api.add(role_in)
        with pytest.raises(RoleAlreadyExists):
            roles_api.add(role_in)


class TestGetRole:
    def test_existing_role_returned(self, roles_api):
        existing_role = roles_api.add(
            schemas.Role(name="foo", attributes={"bar": ["baz"]})
        )
        role = roles_api.get(existing_role.name)
        assert role == existing_role

    def test_if_role_doesnt_exists_none_returned(self, roles_api, random_uuid):
        role = roles_api.get(str(random_uuid))
        assert role is None


class TestUpdateRole:
    def test_role_updated_in_keycloak(self, roles_api):
        role = roles_api.add(schemas.Role(name="foo", attributes={"bar": ["baz"]}))
        role_in = schemas.Role(
            name="new name",
            description="new desc",
            attributes={"one": ["two"]},
        )
        roles_api.update(role.name, role_in)
        role_data = roles_api.ka.realm_roles.get_by_name(role_in.name)
        assert role_data.items() >= role_in.dict().items()

    def test_if_role_doesnt_exist_raises_role_not_found(self, roles_api, random_uuid):
        with pytest.raises(RoleNotFound):
            roles_api.update(str(random_uuid), schemas.Role(name="new"))


class TestRemoveRole:
    def test_existing_role_removed(self, roles_api):
        existing_role = roles_api.add(schemas.Role(name="bar"))

        roles_api.remove(existing_role.name)

        role = roles_api.get(existing_role.name)
        assert role is None

    def test_if_role_doesnt_exist_raises_role_not_found(self, roles_api, random_uuid):
        with pytest.raises(RoleNotFound):
            roles_api.remove(str(random_uuid))


class TestListRoles:
    def test_search_result(self, roles_api):
        role_a = roles_api.add(schemas.Role(name="A"))
        role_a_a = roles_api.add(schemas.Role(name="A a"))
        role_b = roles_api.add(schemas.Role(name="b"))

        result = roles_api.get_many(search="a")
        assert role_a in result
        assert role_a_a in result
        assert role_b not in result

    def test_pagination_is_ok(self, roles_api):
        for i in range(3):
            roles_api.add(schemas.Role(name=f"pagination-{i}"))

        roles = []
        roles.extend(roles_api.get_many(search="pagination", offset=0, limit=1))
        assert len(roles) == 1, roles

        roles.extend(roles_api.get_many(search="pagination", offset=1, limit=2))
        assert len(roles) == 3

        assert roles[0].name.startswith("pagination-")
        assert roles[1].name.startswith("pagination-")
        assert roles[2].name.startswith("pagination-")
