import uuid
from operator import attrgetter, itemgetter

import pytest

from app.adapters.iam import keycloak, schemas
from app.adapters.iam.exc import (
    ClientScopeAlreadyExists,
    ClientScopeNotFound,
    RoleNotFound,
)


@pytest.fixture
def client_scopes_api(kc) -> keycloak.KeycloakClientScopeAPI:
    return keycloak.KeycloakClientScopeAPI(kc)


@pytest.fixture
def roles_api(kc) -> keycloak.KeycloakRoleAPI:
    return keycloak.KeycloakRoleAPI(kc)


@pytest.fixture
def client_scope_in() -> schemas.ClientScope:
    mapper_0 = schemas.ProtocolMapper(
        name="Application ID",
        consent_required=False,
        type=str(schemas.ProtocolMapperType.USER_ATTRIBUTE.value),
        config={
            "userinfo.token.claim": "true",
            "user.attribute": "application_id",
            "id.token.claim": "false",
            "access.token.claim": "true",
            "claim.name": "application.id",
            "jsonType.label": "int",
        },
    )
    mapper_1 = schemas.ProtocolMapper(
        name="application-management audience",
        consent_required=False,
        type=str(schemas.ProtocolMapperType.AUDIENCE.value),
        config={
            "id.token.claim": "false",
            "access.token.claim": "true",
            "userinfo.token.claim": "false",
            "included.custom.audience": "application-management",
        },
    )
    return schemas.ClientScope(
        name="application-management-foo",
        description="Application management scope",
        attributes={
            "include.in.token.scope": "true",
            "display.on.consent.screen": "false",
        },
        protocol_mappers=list(
            sorted(
                [
                    mapper_0,
                    mapper_1,
                ],
                key=attrgetter("name"),
            )
        ),
    )


class TestAddClientScope:
    def test_client_scope_created_in_keycloak(self, client_scopes_api, client_scope_in):
        client_scopes_api.add(client_scope_in)
        scopes_in_keycloak = client_scopes_api.ka.client_scopes.get_many()
        scope_in_keycloak = next(
            (s for s in scopes_in_keycloak if s["name"] == client_scope_in.name), None
        )
        assert scope_in_keycloak is not None

        scope_in_keycloak.pop("id")
        keycloak_mappers = scope_in_keycloak.get("protocolMappers", [])
        for mapper in keycloak_mappers:
            mapper.pop("id")
        scope_in_keycloak["protocolMappers"] = list(
            sorted(keycloak_mappers, key=itemgetter("name"))
        )
        expected = dict(
            protocol="openid-connect",
            name=client_scope_in.name,
            description=client_scope_in.description,
            attributes=client_scope_in.attributes,
            protocolMappers=[
                dict(
                    protocol="openid-connect",
                    name=mapper.name,
                    protocolMapper=mapper.type,
                    consentRequired=mapper.consent_required,
                    config=mapper.config,
                )
                for mapper in client_scope_in.protocol_mappers
            ],
        )
        assert scope_in_keycloak == expected

    def test_returned_client_scope_is_ok(
        self,
        client_scopes_api,
        client_scope_in,
    ):
        created = client_scopes_api.add(client_scope_in)
        created.protocol_mappers = list(
            sorted(created.protocol_mappers, key=attrgetter("name"))
        )
        assert created == client_scope_in

    def test_if_client_scope_name_already_exists_raises_error(
        self, client_scopes_api, client_scope_in
    ):
        client_scopes_api.add(client_scope_in)
        with pytest.raises(ClientScopeAlreadyExists):
            client_scopes_api.add(client_scope_in)


class TestListClientScopes:
    @pytest.fixture()
    def client_scopes(self, client_scopes_api):
        return [
            client_scopes_api.add(schemas.ClientScope(name="a")),
            client_scopes_api.add(schemas.ClientScope(name="b")),
            client_scopes_api.add(schemas.ClientScope(name="c")),
        ]

    def test_list_of_client_scopes_returned(self, client_scopes_api, client_scopes):
        returned = client_scopes_api.get_many()
        expected = set(map(attrgetter("name"), client_scopes))
        assert set(map(attrgetter("name"), returned)) >= expected

    @pytest.mark.parametrize(
        "scope_names",
        [
            ["a", "b", "c"],
            ["a", "b"],
            ["a", "c"],
            ["b", "c"],
            ["c"],
        ],
    )
    def test_filter_by_names(self, client_scopes_api, client_scopes, scope_names):
        returned = client_scopes_api.get_many(scope_names)
        assert set(map(attrgetter("name"), returned)) == set(scope_names)

    def test_representation(self, client_scopes_api, client_scope_in):
        client_scope = client_scopes_api.add(client_scope_in)
        [returned] = client_scopes_api.get_many([client_scope.name])
        assert returned == client_scope


class TestRemoveClientScope:
    def test_client_scope_exists_it_is_removed_from_keycloak(
        self, client_scopes_api, client_scope_in
    ):
        scope = client_scopes_api.add(client_scope_in)
        client_scopes_api.remove(scope.name)
        assert client_scopes_api.get_by_name(scope.name) is None

    def test_if_client_scope_doesnt_exist_raises_client_scope_not_found(
        self, client_scopes_api
    ):
        name = str(uuid.uuid4())
        with pytest.raises(ClientScopeNotFound) as exc_info:
            client_scopes_api.remove(name)

        assert str(exc_info.value) == name


class TestAddClientScopeRoles:
    @pytest.fixture
    def client_scope(self, client_scopes_api, client_scope_in) -> schemas.ClientScope:
        return client_scopes_api.add(client_scope_in)

    def test_roles_are_added_to_keycloak(
        self, client_scopes_api, roles_api, client_scope
    ):
        role_a = roles_api.add(schemas.Role(name="a"))
        role_b = roles_api.add(schemas.Role(name="b"))

        client_scopes_api.add_roles(client_scope.name, [role_a.name, role_b.name])
        scope_id = client_scopes_api._get_id_by_name(client_scope.name)
        ka = client_scopes_api.ka
        keycloak_roles = ka.client_scopes.get_realm_level_roles(scope_id)
        assert set(map(itemgetter("name"), keycloak_roles)) == {"a", "b"}

    def test_if_client_scope_doesnt_exist_raises_scope_not_found(
        self, client_scopes_api, roles_api
    ):
        role_a = roles_api.add(schemas.Role(name="a"))
        with pytest.raises(ClientScopeNotFound):
            client_scopes_api.add_roles(str(uuid.uuid4()), [role_a.name])

    def test_if_role_doesnt_exist_raises_role_not_found(
        self, client_scopes_api, client_scope
    ):
        with pytest.raises(RoleNotFound):
            client_scopes_api.add_roles(client_scope.name, [str(uuid.uuid4())])


class TestGetClientScopesRoles:
    def test_roles_name_are_returned(
        self, client_scopes_api, client_scope_in, roles_api
    ):
        role_a = roles_api.add(schemas.Role(name="a"))
        role_b = roles_api.add(schemas.Role(name="b"))
        scope = client_scopes_api.add(client_scope_in)
        client_scopes_api.add_roles(scope.name, [role_a.name, role_b.name])

        roles = client_scopes_api.get_roles(scope.name)
        assert set(roles) == {role_a.name, role_b.name}
