import pytest

from app.adapters.iam import keycloak, schemas
from app.adapters.iam.exc import GroupNotFound


@pytest.fixture
def groups_api(kc) -> keycloak.KeycloakGroupAPI:
    return keycloak.KeycloakGroupAPI(kc)


class TestAddGroup:
    @pytest.fixture
    def group_in(self, random_uuid) -> schemas.GroupBase:
        return schemas.GroupBase(
            name=str(random_uuid), attributes=dict(type=["organization"])
        )

    def test_group_created_in_keycloak(self, groups_api, group_in):
        groups_api.add(group_in, parent_path=None)
        expected = dict(
            name=group_in.name,
            attributes=group_in.attributes,
        )
        groups = groups_api.ka.groups.get_many(brief_representation=False)
        assert groups[0].items() >= expected.items()

    def test_returned_group_is_ok(self, groups_api, group_in):
        group = groups_api.add(group_in, parent_path=None)
        assert group.name == group_in.name
        assert group.path == f"/{group_in.name}"
        assert group.attributes == group_in.attributes

    def test_if_parent_group_does_not_exist_raises_group_not_found(
        self, groups_api, group_in, random_uuid
    ):
        with pytest.raises(GroupNotFound):
            groups_api.add(group_in, parent_path=f"/{random_uuid}")

    def test_child_group_created_under_parent(self, groups_api, group_in):
        groups_api.ka.groups.create(dict(name="bar"))
        group = groups_api.add(group_in, "/bar")
        assert group.path == f"/bar/{group_in.name}"


class TestUpdateGroup:
    @pytest.fixture()
    def existing_group(self, groups_api, random_uuid):
        group = groups_api.add(
            group=schemas.GroupBase(
                name=str(random_uuid),
                attributes=dict(id=[str(random_uuid)]),
            ),
            parent_path=None,
        )
        return group

    def test_group_attributes_updated(self, groups_api, existing_group, random_uuid):
        group_in = schemas.GroupBase(
            name="new name", attributes=dict(bar=[str(random_uuid)])
        )
        updated = groups_api.update(existing_group.path, group_in)
        assert updated.attributes == group_in.attributes

    def test_if_group_name_changed_path_updated(self, groups_api, existing_group):
        group_in = schemas.GroupBase(name="new name")
        updated = groups_api.update(existing_group.path, group_in)
        assert updated.path == existing_group.path.replace(
            existing_group.name, group_in.name
        )

    def test_if_parent_group_name_changed_child_path_updated(self, groups_api):
        parent = groups_api.add(schemas.GroupBase(name="foo"), None)
        groups_api.add(schemas.GroupBase(name="bar"), parent.path)
        groups_api.update(parent.path, schemas.GroupBase(name="new name"))
        assert groups_api.get("/new name/bar") is not None


class TestRemoveGroup:
    def test_if_group_exists_group_removed_from_keycloak(self, groups_api):
        group = groups_api.add(schemas.GroupBase(name="foo"), None)
        groups_api.remove(group.path)
        assert groups_api.ka.groups.get_many() == []

    def test_if_group_doesnt_exist_raises_group_not_found(
        self, groups_api, random_uuid
    ):
        with pytest.raises(GroupNotFound):
            groups_api.remove(f"/{random_uuid}")


class TestAddMember:
    def test_if_group_not_exists_raises_group_not_found(self, groups_api, unbound_user):
        with pytest.raises(GroupNotFound):
            groups_api.add_member("/no existing/path", unbound_user.id)

    def test_if_group_exist_membership_created(self, groups_api, unbound_user):
        group = groups_api.add(schemas.GroupBase(name="foo"), None)
        groups_api.add_member(group.path, unbound_user.id)
        user_groups = groups_api.ka.users.get_groups(str(unbound_user.id))
        assert user_groups[0]["path"] == group.path

    def test_if_membership_already_exists_is_ok(self, groups_api, unbound_user):
        group = groups_api.add(schemas.GroupBase(name="foo"), None)
        groups_api.add_member(group.path, unbound_user.id)
        groups_api.add_member(group.path, unbound_user.id)


class TestRemoveMember:
    def test_if_group_doesnt_exists_raises_group_not_found(
        self, groups_api, unbound_user
    ):
        with pytest.raises(GroupNotFound):
            groups_api.remove_member("/not existent/path", unbound_user.id)

    def test_if_membership_exist_user_removed_from_group(
        self, groups_api, unbound_user
    ):
        group = groups_api.add(schemas.GroupBase(name="foo"), None)
        groups_api.add_member(group.path, unbound_user.id)

        groups_api.remove_member(group.path, unbound_user.id)
        user_groups = groups_api.ka.users.get_groups(str(unbound_user.id))
        assert not user_groups


class TestListGroups:
    def test_list_of_groups_returned(self, groups_api):
        parent = groups_api.add(schemas.GroupBase(name="parent"), None)
        child = groups_api.add(schemas.GroupBase(name="child"), parent.path)

        groups = groups_api.get_many()
        assert len(groups) == 2, groups
        assert {groups[0].name, groups[1].name} == {parent.name, child.name}
