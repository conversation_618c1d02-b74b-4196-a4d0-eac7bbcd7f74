import pytest

from app.adapters.iam.keycloak import KeycloakPasswordPolicyAPI
from app.adapters.iam.schemas import PasswordPolicy


@pytest.fixture
def password_policy_api(kc) -> KeycloakPasswordPolicyAPI:
    return KeycloakPasswordPolicyAPI(kc)


@pytest.mark.parametrize(
    "policy_string,policies",
    [
        ("", []),
        (
            "regexPattern(^((?!RE:).))",
            [PasswordPolicy(id="regexPattern", value="^((?!RE:).)")],
        ),
        ("length(8)", [PasswordPolicy(id="length", value=8)]),
        (
            "hashIterations(27500) and "
            "passwordHistory(3) and "
            "notUsername(undefined)",
            [
                PasswordPolicy(id="hashIterations", value=27500),
                PasswordPolicy(id="passwordHistory", value=3),
                PasswordPolicy(id="notUsername", value=None),
            ],
        ),
    ],
)
class TestPasswordPolicies:
    def test_get_current_result(self, password_policy_api, kc, policy_string, policies):
        realm_data = kc.realm.get()
        realm_data["passwordPolicy"] = policy_string
        kc.realm.update(realm_data)
        assert password_policy_api.get_current() == policies

    def test_configure_updates_policy_in_keycloak(
        self, kc, password_policy_api, policy_string, policies
    ):
        realm = kc.realm.get()
        realm.update(passwordPolicy="")
        kc.realm.update(realm)
        password_policy_api.configure(policies)
        assert kc.realm.get().get("passwordPolicy", "") == policy_string
