import httpx
import pytest
import respx
from fastapi.encoders import jsonable_encoder
from platform_api_client import PlatformAPIClient
from starlette import status

from app.adapters.platform_api_client.audit_service import AuditAPI
from app.core.config import settings
from tests.test_factories import Audit, AuditLogResponseModel

router = respx.mock(assert_all_called=False, assert_all_mocked=True)
router.post(
    url=settings.AUDIT_LOG_URI + AuditAPI.ADD_AUDIT,
    name="audit",
)


@pytest.fixture
def api_client():
    mock_transport = httpx.MockTransport(router.handler)
    with httpx.Client(transport=mock_transport) as client:
        yield client


@pytest.fixture
def audit_api(api_client) -> AuditAPI:
    client = PlatformAPIClient(transport=api_client)
    return AuditAPI(client, settings.AUDIT_LOG_URI)


class TestAuditAPI:
    @router
    def test_add_audit(self, audit_api, create_audit):
        mock_message = create_audit
        audit = Audit()
        router["audit"].return_value = httpx.Response(
            status_code=status.HTTP_201_CREATED,
            json=jsonable_encoder(audit),
        )
        audit = AuditLogResponseModel(id=None, status=None, error=None, message=None)
        result = audit_api.add_audit(mock_message)
        assert result == audit
