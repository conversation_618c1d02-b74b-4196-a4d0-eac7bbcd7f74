from unittest.mock import MagicMock
from uuid import uuid4

import pytest
from platform_api_client import PlatformAPIClient

from app.adapters.platform_api_client.audit_service import AbstractAuditAPI
from app.api import deps
from app.enums import OrganizationRoleEnum, OrganizationTypeEnum
from app.schemas import ServiceInfo, UserInfo


@pytest.mark.parametrize(
    ("payload", "actor_type"),
    [
        (dict(client_id="foo", email_verified=True, typ="Bearer"), ServiceInfo),
        (
            dict(
                sub=str(uuid4()),
                username="bob",
                email="<EMAIL>",
                email_verified=True,
                typ="Bearer",
                organization=dict(id=1, type=OrganizationTypeEnum.CLIENT),
                realm_access=dict(roles=[OrganizationRoleEnum.CLIENT_ADMIN]),
            ),
            UserInfo,
        ),
    ],
)
def test_get_actor(payload, actor_type):
    result = deps.get_actor(payload)
    assert type(result) is actor_type


def test_get_external_user_client():
    token = "TOKEN"
    client = next(deps.get_external_user_client(token))
    assert isinstance(client, PlatformAPIClient)
    assert client.headers["authorization"] == f"Bearer {token}"


def test_get_audit_api():
    client = MagicMock(spec=PlatformAPIClient)
    api = deps.get_audit_service(client=client)
    assert isinstance(api, AbstractAuditAPI)
