{"id": "32df14aa-0973-4f3b-9bcb-0bfeb39b3521", "name": "CP_Env_DA", "values": [{"key": "TokenUrl", "value": "https://sso.test.connectedplatform.net/auth/realms/connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "default", "enabled": true}, {"key": "Client_secret", "value": "3a2342ac-0798-4518-9ece-268267d0b15f", "type": "default", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "default", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "Password", "value": "Tempo##1234", "type": "default", "enabled": true}, {"key": "Token", "value": "", "type": "any", "enabled": true}, {"key": "BaseUrl", "value": "https://test.connectedplatform.net", "type": "default", "enabled": true}, {"key": "Developer_Name", "value": "Nextgen Clearing", "type": "any", "enabled": true}, {"key": "Categories", "value": "", "type": "any", "enabled": true}, {"key": "Developer_ID", "value": "", "type": "any", "enabled": true}, {"key": "Categories_ID", "value": "", "type": "any", "enabled": true}, {"key": "Components_Name", "value": "", "type": "any", "enabled": true}, {"key": "Components_ID", "value": "", "type": "any", "enabled": true}, {"key": "Application_ID", "value": "", "type": "any", "enabled": true}, {"key": "Description", "value": "", "type": "any", "enabled": true}, {"key": "Benefits", "value": "", "type": "any", "enabled": true}, {"key": "Org_ID", "value": "1", "type": "any", "enabled": true}, {"key": "Org_Type", "value": "DISTRIBUTOR", "type": "any", "enabled": true}, {"key": "Orgclient_id", "value": "", "type": "any", "enabled": true}, {"key": "User_ID", "value": "", "type": "any", "enabled": true}, {"key": "Email_static", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "role_Name", "value": "DistributorAdmin", "type": "any", "enabled": true}, {"key": "FirstName", "value": "", "type": "any", "enabled": true}, {"key": "LastName", "value": "", "type": "any", "enabled": true}, {"key": "pmns1", "value": "", "type": "any", "enabled": true}, {"key": "pmns2", "value": "", "type": "any", "enabled": true}, {"key": "domain1", "value": "", "type": "any", "enabled": true}, {"key": "domain2", "value": "", "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "", "type": "any", "enabled": true}, {"key": "fromDate", "value": "", "type": "any", "enabled": true}, {"key": "toDate", "value": "", "type": "any", "enabled": true}, {"key": "notification_id", "value": "", "type": "any", "enabled": true}, {"key": "<PERSON><PERSON>", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "sendnotificationuser", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "requestTimeout", "value": "", "type": "any", "enabled": true}, {"key": "UplaodFile", "value": "", "type": "any", "enabled": true}, {"key": "error_message", "value": "", "type": "any", "enabled": true}, {"key": "company_name", "value": "", "type": "any", "enabled": true}, {"key": "client_name", "value": "", "type": "any", "enabled": true}, {"key": "client_id", "value": "", "type": "any", "enabled": true}, {"key": "Email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "role", "value": "ClientAdmin", "type": "any", "enabled": true}, {"key": "mozmail", "value": "mozmail.com", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-04-21T10:13:44.681Z", "_postman_exported_using": "Postman/11.41.1"}