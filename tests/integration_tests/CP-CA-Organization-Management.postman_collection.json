{"info": {"_postman_id": "37918d3c-d185-44aa-a113-5bea437a7b72", "name": "CP-CA-Organization-Management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "24199272"}, "item": [{"name": "Authontication Token", "item": [{"name": "Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", " pm.test('Received access_token', function(){\r", "    const data = pm.response.json();\r", "    pm.expect(data).to.haveOwnProperty('access_token');\r", "   pm.environment.set('Token', data['access_token']);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}]}, {"name": "Organizations", "item": [{"name": "Cerate Client (Organization) Copy", "item": [{"name": "Token -", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", " pm.test('Received access_token', function(){\r", "    const data = pm.response.json();\r", "    pm.expect(data).to.haveOwnProperty('access_token');\r", "   pm.environment.set('Token', data['access_token']);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Organizaion - 200 OK - 2", "event": [{"listen": "test", "script": {"exec": ["// Check if the status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Parse the response body as JSON\r", "// I have passing hard coded if we need please change it\r", "let response = pm.response.json();\r", "// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", "\r", "\r", "// const environment = {};\r", "// const data = [\r", "//     {\r", "//         name: \"Nextgen Clearing\",\r", "//         id: 1,\r", "//         parent_id: null,\r", "//         type: \"DISTRIBUTOR\"\r", "//     }\r", "// ];\r", "\r", "const nextgen = response.find(item => item.name === \"Customer A\");\r", "\r", "if (nextgen) {\r", "pm.environment.set(\"Org_ID\", \"21\");\r", "pm.environment.set(\"Org_Type\", \"CLIENT\");\r", "    console.log(environment);\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/?org_id={{Org_ID}}&org_type={{Org_Type}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", ""], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "org_type", "value": "{{Org_Type}}"}]}}, "response": []}, {"name": "Create Organization - 201 Created Valid case", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", "\r", "// var jsonData = JSON.parse(responseBody);\r", "// pm.environment.set(\"Orgclient_id\", jsonData.id);\r", "\r", "\r", "// pm.test(\"Status code name should be Created\", function () {\r", "//     pm.response.to.have.status(\"Created\");\r", "// });\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// Helper function to generate a random 5-character uppercase alphanumeric string\r", "function generatePMN() {\r", "    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < 5; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate 2 PMNs\r", "const pmns1 = generatePMN();\r", "const pmns2 = generatePMN();\r", "\r", "// Generate 2 random domains (lowercase)\r", "const domain1 = `${Math.random().toString(36).substring(2, 8)}.com`;\r", "const domain2 = `${Math.random().toString(36).substring(2, 8)}.com`;\r", "\r", "// Set them as environment variables (no stringify needed)\r", "pm.environment.set(\"pmns1\", pmns1);\r", "pm.environment.set(\"pmns2\", pmns2);\r", "pm.environment.set(\"domain1\", domain1);\r", "pm.environment.set(\"domain2\", domain2);\r", "\r", "// // Log to console for debugging (optional)\r", "// console.log(\"PMNs:\", pmns1, pmns2);\r", "// console.log(\"Domains:\", domain1, domain2);\r", "\r", "// Generate a random company name\r", "let company_name = pm.variables.replaceIn('{{$randomCompanyName}}');\r", "\r", "// Store the generated name in the environment variable\r", "pm.environment.set('client_name', company_name);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{client_name}}\",\r\n  \"allowed_domains\": [\"{{mozmail}}\",\"{{domain2}}\"],\r\n  \"pmn_codes\": [\"{{pmns1}}\",\"{{pmns2}}\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/clients", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "clients"]}}, "response": []}, {"name": "Get Organizaion - 200 OK - 2 <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["// Check if the status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Parse the response body\r", "let response = pm.response.json();\r", "\r", "// Retrieve the stored client_name from the environment\r", "let clientNameToSearch = pm.environment.get('client_name');\r", "\r", "// Search for the client in the response\r", "let client = response.find(function(item) {\r", "    return item.name === clientNameToSearch;\r", "});\r", "\r", "// If client is found, set the id in the environment variable\r", "if (client) {\r", "    pm.environment.set('client_id', client.id);\r", "    console.log('Client ID set to environment variable:', client.id);\r", "} else {\r", "    console.log('Client name not found in the response:', clientNameToSearch);\r", "}\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations"]}}, "response": []}, {"name": "Delete Client - 204 No content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "// pm.test(\"Status code name has string\", function () {\r", "//     pm.response.to.have.status(\"No Content\");\r", "// });"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{client_id}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{client_id}}"]}}, "response": []}]}, {"name": "Update Client (Organization) Copy", "item": [{"name": "Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", " pm.test('Received access_token', function(){\r", "    const data = pm.response.json();\r", "    pm.expect(data).to.haveOwnProperty('access_token');\r", "   pm.environment.set('Token', data['access_token']);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Organizaion - 200 OK - 2", "event": [{"listen": "test", "script": {"exec": ["// Check if the status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Parse the response body as JSON\r", "// I have passing hard coded if we need please change it\r", "let response = pm.response.json();\r", "// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", "\r", "\r", "// const environment = {};\r", "// const data = [\r", "//     {\r", "//         name: \"Nextgen Clearing\",\r", "//         id: 1,\r", "//         parent_id: null,\r", "//         type: \"DISTRIBUTOR\"\r", "//     }\r", "// ];\r", "\r", "const nextgen = response.find(item => item.name === \"Customer A\");\r", "\r", "if (nextgen) {\r", "pm.environment.set(\"Org_ID\", \"21\");\r", "pm.environment.set(\"Org_Type\", \"CLIENT\");\r", "    console.log(environment);\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/?org_id={{Org_ID}}&org_type={{Org_Type}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", ""], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "org_type", "value": "{{Org_Type}}"}]}}, "response": []}, {"name": "Create Organization - 201 Created Valid case", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"Orgclient_id\", jsonData.id);\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// Helper function to generate a random 5-character uppercase alphanumeric string\r", "function generatePMN() {\r", "    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < 5; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate 2 PMNs\r", "const pmns1 = generatePMN();\r", "const pmns2 = generatePMN();\r", "\r", "// Generate 2 random domains (lowercase)\r", "const domain1 = `${Math.random().toString(36).substring(2, 8)}.com`;\r", "const domain2 = `${Math.random().toString(36).substring(2, 8)}.com`;\r", "\r", "// Set them as environment variables (no stringify needed)\r", "pm.environment.set(\"pmns1\", pmns1);\r", "pm.environment.set(\"pmns2\", pmns2);\r", "pm.environment.set(\"domain1\", domain1);\r", "pm.environment.set(\"domain2\", domain2);\r", "\r", "// // Log to console for debugging (optional)\r", "// console.log(\"PMNs:\", pmns1, pmns2);\r", "// console.log(\"Domains:\", domain1, domain2);\r", "\r", "// Generate a random company name\r", "let company_name = pm.variables.replaceIn('{{$randomCompanyName}}');\r", "\r", "// Store the generated name in the environment variable\r", "pm.environment.set('client_name', company_name);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{client_name}}\",\r\n  \"allowed_domains\": [\"{{mozmail}}\",\"{{domain2}}\"],\r\n  \"pmn_codes\": [\"{{pmns1}}\",\"{{pmns2}}\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/clients", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "clients"]}}, "response": []}, {"name": "Get Organizaion - 200 OK - 2 <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["// Check if the status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Parse the response body\r", "let response = pm.response.json();\r", "\r", "// Retrieve the stored client_name from the environment\r", "let clientNameToSearch = pm.environment.get('client_name');\r", "\r", "// Search for the client in the response\r", "let client = response.find(function(item) {\r", "    return item.name === clientNameToSearch;\r", "});\r", "\r", "// If client is found, set the id in the environment variable\r", "if (client) {\r", "    pm.environment.set('client_id', client.id);\r", "    console.log('Client ID set to environment variable:', client.id);\r", "} else {\r", "    console.log('Client name not found in the response:', clientNameToSearch);\r", "}\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations"]}}, "response": []}, {"name": "Create Organization - 201 Created Valid case Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", "// var jsonData = JSON.parse(responseBody);\r", "// pm.environment.set(\"Orgclient_id\", jsonData.id);\r", "\r", "\r", "// pm.test(\"Status code name should be Created\", function () {\r", "//     pm.response.to.have.status(\"Created\");\r", "// });\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// Helper function to generate a random 5-character uppercase alphanumeric string\r", "function generatePMN() {\r", "    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r", "    let result = '';\r", "    for (let i = 0; i < 5; i++) {\r", "        result += chars.charAt(Math.floor(Math.random() * chars.length));\r", "    }\r", "    return result;\r", "}\r", "\r", "// Generate 2 PMNs\r", "const pmns1 = generatePMN();\r", "const pmns2 = generatePMN();\r", "\r", "// Generate 2 random domains (lowercase)\r", "const domain1 = `${Math.random().toString(36).substring(2, 8)}.com`;\r", "const domain2 = `${Math.random().toString(36).substring(2, 8)}.com`;\r", "\r", "// Set them as environment variables (no stringify needed)\r", "pm.environment.set(\"pmns1\", pmns1);\r", "pm.environment.set(\"pmns2\", pmns2);\r", "pm.environment.set(\"domain1\", domain1);\r", "pm.environment.set(\"domain2\", domain2);\r", "\r", "// // Log to console for debugging (optional)\r", "// console.log(\"PMNs:\", pmns1, pmns2);\r", "// console.log(\"Domains:\", domain1, domain2);\r", "\r", "// Generate a random company name\r", "let company_name = pm.variables.replaceIn('{{$randomCompanyName}}');\r", "\r", "// Store the generated name in the environment variable\r", "pm.environment.set('client_name', company_name);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{client_name}}\",\r\n  \"allowed_domains\": [\"{{mozmail}}\",\"{{domain2}}\"],\r\n  \"pmn_codes\": [\"{{pmns1}}\",\"{{pmns2}}\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/v1/organizations/clients/{{client_id}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "clients", "{{client_id}}"]}}, "response": []}]}, {"name": "Read Organization users Copy", "item": [{"name": "Token - 2", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", " pm.test('Received access_token', function(){\r", "    const data = pm.response.json();\r", "    pm.expect(data).to.haveOwnProperty('access_token');\r", "   pm.environment.set('Token', data['access_token']);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Organizaion - 200 OK - 3", "event": [{"listen": "test", "script": {"exec": ["// Check if the status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Parse the response body as JSON\r", "// I have passing hard coded if we need please change it\r", "let response = pm.response.json();\r", "// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", "\r", "\r", "// const environment = {};\r", "// const data = [\r", "//     {\r", "//         name: \"Nextgen Clearing\",\r", "//         id: 1,\r", "//         parent_id: null,\r", "//         type: \"DISTRIBUTOR\"\r", "//     }\r", "// ];\r", "\r", "const nextgen = response.find(item => item.name === \"Nextgen Clearing\");\r", "\r", "if (nextgen) {\r", "pm.environment.set(\"Org_ID\", \"1\");\r", "pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", "    console.log(environment);\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/?org_id={{Org_ID}}&org_type={{Org_Type}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", ""], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "org_type", "value": "{{Org_Type}}"}]}}, "response": []}, {"name": "Read organization users - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}, {"name": "Read organization users   - Invalid Url -", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 404', function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizationsdsds/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizationsdsds", "{{Org_ID}}", "users"]}}, "response": []}, {"name": "Read organization users  - Verified Error response", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", "\r", "// Verify 405 Method Not Allowed response body\r", "pm.test(\"Verify error response format\", function () {\r", "    let jsonData = pm.response.json();\r", "    pm.expect(jsonData).to.have.property(\"detail\", \"Not Found\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/userssdds", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "userssdds"]}}, "response": []}, {"name": "Read organization users - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", "\r", "// Verify 405 Method Not Allowed response body\r", "pm.test(\"Verify error response format\", function () {\r", "    let jsonData = pm.response.json();\r", "    pm.expect(jsonData).to.have.property(\"detail\", \"Method Not Allowed\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}, {"name": "Read organization users   - Verified Response -", "event": [{"listen": "test", "script": {"exec": ["// pm.test(\"Status code is 200\", function () {\r", "//     pm.response.to.have.status(200);\r", "// });\r", "\r", "// pm.test(\"Response is an array and not empty\", function () {\r", "//     let jsonData = pm.response.json().results;\r", "//     pm.expect(jsonData).to.be.an(\"array\").that.is.not.empty;\r", "// });\r", "\r", "// pm.test(\"Each item contains required fields\", function () {\r", "//     let jsonData = pm.response.json().results;\r", "\r", "//     jsonData.forEach(item => {\r", "//         pm.expect(item).to.have.property(\"id\").that.is.a(\"string\").and.not.empty;\r", "//         pm.expect(item).to.have.property(\"enabled\").that.is.a(\"boolean\");\r", "//         pm.expect(item).to.have.property(\"emailVerified\").that.is.a(\"boolean\");\r", "//         pm.expect(item).to.have.property(\"createdTimestamp\").that.is.a(\"number\");\r", "//         pm.expect(item).to.have.property(\"email\").that.is.a(\"string\").and.not.empty;\r", "//         pm.expect(item).to.have.property(\"username\").that.is.a(\"string\").and.not.empty;\r", "//         pm.expect(item).to.have.property(\"firstName\").that.is.a(\"string\").and.not.empty;\r", "//         pm.expect(item).to.have.property(\"lastName\").that.is.a(\"string\").and.not.empty;\r", "//         pm.expect(item).to.have.property(\"roles\").that.is.an(\"array\");\r", "//         pm.expect(item).to.have.property(\"lastLogin\").that.is.a(\"string\").and.not.empty;\r", "//         pm.expect(item).to.have.property(\"createdBy\").that.is.a(\"string\").and.not.empty;\r", "//         pm.expect(item).to.have.property(\"createdAt\").that.is.a(\"string\").and.not.empty;\r", "//         pm.expect(item).to.have.property(\"organizationName\").that.is.a(\"string\").and.not.empty;\r", "        \r", "//         // Check identity<PERSON><PERSON>ider if not null\r", "//         if (item.identityProvider !== null) {\r", "//             pm.expect(item.identityProvider).to.have.property(\"alias\").that.is.a(\"string\").and.not.empty;\r", "//             pm.expect(item.identityProvider).to.have.property(\"displayName\").that.is.a(\"string\").and.not.empty;\r", "//         }\r", "//     });\r", "// });\r", "\r", "pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}, {"name": "Read organization users - Verified Response Time -", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is within limit\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}, {"name": "Read organization users - Verified Status code and name -", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Successful POST request\", function () {\r", "    pm.expect(pm.response.code).to.be.oneOf([404]);\r", "});\r", "\r", "pm.test(\"Status code name has string\", function () {\r", "    pm.response.to.have.status(\"Not Found\");\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}, {"name": "Read organization users - Invalid <PERSON> -", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "Invalid", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}, {"name": "Read organization users- <PERSON>lank <PERSON> -", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}, {"name": "Read organization users - Without Token -", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}]}, {"name": "Get organization Descendants Copy", "item": [{"name": "Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", " pm.test('Received access_token', function(){\r", "    const data = pm.response.json();\r", "    pm.expect(data).to.haveOwnProperty('access_token');\r", "   pm.environment.set('Token', data['access_token']);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Organizaion - 200 OK -", "event": [{"listen": "test", "script": {"exec": ["// Check if the status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Parse the response body as JSON\r", "// I have passing hard coded if we need please change it\r", "let response = pm.response.json();\r", "pm.environment.set(\"Org_ID\", \"21\");\r", "pm.environment.set(\"Org_Type\", \"CLIENT\");\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/?org_id={{Org_ID}}&org_type={{Org_Type}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", ""], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "org_type", "value": "{{Org_Type}}"}]}}, "response": []}, {"name": "Get organization descendants - 200 OK Distributors Des-Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/descendants?org_id={{Org_ID}}&descendant_type={{Org_Type}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "descendants"], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "descendant_type", "value": "{{Org_Type}}"}]}}, "response": []}, {"name": "Get organization descendants - 200 OK Client", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/descendants?org_id={{Org_ID}}&descendant_type=CLIENT", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "descendants"], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "descendant_type", "value": "CLIENT"}]}}, "response": []}, {"name": "Get organization descendants - 200 OK PARTNER", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/descendants?org_id={{Org_ID}}&descendant_type=PARTNER", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "descendants"], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "descendant_type", "value": "PARTNER"}]}}, "response": []}, {"name": "Get organization descendants - all 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/descendants", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "descendants"]}}, "response": []}]}, {"name": "Read Organization user by id Copy", "item": [{"name": "Token - 2", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", " pm.test('Received access_token', function(){\r", "    const data = pm.response.json();\r", "    pm.expect(data).to.haveOwnProperty('access_token');\r", "   pm.environment.set('Token', data['access_token']);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Organizaion - 200 OK - 3", "event": [{"listen": "test", "script": {"exec": ["// Check if the status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Parse the response body as JSON\r", "// I have passing hard coded if we need please change it\r", "let response = pm.response.json();\r", "// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", "\r", "\r", "// const environment = {};\r", "// const data = [\r", "//     {\r", "//         name: \"Nextgen Clearing\",\r", "//         id: 1,\r", "//         parent_id: null,\r", "//         type: \"DISTRIBUTOR\"\r", "//     }\r", "// ];\r", "\r", "const nextgen = response.find(item => item.name === \"Nextgen Clearing\");\r", "\r", "if (nextgen) {\r", "pm.environment.set(\"Org_ID\", \"1\");\r", "pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", "    console.log(environment);\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/?org_id={{Org_ID}}&org_type={{Org_Type}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", ""], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "org_type", "value": "{{Org_Type}}"}]}}, "response": []}, {"name": "get roles - 200 OK Copy 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users/roles", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users", "roles"]}}, "response": []}, {"name": "Create User - 201 Created", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"User_ID\", jsonData.id);\r", "\r", "\r", "pm.test(\"Status code name should be Created\", function () {\r", "    pm.response.to.have.status(\"Created\");\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"email\": \"{{$randomEmail}}\",\r\n  \"firstName\": \"{{$randomFirstName}}\",\r\n  \"lastName\": \"{{$randomLastName}}\",\r\n  \"role\": \"ClientAdmin\",\r\n  \"identityProvider\": null,\r\n  \"enabled\": true,\r\n  \"emailVerified\": true,\r\n  \"username\": \"{{$randomEmail}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}, {"name": "Read Organization user by id - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users/{{User_ID}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users", "{{User_ID}}"]}}, "response": []}]}, {"name": "Create Org Users Copy", "item": [{"name": "Token - 2 Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", " pm.test('Received access_token', function(){\r", "    const data = pm.response.json();\r", "    pm.expect(data).to.haveOwnProperty('access_token');\r", "   pm.environment.set('Token', data['access_token']);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Organizaion - 200 OK - 2 <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["// Check if the status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Parse the response body as JSON\r", "// I have passing hard coded if we need please change it\r", "let response = pm.response.json();\r", "// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", "\r", "\r", "// const environment = {};\r", "// const data = [\r", "//     {\r", "//         name: \"Nextgen Clearing\",\r", "//         id: 1,\r", "//         parent_id: null,\r", "//         type: \"DISTRIBUTOR\"\r", "//     }\r", "// ];\r", "\r", "const nextgen = response.find(item => item.name === \"Nextgen Clearing\");\r", "\r", "if (nextgen) {\r", "pm.environment.set(\"Org_ID\", \"1\");\r", "pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", "    console.log(environment);\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/?org_id={{Org_ID}}&org_type={{Org_Type}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", ""], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "org_type", "value": "{{Org_Type}}"}]}}, "response": []}, {"name": "get roles - 200 OK Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", "\r", "pm.test(\"Status code name has string\", function () {\r", "    pm.response.to.have.status(\"Not Found\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users/roles", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users", "roles"]}}, "response": []}, {"name": "Create Organization users - 201 Created", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"User_ID\", jsonData.id);\r", "\r", "\r", "pm.test(\"Status code name should be Created\", function () {\r", "    pm.response.to.have.status(\"Created\");\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"email\": \"{{$randomEmail}}\",\r\n  \"firstName\": \"{{$randomFirstName}}\",\r\n  \"lastName\": \"{{$randomLastName}}\",\r\n  \"role\": \"ClientAdmin\",\r\n  \"identityProvider\": null,\r\n  \"enabled\": true,\r\n  \"emailVerified\": true,\r\n  \"username\": \"{{$randomEmail}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseUrl}}/v1/organizations/{{Org_ID}}/users", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", "{{Org_ID}}", "users"]}}, "response": []}]}, {"name": "Get organization Copy", "item": [{"name": "Token", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", " pm.test('Received access_token', function(){\r", "    const data = pm.response.json();\r", "    pm.expect(data).to.haveOwnProperty('access_token');\r", "   pm.environment.set('Token', data['access_token']);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Organizaion - 200 OK -", "event": [{"listen": "test", "script": {"exec": ["// Check if the status code is 200\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Parse the response body as JSON\r", "// I have passing hard coded if we need please change it\r", "let response = pm.response.json();\r", "pm.environment.set(\"Org_ID\", \"21\");\r", "pm.environment.set(\"Org_Type\", \"CLIENT\");\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["// pm.environment.set(\"Org_ID\", \"1\");\r", "// pm.environment.set(\"Org_Type\", \"DISTRIBUTOR\");\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations"]}}, "response": []}, {"name": "Get organization - 200 OK Distributors Des-Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/?org_id={{Org_ID}}&org_type={{Org_Type}}", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", ""], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "org_type", "value": "{{Org_Type}}"}]}}, "response": []}, {"name": "Get organization - 200 OK Client", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/?org_id={{Org_ID}}&org_type=CLIENT", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", ""], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "org_type", "value": "CLIENT"}]}}, "response": []}, {"name": "Get organization - 200 OK PARTNER", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations/?org_id={{Org_ID}}&org_type=PARTNER", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations", ""], "query": [{"key": "org_id", "value": "{{Org_ID}}"}, {"key": "org_type", "value": "PARTNER"}]}}, "response": []}, {"name": "Get organization - all 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations"]}}, "response": []}, {"name": "Get organization - Invalid token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "Invalid", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations"]}}, "response": []}, {"name": "Get organization - Blank token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations"]}}, "response": []}, {"name": "Get organization - Without token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseUrl}}/v1/organizations", "host": ["{{BaseUrl}}"], "path": ["v1", "organizations"]}}, "response": []}]}]}]}