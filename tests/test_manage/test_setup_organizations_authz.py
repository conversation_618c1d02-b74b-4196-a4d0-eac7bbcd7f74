from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.adapters.iam.schemas import Role
from app.enums import OrganizationRoleEnum
from app.services import mappers
from tests.factories import ClientFactory, DistributorFactory

runner = CliRunner()


class TestSetupOrganizationsAuthz:
    def test_organization_group_attributes_updated(self, db, iam, manage_app):
        distributor = DistributorFactory()
        distributor_group = mappers.organization_to_group(distributor)
        distributor_group.attributes = {}
        iam.groups.add(distributor_group, parent_path=None)

        client_org = ClientFactory(parent=distributor)
        client_org_group = mappers.organization_to_group(client_org)
        client_org_group.attributes = {"type": ["CUSTOMER"]}
        iam.groups.add(client_org_group, parent_path=distributor.path)

        assert iam.groups.get(distributor.path).attributes == {}
        assert iam.groups.get(client_org.path).attributes == {"type": ["CUSTOMER"]}

        result = runner.invoke(manage_app, "setup-organizations-authz")
        assert result.exit_code == 0, f"{result.stdout}"

        distributor_attributes = iam.groups.get(distributor.path).attributes
        assert (
            distributor_attributes
            == mappers.organization_to_group(distributor).attributes
        )
        client_attributes = iam.groups.get(client_org.path).attributes
        assert client_attributes == mappers.organization_to_group(client_org).attributes

    def test_partner_user_role_created(self, iam, manage_app):
        role = iam.roles.get(OrganizationRoleEnum.PARTNER_USER.value)
        if role:
            iam.roles.remove(role.name)
        result = runner.invoke(manage_app, "setup-organizations-authz")
        assert result.exit_code == 0, f"{result.stdout}"
        created_role = iam.roles.get(OrganizationRoleEnum.PARTNER_USER.value)
        assert created_role is not None

    def test_existing_partner_user_role_is_ok(self, iam, manage_app):
        role = iam.roles.get(OrganizationRoleEnum.PARTNER_USER.value)
        if not role:
            iam.roles.add(Role(name=OrganizationRoleEnum.PARTNER_USER.value))
        result = runner.invoke(manage_app, "setup-organizations-authz")
        assert result.exit_code == 0, f"{result.stdout}"
        assert iam.roles.get(OrganizationRoleEnum.PARTNER_USER.value)
