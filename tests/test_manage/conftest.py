from contextlib import contextmanager

import pytest
import typer
from pytest_lazyfixture import lazy_fixture

from app.manage import app


@pytest.fixture(params=[lazy_fixture("in_memory_iam"), lazy_fixture("keycloak_iam")])
def iam(request):
    return request.param


@pytest.fixture
def manage_app(iam):
    @contextmanager
    def get_iam():
        yield iam

    def inject_iam(ctx: typer.Context):
        ctx.ensure_object(dict)
        ctx.obj["get_iam"] = get_iam

    original_callback = app.registered_callback.callback
    app.callback()(inject_iam)
    yield app
    app.callback()(original_callback)
