from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.services.authz_apps import (
    create_app_management_scope,
    create_application_role,
    get_app_management_scope,
    get_application_role,
)

runner = CliRunner()


def test_setup_applications_authz_initial(iam, manage_app):
    scope = get_app_management_scope(iam)
    if scope:
        iam.client_scopes.remove(scope.name)
    role = get_application_role(iam)
    if role:
        iam.roles.remove(role.name)
    result = runner.invoke(manage_app, ["setup-applications-authz"])
    assert result.exit_code == 0, result.stdout
    assert result.stdout.count("OK, created") == 2, result.stdout

    scope = get_app_management_scope(iam)
    assert scope is not None, result.stdout
    role = get_application_role(iam)
    assert role is not None, result.stdout


def test_setup_applications_authz_consequent(iam, manage_app):
    role = get_application_role(iam)
    if not role:
        create_application_role(iam)
    scope = get_app_management_scope(iam)
    if not scope:
        create_app_management_scope(iam)
    result = runner.invoke(manage_app, ["setup-applications-authz"])
    assert result.exit_code == 0, result.stdout
    assert result.stdout.count("OK, found") == 2, result.stdout
