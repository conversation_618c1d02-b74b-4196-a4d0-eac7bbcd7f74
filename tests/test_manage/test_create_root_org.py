from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.core.utils import get_object
from app.models.organization import Distributor, Organization
from app.services import org_management

runner = CliRunner()

ADMIN_PASSWORD = "Pa55w0rd"
ADMIN_EMAIL = "<EMAIL>"
ORG_NAME = "foo root org"


def test_with_env_is_ok(db, manage_app):
    result = runner.invoke(
        manage_app,
        ["create-root-org"],
        env=dict(
            ROOT_ORG_NAME=ORG_NAME,
            ROOT_ORG_ADMIN_EMAIL=ADMIN_EMAIL,
            ROOT_ORG_ADMIN_PASSWORD=ADMIN_PASSWORD,
        ),
    )
    assert result.exit_code == 0, result.stdout
    assert f'Organization "{ORG_NAME}" successfully created.' in result.stdout
    assert org_management.organization_name_exist(db, org_name=ORG_NAME)


def test_with_params_is_ok(db, manage_app):
    result = runner.invoke(
        manage_app,
        [
            "create-root-org",
            "--organization-name",
            ORG_NAME,
            "--admin-email",
            ADMIN_EMAIL,
            "--admin-password",
            ADMIN_PASSWORD,
        ],
    )
    assert result.exit_code == 0, result.stdout
    assert f'Organization "{ORG_NAME}" successfully created.' in result.stdout
    assert org_management.organization_name_exist(db, org_name=ORG_NAME)


def test_error_on_existing(db, iam, manage_app):
    org_management.create_root_organization(
        db,
        iam,
        organization_name=ORG_NAME,
        admin_email=ADMIN_EMAIL,
        admin_password=ADMIN_PASSWORD,
    )
    result = runner.invoke(
        manage_app,
        ["create-root-org"],
        env=dict(
            ROOT_ORG_NAME=ORG_NAME,
            ROOT_ORG_ADMIN_EMAIL=ADMIN_EMAIL,
            ROOT_ORG_ADMIN_PASSWORD=ADMIN_PASSWORD,
        ),
    )
    assert result.exit_code == 1
    assert f'Organization "{ORG_NAME}" exists in DB.' in result.stdout
    assert org_management.organization_name_exist(db, org_name=ORG_NAME)


def test_ignore_existent_only_db(db, manage_app, iam):
    org = Distributor(name=ORG_NAME)
    db.add(org)
    db.commit()
    result = runner.invoke(
        manage_app,
        ["create-root-org", "--ignore-existent"],
        env=dict(
            ROOT_ORG_NAME=ORG_NAME,
            ROOT_ORG_ADMIN_EMAIL=ADMIN_EMAIL,
            ROOT_ORG_ADMIN_PASSWORD=ADMIN_PASSWORD,
        ),
    )
    assert result.exit_code == 0, result.stdout
    assert f'Organization "{ORG_NAME}" exists in DB.' in result.stdout, result.stdout

    assert org_management.organization_name_exist(db, org_name=ORG_NAME)
    db.refresh(org)
    group = iam.groups.get(org.path)
    assert group
    assert group.name == org.name


def test_create_root_org_ignore_existent(db, manage_app, iam):
    org = org_management.create_root_organization(
        db,
        iam,
        organization_name=ORG_NAME,
        admin_email=ADMIN_EMAIL,
        admin_password=ADMIN_PASSWORD,
    )
    organization_db = get_object(db, Organization, False, id=org.id)
    initial_group_path = organization_db.path

    result = runner.invoke(
        manage_app,
        ["create-root-org", "--ignore-existent"],
        env=dict(
            ROOT_ORG_NAME=ORG_NAME,
            ROOT_ORG_ADMIN_EMAIL=ADMIN_EMAIL,
            ROOT_ORG_ADMIN_PASSWORD=ADMIN_PASSWORD,
        ),
    )
    assert result.exit_code == 0, result.stdout
    assert org_management.organization_name_exist(db, org_name=ORG_NAME)
    db.refresh(organization_db)
    assert organization_db.path == initial_group_path


def test_admin_created_and_email_verified(db, iam, manage_app):
    result = runner.invoke(
        manage_app,
        [
            "create-root-org",
            "--organization-name",
            ORG_NAME,
            "--admin-email",
            ADMIN_EMAIL,
            "--admin-password",
            ADMIN_PASSWORD,
        ],
    )
    assert result.exit_code == 0, result.stdout
    org = get_object(db, Organization, False, name=ORG_NAME)
    assert org

    user = iam.users.get_by_email(ADMIN_EMAIL)
    assert user.email_verified
    user_org = org_management.get_user_org(db, iam, user_id=user.id)
    assert user_org == org
