from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.services import org_management

runner = <PERSON><PERSON><PERSON>unner()

ADMIN_PASSWORD = "Pa55w0rd"
ADMIN_EMAIL = "<EMAIL>"
ORG_NAME = "foo root org"


def test_no_input(db, iam, manage_app):
    org_management.create_root_organization(
        db,
        iam,
        organization_name=ORG_NAME,
        admin_email=ADMIN_EMAIL,
        admin_password=ADMIN_PASSWORD,
    )
    result = runner.invoke(
        manage_app,
        [
            "remove-root-org",
            "--organization-name",
            ORG_NAME,
            "--no-input",
        ],
    )
    assert result.exit_code == 0, (result.stdout, result.stderr)
    assert f"Organization {ORG_NAME} was successfully removed."
    assert not org_management.organization_name_exist(db, org_name=ORG_NAME)


def test_input(db, iam, manage_app):
    org_management.create_root_organization(
        db,
        iam,
        organization_name=ORG_NAME,
        admin_email=ADMIN_EMAIL,
        admin_password=ADMIN_PASSWORD,
    )

    result = runner.invoke(manage_app, ["remove-root-org"], input=f"{ORG_NAME}\nyes\n")
    assert result.exit_code == 0
    assert f"Organization name: {ORG_NAME}" in result.stdout
    assert f"Delete {ORG_NAME} organization? [y/N]: yes" in result.stdout
    assert f"Organization {ORG_NAME} was successfully removed." in (result.stdout)
    assert not org_management.organization_name_exist(db, org_name=ORG_NAME)
