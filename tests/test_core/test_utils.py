import pytest
from fastapi import HTTPException
from sqlalchemy import select
from sqlalchemy.orm import Session
from sqlalchemy.orm.attributes import InstrumentedAttribute

from app.models import Organization
from src.app.core.utils import get_object, get_object_or_404
from tests.factories.organization import ClientFactory, DistributorFactory


def test_get_object_single_model(db):
    """Test retrieving a single org by model."""
    org = DistributorFactory()
    object = get_object(db, Organization, False, Organization.name == org.name)
    assert isinstance(object, Organization)


def test_get_object_multiple_models(db: Session):
    """Test retrieving multiple orgs."""
    org = DistributorFactory()
    client1 = ClientFactory(parent=org)
    client2 = ClientFactory(parent=org)

    result = get_object(db, Organization, True, *[Organization.parent_id == org.id])
    assert isinstance(result, list)
    assert all(isinstance(org, Organization) for org in result)
    assert len(result) == 2
    assert {org.name for org in result} == {client1.name, client2.name}


def test_get_object_with_select(db: Session):
    """Test retrieving a single org with a Select query."""
    org = DistributorFactory()
    client1 = ClientFactory(parent=org)
    stmt = select(Organization).where(Organization.name == client1.name)
    result = get_object(db, stmt, False)
    assert result is not None
    assert result.name == client1.name


def test_get_object_with_instrumented_attribute(db: Session):
    """Test retrieving a column using an InstrumentedAttribute."""
    org = DistributorFactory()
    client1 = ClientFactory(parent=org)
    client2 = ClientFactory(parent=org)

    column: InstrumentedAttribute = Organization.name
    result = get_object(db, column, True, *[Organization.parent_id == org.id])
    assert isinstance(result, list)
    assert len(result) == 2
    assert client1.name in result
    assert client2.name in result


def test_get_object_with_filter(db: Session):
    """Test filtering by specific criteria."""
    org = DistributorFactory()
    client1 = ClientFactory(parent=org)
    result = get_object(db, Organization, False, Organization.name == client1.name)
    assert result is not None
    assert result.name == client1.name


def test_get_object_not_found(db: Session):
    """Test when no object matches the criteria."""
    result = get_object(db, Organization, False, Organization.name == "Charlie")
    assert result is None


def test_get_object_empty_list(db: Session):
    """Test when querying multiple objects but none match."""
    results = get_object(db, Organization, True, Organization.name == "Charlie")
    assert isinstance(results, list)
    assert results == []


def test_get_object_or_404_single_model(db):
    """Test retrieving a single org by model."""
    org = DistributorFactory()
    object = get_object_or_404(db, Organization, False, Organization.name == org.name)
    assert isinstance(object, Organization)


def test_get_object_or_404_multiple_models(db: Session):
    """Test retrieving multiple orgs."""
    org = DistributorFactory()
    client1 = ClientFactory(parent=org)
    client2 = ClientFactory(parent=org)

    result = get_object_or_404(
        db, Organization, True, *[Organization.parent_id == org.id]
    )
    assert isinstance(result, list)
    assert all(isinstance(org, Organization) for org in result)
    assert len(result) == 2
    assert {org.name for org in result} == {client1.name, client2.name}


def test_get_object_or_404_with_select(db: Session):
    """Test retrieving a single org with a Select query."""
    org = DistributorFactory()
    client1 = ClientFactory(parent=org)
    stmt = select(Organization).where(Organization.name == client1.name)
    result = get_object_or_404(db, stmt, False)
    assert result is not None
    assert result.name == client1.name


def test_get_object_or_404_with_instrumented_attribute(db: Session):
    """Test retrieving a column using an InstrumentedAttribute."""
    org = DistributorFactory()
    client1 = ClientFactory(parent=org)
    client2 = ClientFactory(parent=org)

    column: InstrumentedAttribute = Organization.name
    result = get_object_or_404(db, column, True, *[Organization.parent_id == org.id])
    assert isinstance(result, list)
    assert len(result) == 2
    assert client1.name in result
    assert client2.name in result


def test_get_object_or_404_with_filter(db: Session):
    """Test filtering by specific criteria."""
    org = DistributorFactory()
    client1 = ClientFactory(parent=org)
    result = get_object_or_404(
        db, Organization, False, Organization.name == client1.name
    )
    assert result is not None
    assert result.name == client1.name


def test_get_object_or_404_not_found(db: Session):
    """Test when no object matches the criteria."""
    with pytest.raises(HTTPException) as e:
        get_object_or_404(db, Organization, False, Organization.name == "Charlie")
    assert e.value.status_code == 404
    assert e.value.detail == "Not Found"


def test_get_object_or_404_empty_list(db: Session):
    """Test when querying multiple objects but none match."""
    with pytest.raises(HTTPException) as e:
        get_object_or_404(db, Organization, True, Organization.name == "Charlie")
    assert e.value.status_code == 404
    assert e.value.detail == "Not Found"
