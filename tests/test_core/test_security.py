import fastapi
import httpx
from keycloak_client import KeycloakException
from starlette import status

from app.core.security import http_exc_from_keycloak


def test_http_exc_from_keycloak_text():
    request = httpx.Request(method="GET", url="https://example.com")
    response = httpx.Response(
        status_code=status.HTTP_400_BAD_REQUEST,
        request=request,
    )
    error_msg = "foo"
    keycloak_exc = KeycloakException(
        error_msg,
        request=request,
        response=response,
    )
    http_exc = http_exc_from_keycloak(keycloak_exc)
    assert type(http_exc) == fastapi.HTTPException
    assert http_exc.status_code == response.status_code
    assert http_exc.detail == error_msg


def test_http_exc_from_keycloak_json():
    request = httpx.Request(method="GET", url="https://example.com")
    response = httpx.Response(
        status_code=status.HTTP_400_BAD_REQUEST, request=request, json={"foo": "bar"}
    )
    keycloak_exc = KeycloakException(
        "",
        request=request,
        response=response,
    )
    http_exc = http_exc_from_keycloak(keycloak_exc)
    assert type(http_exc) == fastapi.HTTPException
    assert http_exc.status_code == response.status_code
    assert http_exc.detail == response.json()
