import pytest
from fastapi import Depends, FastAPI, status
from fastapi.testclient import TestClient

from app.api.deps import healthy_secret
from app.core.config import settings
from app.services.health_check import (
    HEALTH_CHECK_HEADER,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    HealthCheckError,
)


def test_error_handler_with_exceptions():
    handler = ErrorHandler()
    with handler(service_name="one"):
        raise Exception("one")
    with handler(service_name="two"):
        raise Exception("two")

    with pytest.raises(HealthCheckError) as exc_info:
        handler.raise_for_errors()

    exc = exc_info.value
    assert str(exc) == str(
        HealthCheckError(
            {
                "one": str(Exception("one")),
                "two": str(Exception("two")),
            }
        )
    )


def test_error_handler_without_exceptions():
    handler = ErrorHandler()
    with handler(service_name="one"):
        pass
    handler.raise_for_errors()


app = FastAPI()

ENDPOINT = "/items"


@app.get(ENDPOINT, dependencies=[Depends(healthy_secret)])
def items():
    return None


client = TestClient(app)


def test_healthy_secret_ok():
    response = client.get(
        ENDPOINT, headers={HEALTH_CHECK_HEADER: settings.HEALTH_CHECK_SECRET}
    )
    assert response.status_code == status.HTTP_200_OK, response.json()


def test_healthy_secret_missing():
    response = client.get(ENDPOINT)
    assert response.status_code == status.HTTP_404_NOT_FOUND, response.json()
