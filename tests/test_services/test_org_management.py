import uuid
from operator import attrgetter, itemgetter
from typing import List
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import httpx
import pytest
from keycloak_client import KeycloakException
from pydantic import EmailStr, SecretStr
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app import models, schemas
from app.adapters.iam.exc import BadPassword
from app.adapters.iam.in_memory import InMemoryIAM
from app.adapters.iam.schemas import Group, IdentityProvider, UserCreate
from app.adapters.platform_api_client.schemas import AuditLogModel, AuditResponse
from app.common.exceptions import NotFound
from app.common.pagination import Pagination
from app.enums import FileReportStatusEnum, OrganizationRoleEnum, OrganizationTypeEnum
from app.models import FileReport, UserFile, UserReport
from app.models.organization import Partner
from app.schemas.organization import OrganizationNameStr
from app.services import mappers, org_management
from app.services.org_management import OrganizationExistsInDb, UserState
from tests.factories import (
    ClientFactory,
    DistributorFactory,
    DomainFactory,
    PartnerFactory,
    PMNCodeFactory,
)


@pytest.fixture
def iam(in_memory_iam) -> InMemoryIAM:
    return in_memory_iam


class TestCreateOrganization:
    @staticmethod
    def test_ok(db, iam):
        org_name = "acme"
        organization_in = schemas.OrganizationCreate(name=OrganizationNameStr(org_name))
        organization_db = org_management.create_organization(
            db,
            iam,
            organization_in=organization_in,
            org_type=OrganizationTypeEnum.DISTRIBUTOR,
        )
        # db
        organization_model = org_management.get_object(
            db, models.Organization, False, id=organization_db.id
        )

        assert organization_model.name == organization_in.name

        org_group = iam.groups.get(organization_model.path)
        assert org_group
        assert org_group.name == org_name
        assert org_group.path == f"/{org_name}"
        assert org_group.attributes == dict(
            type=["organization"],
            organization_id=[str(organization_model.id)],
            organization_type=[organization_model.type.value],
        )

    @staticmethod
    def test_name_exists(db):
        # skip keycloak setup/teardown
        kc_mock = Mock()

        org_name = "Acme"
        organization_db = models.Distributor(name=org_name)  # noqa
        db.add(organization_db)
        db.commit()

        with pytest.raises(OrganizationExistsInDb):
            organization_in = schemas.OrganizationCreate(
                name=OrganizationNameStr(org_name)
            )
            org_management.create_organization(
                db,
                kc_mock,
                organization_in=organization_in,
                org_type=OrganizationTypeEnum.DISTRIBUTOR,
            )
        # ignore case in org name
        with pytest.raises(OrganizationExistsInDb):
            organization_in = schemas.OrganizationCreate(
                name=OrganizationNameStr(org_name.lower())
            )
            org_management.create_organization(
                db,
                kc_mock,
                organization_in=organization_in,
                org_type=OrganizationTypeEnum.DISTRIBUTOR,
            )

    @staticmethod
    def test_keycloak_error(db, monkeypatch):
        # skip keycloak setup/teardown
        kc_mock = Mock()

        def create_org_group_mock(*_, **__):
            raise KeycloakException(
                "foo", request=httpx.Request("GET", ""), response=httpx.Response(400)
            )

        monkeypatch.setattr(
            org_management, "create_organization_group", create_org_group_mock
        )
        org_name = OrganizationNameStr("Acme")
        with pytest.raises(KeycloakException):
            organization_in = schemas.OrganizationCreate(name=org_name)
            org_management.create_organization(
                db,
                kc_mock,
                organization_in=organization_in,
                org_type=OrganizationTypeEnum.DISTRIBUTOR,
            )

        # org not committed to db
        assert not org_management.organization_name_exist(db, org_name=org_name)

    @staticmethod
    def test_db_error(db, iam, iam_store, monkeypatch):
        def _mock_commit(*_, **__):
            raise SQLAlchemyError()

        monkeypatch.setattr(db, "commit", _mock_commit)
        org_name = OrganizationNameStr("Acme")
        organization_in = schemas.OrganizationCreate(name=org_name)
        with pytest.raises(SQLAlchemyError):
            org_management.create_organization(
                db,
                iam,
                organization_in=organization_in,
                org_type=OrganizationTypeEnum.DISTRIBUTOR,
            )
        assert not iam.groups.get_many()

    @staticmethod
    def test_with_parent(db, iam):
        parent_organization_in = schemas.OrganizationCreate(
            name=OrganizationNameStr("BBQ")
        )
        parent_organization_db = org_management.create_organization(
            db,
            iam,
            organization_in=parent_organization_in,
            org_type=OrganizationTypeEnum.DISTRIBUTOR,
        )
        parent_organization_model = org_management.get_object(
            db, models.Organization, False, id=parent_organization_db.id
        )

        child_organization_in = schemas.OrganizationCreate(
            name=OrganizationNameStr("acme")
        )
        child_organization_db = org_management.create_organization(
            db,
            iam,
            organization_in=child_organization_in,
            parent=parent_organization_model,
            org_type=OrganizationTypeEnum.CLIENT,
        )
        child_organization_model = org_management.get_object(
            db, models.Organization, False, id=child_organization_db.id
        )
        assert child_organization_model.parent_id == parent_organization_db.id
        assert iam.groups.get(child_organization_model.path)


class TestDeleteOrganization:
    @staticmethod
    def test_ok(db, iam, iam_store, create_user):
        organization_in = schemas.OrganizationCreate(name=OrganizationNameStr("acme"))
        organization_db = org_management.create_organization(
            db,
            iam,
            organization_in=organization_in,
            org_type=OrganizationTypeEnum.DISTRIBUTOR,
        )
        user_sandy = create_user(email="<EMAIL>")
        organization_model = org_management.get_object(
            db, models.Organization, False, id=organization_db.id
        )

        org_management.add_user_to_organization(
            iam,
            organization=organization_model,
            user_id=user_sandy.id,
            organization_role=OrganizationRoleEnum.CLIENT_ADMIN,
        )

        org_management.delete_organization(
            db,
            iam,
            organization=organization_model,
        )
        assert not org_management.organization_name_exist(
            db, org_name=organization_db.name
        )
        assert iam.groups.get(organization_model.path) is None

        assert iam.users.get_by_id(user_sandy.id) is not None
        sandy_roles = iam.users.get_roles(user_sandy.id)
        assert OrganizationRoleEnum.CLIENT_ADMIN.value not in sandy_roles

    @staticmethod
    def test_delete_users(db, iam, create_user):
        organization_in = schemas.OrganizationCreate(name=OrganizationNameStr("acme"))
        organization_db = org_management.create_organization(
            db,
            iam,
            organization_in=organization_in,
            org_type=OrganizationTypeEnum.DISTRIBUTOR,
        )
        user_sandy = create_user(email="<EMAIL>")
        organization_model = org_management.get_object(
            db, models.Organization, False, id=organization_db.id
        )
        org_management.add_user_to_organization(
            iam,
            organization=organization_model,
            user_id=user_sandy.id,
            organization_role=OrganizationRoleEnum.CLIENT_ADMIN,
        )
        user_bob = create_user(email="<EMAIL>")
        org_management.add_user_to_organization(
            iam,
            organization=organization_model,
            user_id=user_bob.id,
            organization_role=OrganizationRoleEnum.CLIENT_USER,
        )

        org_management.delete_organization(
            db, iam, organization=organization_model, delete_users=True
        )

        assert not iam.users.get_many()


class TestCreateRootOrganization:
    @staticmethod
    def test_ok(db, iam, iam_store):
        org_name = "bikini bottom"
        admin_email = "<EMAIL>"
        admin_password = "garry"
        organization_db = org_management.create_root_organization(
            db,
            iam,
            organization_name=org_name,
            admin_email=admin_email,
            admin_password=admin_password,
        )
        organization_model = org_management.get_object(
            db, models.Organization, False, id=organization_db.id
        )

        assert organization_model.name == org_name
        group = iam.groups.get(organization_model.path)
        assert group
        assert group.name == organization_model.name

        user = iam.users.get_by_email(EmailStr(admin_email))
        assert user
        assert user.email == admin_email
        assert iam_store.user_password[user.id] == (SecretStr(admin_password), False)
        user_groups = iam.users.get_groups(user.id)
        assert user_groups
        assert user_groups[0].name == organization_model.name

        user_roles = iam.users.get_roles(user.id)
        assert OrganizationRoleEnum.DISTRIBUTOR_ADMIN.value in user_roles


class TestRemoveRootOrganization:
    @staticmethod
    def test_ok(db, iam):
        org_name = "bikini bottom"
        admin_email = "<EMAIL>"
        admin_password = "garry"
        root_organization_db = org_management.create_root_organization(
            db,
            iam,
            organization_name=org_name,
            admin_email=admin_email,
            admin_password=admin_password,
        )
        root_org_model = org_management.get_object(
            db, models.Organization, False, id=root_organization_db.id
        )

        child_organization_db = org_management.create_organization(
            db,
            iam,
            organization_in=schemas.OrganizationCreate(
                name=OrganizationNameStr("acme")
            ),
            parent=root_org_model,
            org_type=OrganizationTypeEnum.CLIENT,
        )
        child_org_model = org_management.get_object(
            db, models.Organization, False, id=child_organization_db.id
        )

        org_management.remove_root_organization(
            db,
            iam,
            organization_name=root_org_model.name,
        )

        assert not org_management.organization_name_exist(
            db, org_name=root_organization_db.name
        )
        assert not org_management.organization_name_exist(
            db, org_name=child_organization_db.name
        )

        root_org_group = iam.groups.get(root_org_model.path)
        assert not root_org_group

        child_organization_group = iam.groups.get(child_org_model.path)
        assert not child_organization_group


@pytest.mark.parametrize(
    "attributes,is_group",
    [
        ({}, False),
        ({"foo": ["bar"]}, False),
        ({"type": ["module"]}, False),
        ({"type": ["organization"]}, True),
    ],
)
def test_is_organization_group(attributes, is_group):
    group = Group(
        name="boo",
        path="/boo",
        attributes=attributes,
    )
    assert org_management.is_organization_group(group) == is_group


@pytest.mark.parametrize(
    "org_factory,roles",
    [
        (
            DistributorFactory,
            [
                dict(name="DistributorAdmin", display_name="Admin"),
                dict(name="DistributorUser", display_name="User"),
            ],
        ),
        (
            ClientFactory,
            [
                dict(name="ClientAdmin", display_name="Admin"),
                dict(name="ClientUser", display_name="User"),
            ],
        ),
    ],
)
def test_read_user_roles(root_organization, org_factory, roles):
    org = org_factory()
    result = org_management.get_allowed_roles(org)
    assert list(sorted(result, key=attrgetter("name"))) == list(
        sorted(roles, key=itemgetter("name"))
    )


class TestGetDescendants:
    def test_get_descendants(self, db):
        root = DistributorFactory()
        root_pmn = PMNCodeFactory.create_batch(1, org_id=root.id)
        root_domain = DomainFactory.create_batch(1, org_id=root.id)
        root_client_a = ClientFactory(parent=root)
        root_client_a_pmn = PMNCodeFactory.create_batch(1, org_id=root_client_a.id)
        root_client_a_domain = DomainFactory.create_batch(1, org_id=root_client_a.id)
        root_client_b = ClientFactory(parent=root)
        distributor_a = DistributorFactory(parent=root)
        distributor_a_pmn = PMNCodeFactory.create_batch(1, org_id=distributor_a.id)
        distributor_a_domain = DomainFactory.create_batch(1, org_id=distributor_a.id)
        distributor_a_client_a = ClientFactory(parent=distributor_a)
        distributor_a_client_b = ClientFactory(parent=distributor_a)
        distributor_a_distributor_b = DistributorFactory(parent=distributor_a)
        distributor_a_distributor_b_client_a = ClientFactory(
            parent=distributor_a_distributor_b
        )
        distributor_c = DistributorFactory(parent=root)
        distributor_c_client_a = ClientFactory(parent=distributor_c)
        distributor_c_client_b = ClientFactory(parent=distributor_c)

        descendants = org_management.get_descendants(
            db, root_org=root, page=1, page_size=10, count=False
        )
        expected = [
            (root, [root_pmn[0].pmn_code], [root_domain[0].domain_name]),
            (
                root_client_a,
                [root_client_a_pmn[0].pmn_code],
                [root_client_a_domain[0].domain_name],
            ),
            (root_client_b, None, None),
            (
                distributor_a,
                [distributor_a_pmn[0].pmn_code],
                [distributor_a_domain[0].domain_name],
            ),
            (distributor_a_client_a, None, None),
            (distributor_a_client_b, None, None),
            (distributor_a_distributor_b, None, None),
            (distributor_a_distributor_b_client_a, None, None),
            (distributor_c, None, None),
            (distributor_c_client_a, None, None),
            (distributor_c_client_b, None, None),
        ]
        assert descendants == expected[:10]

        descendants = org_management.get_descendants(
            db, root_org=distributor_a, page=1, page_size=10, count=False
        )
        expected = [
            (
                distributor_a,
                [distributor_a_pmn[0].pmn_code],
                [distributor_a_domain[0].domain_name],
            ),
            (distributor_a_client_a, None, None),
            (distributor_a_client_b, None, None),
            (distributor_a_distributor_b, None, None),
            (distributor_a_distributor_b_client_a, None, None),
        ]
        assert descendants == expected

        descendants = org_management.get_descendants(
            db, root_org=distributor_a_distributor_b, page=1, page_size=10, count=False
        )
        expected = [
            (distributor_a_distributor_b, None, None),
            (distributor_a_distributor_b_client_a, None, None),
        ]
        assert descendants == expected

    def test_get_descendants_only_clients(self, db):
        root = DistributorFactory()
        root_client_a = ClientFactory(parent=root)
        root_client_a_pmn = PMNCodeFactory.create_batch(1, org_id=root_client_a.id)
        root_client_a_domain = DomainFactory.create_batch(1, org_id=root_client_a.id)
        root_client_b = ClientFactory(parent=root)

        distributor_a = DistributorFactory(parent=root)
        distributor_a_client_a = ClientFactory(parent=distributor_a)
        distributor_a_client_b = ClientFactory(parent=distributor_a)

        distributor_b = DistributorFactory(parent=root)
        distributor_b_client_a = ClientFactory(parent=distributor_b)
        distributor_b_client_a_pmn = PMNCodeFactory.create_batch(
            1, org_id=distributor_b_client_a.id
        )
        distributor_b_client_a_domain = DomainFactory.create_batch(
            1, org_id=distributor_b_client_a.id
        )
        distributor_b_client_b = ClientFactory(parent=distributor_b)

        distributor_c = DistributorFactory(parent=distributor_a)
        distributor_c_client_a = ClientFactory(parent=distributor_c)
        distributor_c_client_b = ClientFactory(parent=distributor_c)

        descendants = org_management.get_descendants(
            db,
            root,
            1,
            10,
            False,
            None,
            *[models.Organization.type == OrganizationTypeEnum.CLIENT],
        )
        expected = [
            (
                root_client_a,
                [root_client_a_pmn[0].pmn_code],
                [root_client_a_domain[0].domain_name],
            ),
            (root_client_b, None, None),
            (distributor_a_client_a, None, None),
            (distributor_a_client_b, None, None),
            (
                distributor_b_client_a,
                [distributor_b_client_a_pmn[0].pmn_code],
                [distributor_b_client_a_domain[0].domain_name],
            ),
            (distributor_b_client_b, None, None),
            (distributor_c_client_a, None, None),
            (distributor_c_client_b, None, None),
        ]

        assert len(descendants) == 8, descendants
        assert descendants == expected

        descendants = org_management.get_descendants(
            db,
            distributor_a,
            1,
            10,
            False,
            None,
            *[models.Organization.type == OrganizationTypeEnum.CLIENT],
        )
        assert descendants == [
            (distributor_a_client_a, None, None),
            (distributor_a_client_b, None, None),
            (distributor_c_client_a, None, None),
            (distributor_c_client_b, None, None),
        ]

        descendants = org_management.get_descendants(
            db,
            distributor_c,
            1,
            10,
            False,
            None,
            *[models.Organization.type == OrganizationTypeEnum.CLIENT],
        )
        assert len(descendants) == 2, descendants
        assert descendants == [
            (distributor_c_client_a, None, None),
            (distributor_c_client_b, None, None),
        ]


class TestUpdateOrganization:
    def test_valid_data_is_ok(self, db, iam, client_org):
        client_update = schemas.OrganizationUpdate(
            name=OrganizationNameStr(client_org.name[:-1] + "abc")
        )
        new_client_org = org_management.update_organization(
            db, iam, client_org, client_update
        )
        assert new_client_org.name == client_update.name

    def test_iam_group_updated(self, db, iam, root_organization, client_org, iam_store):
        client_update = schemas.OrganizationUpdate(
            name=OrganizationNameStr(client_org.name[:-1] + "abc")
        )
        new_client_org = org_management.update_organization(
            db, iam, client_org, client_update
        )
        org = org_management.get_object(
            db, models.Organization, False, id=new_client_org.id
        )
        new_path = org.path
        group = iam.groups.get(new_path)
        assert group, iam_store.groups
        assert group.name == org.name

    def test_name_already_exists(self, db, iam, client_org, create_client_org):
        client_update = schemas.OrganizationUpdate(
            name=OrganizationNameStr(client_org.name)
        )
        client_org_2 = create_client_org(
            name=client_org.name[:-1] + "abc", parent=client_org.parent
        )
        with pytest.raises(OrganizationExistsInDb):
            org_management.update_organization(db, iam, client_org_2, client_update)

    def test_name_not_changed(self, db, iam, client_org):
        client_update = schemas.OrganizationUpdate(
            name=OrganizationNameStr(client_org.name)
        )
        new_client_org = org_management.update_organization(
            db, iam, client_org, client_update
        )
        assert new_client_org.name == client_update.name

    def test_name_not_changed_ignore_case(
        self, db, iam, root_organization, create_client_org
    ):
        client_org = create_client_org("Random name", root_organization)
        client_update = schemas.OrganizationUpdate(
            name=OrganizationNameStr(client_org.name.upper())
        )
        new_client_org = org_management.update_organization(
            db, iam, client_org, client_update
        )
        assert new_client_org.name == client_update.name


@pytest.mark.usefixtures("root_organization")
class TestAddPartnerUser:
    def test_if_partner_user_draft_exists_then_it_is_reserved(
        self,
        db,
        iam,
        create_user,
        core_api,
    ):
        email = "<EMAIL>"
        create_user(
            email=email,
            password="",
            enabled=False,
            attributes=UserState.DRAFT.as_attributes(),
        )
        user_in = schemas.CreatePartnerUser(
            email=EmailStr(email),
            organization_name=OrganizationNameStr(f"acme-{uuid.uuid4().hex[:6]}"),
        )
        user = org_management.add_partner_user(
            db,
            iam,
            core_api,
            user_in,
        )
        iam_user = iam.users.get_by_id(user.id)
        assert UserState.RESERVED.in_attributes(iam_user.attributes)

    def test_mobile_phone_saved(
        self,
        db,
        iam,
        create_user,
        core_api,
    ):
        email = "<EMAIL>"
        user_in = schemas.CreatePartnerUser(
            email=EmailStr(email),
            organization_name=OrganizationNameStr(f"acme-{uuid.uuid4().hex[:6]}"),
            mobile_phone="+************",
        )
        user = org_management.add_partner_user(
            db,
            iam,
            core_api,
            user_in,
        )
        iam_user = iam.users.get_by_id(user.id)
        assert iam_user.attributes["mobilePhone"] == [user.mobile_phone]

    def test_partner_apps_notified(self, db, iam, core_api):
        email = "<EMAIL>"
        user_in = schemas.CreatePartnerUser(
            email=EmailStr(email),
            organization_name=OrganizationNameStr(f"acme-{uuid.uuid4().hex[:6]}"),
        )
        user = org_management.add_partner_user(
            db,
            iam,
            core_api,
            user_in,
        )
        assert core_api.notifications.get(email) == user

    def test_contact_added(self, db, iam, core_api):
        email = "<EMAIL>"
        user_in = schemas.CreatePartnerUser(
            email=EmailStr(email),
            organization_name=OrganizationNameStr(f"acme-{uuid.uuid4().hex[:6]}"),
        )
        user = org_management.add_partner_user(
            db,
            iam,
            core_api,
            user_in,
        )
        assert user in core_api.contacts

    def test_if_user_draft_exists_then_names_are_updated(
        self,
        db,
        iam,
        create_user,
        core_api,
    ):
        email = "<EMAIL>"
        create_user(
            first_name="bob1",
            last_name="sponge1",
            email=email,
            enabled=False,
            attributes=UserState.DRAFT.as_attributes(),
        )
        user_in = schemas.CreatePartnerUser(
            email=EmailStr(email),
            first_name="bob2",
            last_name="sponge2",
            organization_name=OrganizationNameStr(f"acme-{uuid.uuid4().hex[:6]}"),
        )
        user = org_management.add_partner_user(db, iam, core_api, user_in)
        iam_user = iam.users.get_by_id(user.id)
        assert iam_user.first_name == user_in.first_name
        assert iam_user.last_name == user_in.last_name

    def test_if_user_exists_and_not_a_draft_raises_user_already_exists(
        self,
        db,
        iam,
        create_user,
        core_api,
    ):
        user = create_user(email="<EMAIL>")
        user_in = schemas.CreatePartnerUser(
            email=user.email,
            organization_name=OrganizationNameStr(f"acme-{uuid.uuid4().hex[:6]}"),
        )
        with pytest.raises(org_management.UserAlreadyExists):
            org_management.add_partner_user(db, iam, core_api, user_in)

    def test_if_organization_is_distributor_raises_not_allowed_exception(
        self, db, iam, root_organization, core_api
    ):
        user_in = schemas.CreatePartnerUser(
            email=EmailStr("<EMAIL>"),
            organization_name=OrganizationNameStr(root_organization.name),
        )
        with pytest.raises(org_management.OperationNotAllowed):
            org_management.add_partner_user(db, iam, core_api, user_in)

    def test_if_organization_does_not_exist_a_partner_organization_created(
        self, db, iam, core_api
    ):
        user_in = schemas.CreatePartnerUser(
            email=EmailStr("<EMAIL>"),
            organization_name=OrganizationNameStr(f"acme-{uuid.uuid4().hex[:6]}"),
        )
        user = org_management.add_partner_user(db, iam, core_api, user_in)
        assert user.organization.type == OrganizationTypeEnum.PARTNER
        organization = (
            db.query(Partner).filter_by(name=user_in.organization_name).first()
        )
        assert organization is not None
        assert iam.groups.get(organization.path)

    @pytest.mark.parametrize(
        "organization_type", [OrganizationTypeEnum.PARTNER, OrganizationTypeEnum.CLIENT]
    )
    def test_if_organization_exists_a_membership_created_for_user(
        self, db, iam, core_api, root_organization, organization_type
    ):
        existing_organization = org_management.create_organization(
            db,
            iam,
            organization_in=schemas.OrganizationCreate(
                name=OrganizationNameStr("acme")
            ),
            org_type=organization_type,
            parent=root_organization,
        )
        user_in = schemas.CreatePartnerUser(
            email=EmailStr("<EMAIL>"),
            organization_name=OrganizationNameStr(existing_organization.name),
        )
        user = org_management.add_partner_user(db, iam, core_api, user_in)
        assert user.organization.id == existing_organization.id
        assert user.organization.type == existing_organization.type


class TestEnablePartnerUser:
    @pytest.mark.parametrize(
        "attributes",
        [
            {},
            org_management.UserState.DRAFT.as_attributes(),
            org_management.UserState.REGISTERED.as_attributes(),
        ],
    )
    def test_if_not_reserved_raises_not_allowed(self, db, iam, create_user, attributes):
        user = create_user(
            email="<EMAIL>",
            attributes=attributes,
        )
        with pytest.raises(org_management.OperationNotAllowed):
            org_management.enable_partner_user(
                db, iam, user.id, schemas.EnablePartnerUser(password=SecretStr("boo"))
            )

    def test_if_not_a_member_of_organization_raises_not_allowed(
        self, db, iam, create_user
    ):
        user = create_user(
            email="<EMAIL>", attributes=UserState.RESERVED.as_attributes()
        )
        with pytest.raises(org_management.OperationNotAllowed):
            org_management.enable_partner_user(
                db, iam, user.id, schemas.EnablePartnerUser(password=SecretStr("boo"))
            )

    @pytest.mark.parametrize(
        "organization_type,enabled",
        [
            (OrganizationTypeEnum.CLIENT, False),
            (OrganizationTypeEnum.PARTNER, True),
        ],
    )
    def test_if_user_reserved_and_a_member_of_organization_then_registered(
        self, db, iam, create_user, root_organization, organization_type, enabled
    ):
        organization = org_management.create_organization(
            db,
            iam,
            organization_in=schemas.OrganizationCreate(
                name=OrganizationNameStr("acme")
            ),
            org_type=organization_type,
            parent=root_organization,
        )
        organization_model = org_management.get_object(
            db, models.Organization, False, id=organization.id
        )
        user = create_user(
            email="<EMAIL>",
            attributes=UserState.RESERVED.as_attributes(),
        )
        org_management.add_user_to_organization(
            iam,
            organization=organization_model,
            user_id=user.id,
            organization_role=organization_type.roles[-1],
        )

        org_management.enable_partner_user(
            db, iam, user.id, schemas.EnablePartnerUser(password=SecretStr("boo"))
        )

        updated_user = iam.users.get_by_id(user.id)
        assert updated_user.email_verified is True, updated_user
        assert updated_user.enabled is enabled, updated_user
        assert UserState.REGISTERED.in_attributes(updated_user.attributes)

    def test_if_bad_password_then_raises_not_allowed(
        self, db, iam, create_user, root_organization
    ):
        organization = org_management.create_organization(
            db,
            iam,
            organization_in=schemas.OrganizationCreate(
                name=OrganizationNameStr("acme")
            ),
            org_type=OrganizationTypeEnum.PARTNER,
            parent=root_organization,
        )
        organization_model = org_management.get_object(
            db, models.Organization, False, id=organization.id
        )
        user = create_user(
            email="<EMAIL>",
            email_verified=False,
            enabled=False,
            attributes=UserState.RESERVED.as_attributes(),
        )
        org_management.add_user_to_organization(
            iam,
            organization=organization_model,
            user_id=user.id,
            organization_role=OrganizationRoleEnum.CLIENT_USER,
        )

        def raise_bad_password(*args, **kwargs):
            raise BadPassword("some message")

        iam.users.set_password = raise_bad_password

        with pytest.raises(org_management.OperationNotAllowed) as exc_info:
            org_management.enable_partner_user(
                db, iam, user.id, schemas.EnablePartnerUser(password=SecretStr("boo"))
            )
        assert str(exc_info.value) == "some message"

        updated_user = iam.users.get_by_id(user.id)
        assert updated_user.email_verified is False, updated_user
        assert updated_user.enabled is False, updated_user
        assert UserState.RESERVED.in_attributes(updated_user.attributes)


class TestFileRecordIntegration:
    """Integration tests for file record creation functions without mocking."""

    @pytest.fixture
    def client_org(self, db: Session):
        """Create a client organization for testing."""
        client = ClientFactory()
        db.add(client)
        db.commit()
        db.refresh(client)
        return client

    def test_create_user_file_record(self, db: Session, client_org):
        """Test creating a user file record with actual database."""
        filename = "test_users.csv"
        s3_path = "s3://test-bucket/test_users.csv"

        user_file = org_management.create_user_file_record(
            db, client_org.id, filename, s3_path
        )

        assert user_file.id is not None
        assert user_file.uuid is not None
        assert user_file.organization_id == client_org.id
        assert user_file.file_name == filename
        assert user_file.path == s3_path

        db_user_file = db.query(UserFile).filter(UserFile.id == user_file.id).first()
        assert db_user_file is not None
        assert db_user_file.file_name == filename

        db.delete(user_file)
        db.commit()

    def test_create_file_report_record(self, db: Session, client_org):
        """Test creating a file report record with actual database."""
        filename = "test_users.csv"
        s3_path = "s3://test-bucket/test_users.csv"
        total_rows = 10
        valid_rows = 8
        status = FileReportStatusEnum.COMPLETED

        user_file = org_management.create_user_file_record(
            db, client_org.id, filename, s3_path
        )

        file_report = org_management.create_file_report_record(
            db, user_file.id, total_rows, valid_rows, status
        )

        assert file_report.id is not None
        assert file_report.user_file_id == user_file.id
        assert file_report.total_rows == total_rows
        assert file_report.valid_rows == valid_rows
        assert file_report.status == status

        db_file_report = (
            db.query(FileReport).filter(FileReport.id == file_report.id).first()
        )
        assert db_file_report is not None
        assert db_file_report.total_rows == total_rows

        db.delete(file_report)
        db.delete(user_file)
        db.commit()

    def test_create_user_report_records(self, db: Session, client_org):
        """Test creating user report records with actual database."""
        filename = "test_users.csv"
        s3_path = "s3://test-bucket/test_users.csv"

        user_file = org_management.create_user_file_record(
            db, client_org.id, filename, s3_path
        )

        invalid_rows = [
            {
                "first_name": "John",
                "last_name": "Doe",
                "email": "invalid-email",
                "error": "Invalid email format",
            },
            {
                "first_name": "",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "error": "First name is required",
            },
        ]

        user_reports = org_management.create_user_report_records(
            db, user_file.id, invalid_rows
        )

        assert len(user_reports) == 2

        assert user_reports[0].user_file_id == user_file.id
        assert user_reports[0].first_name == "John"
        assert user_reports[0].last_name == "Doe"
        assert user_reports[0].email == "invalid-email"
        assert user_reports[0].reason == "Invalid email format"

        assert user_reports[1].user_file_id == user_file.id
        assert user_reports[1].first_name == ""
        assert user_reports[1].last_name == "Smith"
        assert user_reports[1].email == "<EMAIL>"
        assert user_reports[1].reason == "First name is required"

        db_user_reports = (
            db.query(UserReport).filter(UserReport.user_file_id == user_file.id).all()
        )
        assert len(db_user_reports) == 2

        for report in user_reports:
            db.delete(report)
        db.delete(user_file)
        db.commit()

    def test_update_file_report(self, db: Session, client_org):
        """Test updating file report status and valid rows."""
        filename = "test_users.csv"
        s3_path = "s3://test-bucket/test_users.csv"

        user_file = org_management.create_user_file_record(
            db, client_org.id, filename, s3_path
        )

        file_report = org_management.create_file_report_record(
            db, user_file.id, 10, 0, FileReportStatusEnum.IN_PROGRESS
        )

        org_management._update_file_report(
            db, user_file.id, FileReportStatusEnum.COMPLETED, 8
        )

        db.refresh(file_report)
        assert file_report.status == FileReportStatusEnum.COMPLETED
        assert file_report.valid_rows == 8

        db.delete(file_report)
        db.delete(user_file)
        db.commit()

    def test_file_record_cascade_delete(self, db: Session, client_org):
        """Test that deleting a user file cascades to related records."""
        filename = "test_users.csv"
        s3_path = "s3://test-bucket/test_users.csv"

        user_file = org_management.create_user_file_record(
            db, client_org.id, filename, s3_path
        )

        org_management.create_file_report_record(
            db, user_file.id, 10, 5, FileReportStatusEnum.COMPLETED
        )

        invalid_rows = [
            {
                "first_name": "John",
                "last_name": "Doe",
                "email": "invalid",
                "error": "Invalid email",
            }
        ]
        org_management.create_user_report_records(db, user_file.id, invalid_rows)

        assert db.query(UserFile).filter(UserFile.id == user_file.id).count() == 1
        assert (
            db.query(FileReport).filter(FileReport.user_file_id == user_file.id).count()
            == 1
        )
        assert (
            db.query(UserReport).filter(UserReport.user_file_id == user_file.id).count()
            == 1
        )

        db.delete(user_file)
        db.commit()

        assert db.query(UserFile).filter(UserFile.id == user_file.id).count() == 0
        assert (
            db.query(FileReport).filter(FileReport.user_file_id == user_file.id).count()
            == 0
        )
        assert (
            db.query(UserReport).filter(UserReport.user_file_id == user_file.id).count()
            == 0
        )


class TestProcessValidRows:
    @pytest.fixture
    def db_session(self):
        """Mock database session"""
        return MagicMock(spec=Session)

    @pytest.fixture
    def iam_store(self):
        """Create IAM store for testing"""
        from app.adapters.iam.in_memory import IAMStore

        return IAMStore()

    @pytest.fixture
    def iam(self, iam_store):
        """Create in-memory IAM for testing"""
        from app.adapters.iam.in_memory import InMemoryIAM

        return InMemoryIAM(iam_store)

    @pytest.fixture
    def client_org(self, db: Session):
        """Create a client organization for testing"""
        client = ClientFactory()
        db.add(client)
        db.commit()
        db.refresh(client)
        return client

    @pytest.fixture
    def distributor_org(self, db: Session):
        """Create a distributor organization for testing"""
        distributor = DistributorFactory()
        db.add(distributor)
        db.commit()
        db.refresh(distributor)
        return distributor

    @pytest.fixture
    def partner_org(self, db: Session):
        """Create a partner organization for testing"""
        partner = PartnerFactory()
        db.add(partner)
        db.commit()
        db.refresh(partner)
        return partner

    @pytest.fixture
    def message_for_audit_log(self):
        return AuditLogModel(
            ipAddress="request.client.host",
            user="<EMAIL>",
            request="organization/1/users/bulk",
            event="POST",
            source="USER",
            payload=None,
        )

    @pytest.fixture
    def valid_rows(self):
        """Sample valid rows for testing"""
        return [
            {"first_name": "John", "last_name": "Doe", "email": "<EMAIL>"},
            {
                "first_name": "Jane",
                "last_name": "Smith",
                "email": "<EMAIL>",
            },
        ]

    @pytest.fixture
    def client_roles(self):
        """Client roles for testing"""
        return [OrganizationRoleEnum.CLIENT_USER.value]

    @pytest.fixture
    def distributor_roles(self):
        """Distributor roles for testing"""
        return [OrganizationRoleEnum.DISTRIBUTOR_USER.value]

    @pytest.fixture
    def mock_user_info(self):
        return schemas.UserInfo(
            id=str(uuid.uuid4()),
            username="test-user",
            email="<EMAIL>",
            roles=["admin"],
            email_verified=True,
            typ="Bearer",
            organization=schemas.OrganizationSimple(
                id=1, type=OrganizationTypeEnum.CLIENT, name="Test Org", parent_id=1
            ),
            realm_access=schemas.auth.RealmAccess(
                roles=[OrganizationRoleEnum.DISTRIBUTOR_ADMIN]
            ),
            sub=str(uuid.uuid4()),
        )

    @pytest.fixture
    def partner_roles(self):
        """Partner roles for testing"""
        return [OrganizationRoleEnum.PARTNER_USER.value]

    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_all_successful(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_user_to_org,
        db_session,
        iam,
        client_org,
        valid_rows,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with all successful user creations."""
        user_file_id = 1
        is_error = False

        mock_create_user_report_records.reset_mock()

        assert iam.users.get_by_email("<EMAIL>") is None
        assert iam.users.get_by_email("<EMAIL>") is None

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            valid_rows,
            client_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        if mock_update_file_report.call_count > 0:
            args, kwargs = mock_update_file_report.call_args
            print(f"_update_file_report args: {args}")
            print(f"_update_file_report kwargs: {kwargs}")

        mock_create_user_report_records.assert_not_called()

        assert mock_update_file_report.call_count == 1
        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED, 2
        )

        assert iam.users.get_by_email("<EMAIL>") is not None
        assert iam.users.get_by_email("<EMAIL>") is not None

    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    @patch("app.services.users.validate_identity_provider")
    def test_process_valid_rows_with_identity_provider(
        self,
        mock_idp,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_user_to_org,
        db_session,
        iam,
        client_org,
        valid_rows,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with identity provider specified."""
        user_file_id = 1
        is_error = False
        identity_provider = "example"
        mock_idp.return_value = IdentityProvider(
            alias=identity_provider, display_name=identity_provider
        )
        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            valid_rows,
            client_roles,
            identity_provider,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        mock_create_user_report_records.assert_not_called()
        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED, 2
        )

        users_created = iam.users.get_by_email("<EMAIL>")
        assert users_created is not None

    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_existing_users(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_user_to_org,
        db_session,
        iam,
        client_org,
        valid_rows,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with some users already existing."""
        user_file_id = 1
        is_error = False

        existing_user_schema = UserCreate(
            username="<EMAIL>",
            email="<EMAIL>",
            first_name="John",
            last_name="Doe",
            enabled=True,
            email_verified=True,
        )
        existing_user = iam.users.add(existing_user_schema)

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            valid_rows,
            client_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        mock_create_user_report_records.assert_not_called()
        assert mock_update_file_report.call_count == 1
        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED, 2
        )

        created_user = iam.users.get_by_email("<EMAIL>")
        existing_user = iam.users.get_by_email("<EMAIL>")
        assert created_user is not None
        assert existing_user is not None

        assert iam.users.get_groups(created_user.id) is not None
        assert iam.users.get_groups(existing_user.id) is not None

    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_existing_users_with_org(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_user_to_org,
        db_session,
        iam,
        valid_rows,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with some users already existing."""
        user_file_id = 1
        is_error = False

        existing_user_schema = UserCreate(
            username="<EMAIL>",
            email="<EMAIL>",
            first_name="John",
            last_name="Doe",
            enabled=True,
            email_verified=True,
        )
        existing_user = iam.users.add(existing_user_schema)
        distributor = DistributorFactory()
        distributor_group = mappers.organization_to_group(distributor)
        distributor_group.attributes = {}
        iam.groups.add(distributor_group, parent_path=None)

        client_org = ClientFactory(parent=distributor)
        client_org_group = mappers.organization_to_group(client_org)
        client_org_group.attributes = {"type": ["CUSTOMER"]}
        iam.groups.add(client_org_group, parent_path=distributor.path)

        iam.groups.add_member(client_org.path, existing_user.id)

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            valid_rows,
            client_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )
        mock_create_user_report_records.assert_called_once()
        failed_rows = mock_create_user_report_records.call_args.kwargs["invalid_rows"]
        assert len(failed_rows) == 1
        assert failed_rows[0]["email"] == "<EMAIL>"
        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED_WITH_ERRORS, 1
        )
        assert "already exists" in failed_rows[0]["error"]

    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_validation_error(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_user_to_org,
        db_session,
        iam,
        client_org,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with validation errors."""
        user_file_id = 1
        is_error = False

        invalid_rows = [
            {"first_name": "John", "last_name": "Doe", "email": ""},
            {
                "first_name": "Jane",
                "last_name": "Smith",
                "email": "<EMAIL>",
            },
        ]

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            invalid_rows,
            client_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )
        mock_create_user_report_records.assert_called_once()
        failed_rows = mock_create_user_report_records.call_args.kwargs["invalid_rows"]
        assert len(failed_rows) == 1
        assert failed_rows[0]["first_name"] == "John"
        assert failed_rows[0]["last_name"] == "Doe"
        assert failed_rows[0]["email"] == ""
        assert "validation errors" in failed_rows[0]["error"]

        assert mock_create_user_report_records.call_count == 1

        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED_WITH_ERRORS, 1
        )

        assert iam.users.get_by_email("<EMAIL>") is not None

    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_validation_exception(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        db_session,
        iam,
        client_org,
        valid_rows,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with validation exception."""
        user_file_id = 1
        is_error = False

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            valid_rows,
            client_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        mock_create_user_report_records.assert_called_once()
        failed_rows = mock_create_user_report_records.call_args.kwargs["invalid_rows"]
        assert len(failed_rows) == 2

        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED_WITH_ERRORS, 0
        )

    @patch(
        "app.services.users.add_user_in_to_organization",
        side_effect=Exception("Failed to add to org"),
    )
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_add_to_org_exception(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_to_org,
        db_session,
        iam,
        client_org,
        valid_rows,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with exception when adding to organization."""
        user_file_id = 1
        is_error = False

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            valid_rows,
            client_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        mock_create_user_report_records.assert_called_once()
        failed_rows = mock_create_user_report_records.call_args.kwargs["invalid_rows"]
        assert len(failed_rows) == 2
        assert "Failed to add to org" in failed_rows[0]["error"]

        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED_WITH_ERRORS, 0
        )

    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_empty_list(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        db_session,
        iam,
        client_org,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing an empty list of rows."""
        user_file_id = 1
        is_error = False
        empty_rows = []

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            empty_rows,
            client_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        mock_create_user_report_records.assert_not_called()
        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED, 0
        )

    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_is_error_flag(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_user_to_org,
        db_session,
        iam,
        client_org,
        valid_rows,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with is_error flag set to True."""
        user_file_id = 1

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            valid_rows,
            client_roles,
            None,
            True,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        mock_create_user_report_records.assert_not_called()
        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED_WITH_ERRORS, 2
        )

    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_different_org_types(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_user_to_org,
        db_session,
        iam,
        distributor_org,
        partner_org,
        client_org,
        valid_rows,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with different organization types."""
        user_file_id = 1
        is_error = False

        distributor_roles = [OrganizationRoleEnum.DISTRIBUTOR_USER.value]
        org_management._process_valid_rows(
            db_session,
            iam,
            distributor_org,
            user_file_id,
            valid_rows,
            distributor_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        partner_roles = [OrganizationRoleEnum.PARTNER_USER.value]
        org_management._process_valid_rows(
            db_session,
            iam,
            partner_org,
            user_file_id,
            valid_rows,
            partner_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        distributor_user = iam.users.get_by_email("<EMAIL>")
        partner_user = iam.users.get_by_email("<EMAIL>")

        assert distributor_user is not None
        assert partner_user is not None

    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_multiple_roles(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_user_to_org,
        db_session,
        iam,
        client_org,
        valid_rows,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing valid rows with multiple roles assigned."""
        user_file_id = 1
        is_error = False
        multiple_roles = [OrganizationRoleEnum.CLIENT_USER.value, "Approver"]

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            valid_rows,
            multiple_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )

        mock_create_user_report_records.assert_not_called()
        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED, 2
        )

        assert iam.users.get_by_email("<EMAIL>") is not None
        assert iam.users.get_by_email("<EMAIL>") is not None

    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.org_management.create_user_report_records")
    @patch("app.services.org_management._update_file_report")
    @patch("app.adapters.platform_api_client.audit_service.AuditAPI")
    def test_process_valid_rows_with_missing_fields(
        self,
        mock_audit_service,
        mock_update_file_report,
        mock_create_user_report_records,
        mock_add_user_to_org,
        db_session,
        iam,
        client_org,
        client_roles,
        message_for_audit_log,
        mock_user_info,
    ):
        """Test processing rows with missing optional fields."""
        user_file_id = 1
        is_error = False
        rows_with_missing_fields = [
            {
                "last_name": "Doe",
                "email": "<EMAIL>",
            },
            {
                "first_name": "Jane",
                "email": "<EMAIL>",
            },
            {
                "first_name": "Jane",
                "last_name": "Doe",
                "email": "<EMAIL>",
            },
        ]

        org_management._process_valid_rows(
            db_session,
            iam,
            client_org,
            user_file_id,
            rows_with_missing_fields,
            client_roles,
            None,
            is_error,
            mock_audit_service,
            message_for_audit_log,
            mock_user_info,
        )
        mock_audit_service.assert_not_called()
        mock_update_file_report.assert_called_once_with(
            db_session, user_file_id, FileReportStatusEnum.COMPLETED_WITH_ERRORS, 1
        )

        user1 = iam.users.get_by_email("<EMAIL>")
        user2 = iam.users.get_by_email("<EMAIL>")

        assert user1 is None
        assert user2


@pytest.fixture
def user_file(db: Session, client_org: models.Organization) -> models.UserFile:
    """Create a test user file."""
    user_file = models.UserFile(
        uuid=uuid.uuid4(),
        file_name=f"{str(uuid.uuid4())[:6]}_test_users.csv",
        path="s3://bucket/test_users.csv",
        organization_id=client_org.id,
    )
    db.add(user_file)
    db.commit()
    db.refresh(user_file)
    return user_file


@pytest.fixture
def file_report(db: Session, user_file: models.UserFile) -> models.FileReport:
    """Create a test file report."""
    file_report = models.FileReport(
        user_file_id=user_file.id,
        status="completed",
        total_rows=100,
        valid_rows=98,
    )
    db.add(file_report)
    db.commit()
    db.refresh(file_report)
    return file_report


@pytest.fixture
def user_reports(db: Session, user_file: models.UserFile) -> List[models.UserReport]:
    """Create test user reports."""
    reports = [
        models.UserReport(
            user_file_id=user_file.id,
            first_name="User1",
            last_name="Test",
            email="<EMAIL>",
            reason="Invalid first name format.",
        ),
        models.UserReport(
            user_file_id=user_file.id,
            first_name="User2",
            last_name="Test",
            email="user2example.com",
            reason="Invalid email format.",
        ),
    ]
    db.add_all(reports)
    db.commit()
    for report in reports:
        db.refresh(report)
    return reports


@pytest.fixture
def audit_log():
    return AuditResponse(
        user="<EMAIL>",
        created_date="2024-01-01T12:00:00Z",
        ipAddress="45.0.0",
        event="POST",
        request="BULK",
        payload=None,
        source="organizations/bulk",
    )


class TestGetUserFileStatus:
    def test_empty_result(
        self, db: Session, client_org: models.Organization, audit_log
    ):
        """Test that an empty result is returned when no files exist."""
        pagination = Pagination(page=1, page_size=10)
        mock_audit_service = MagicMock()
        mock_audit_service.get_audit.return_value = audit_log
        with pytest.raises(NotFound) as e:
            org_management.get_user_file_status(
                db=db,
                organization_id=client_org.id,
                pagination=pagination,
                audit_service=mock_audit_service,
            )
        assert str(e.value) == "User file not found."

    def test_single_file(
        self,
        db: Session,
        client_org: models.Organization,
        user_file: models.UserFile,
        file_report: models.FileReport,
        audit_log,
    ):
        """Test retrieval of a single file status."""
        mock_audit_service = MagicMock()
        mock_audit_service.get_audit.return_value = audit_log
        pagination = Pagination(page=1, page_size=10)
        results, total_count = org_management.get_user_file_status(
            db=db,
            organization_id=client_org.id,
            pagination=pagination,
            audit_service=mock_audit_service,
        )
        assert total_count == 1
        assert len(results) == 1

        # Check the file status details
        file_status = results[0]
        assert file_status.fileName == "test_users.csv"
        assert file_status.id == user_file.uuid
        assert file_status.status == file_report.status

    def test_pagination(
        self,
        db: Session,
        client_org: models.Organization,
        user_file: models.UserFile,
        file_report: models.FileReport,
        audit_log,
    ):
        """Test pagination of file status results."""
        # Create additional user files
        for i in range(5):
            new_file = models.UserFile(
                uuid=uuid.uuid4(),
                file_name=f"{str(uuid.uuid4())[:6]}_test_users_{i}.csv",
                path=f"s3://bucket/test_users_{i}.csv",
                organization_id=client_org.id,
            )
            db.add(new_file)
            db.commit()
            db.refresh(new_file)

            new_report = models.FileReport(
                user_file_id=new_file.id,
                status="completed",
                total_rows=100,
                valid_rows=98,
            )
            db.add(new_report)
            db.commit()

        # Test first page with page_size=2
        mock_audit_service = MagicMock()
        mock_audit_service.get_audit.return_value = audit_log
        pagination = Pagination(page=1, page_size=2)
        results, total_count = org_management.get_user_file_status(
            db=db,
            organization_id=client_org.id,
            pagination=pagination,
            audit_service=mock_audit_service,
        )
        assert total_count == 6  # 5 new files + 1 from fixture
        assert len(results) == 2
        assert results[0].fileName == "test_users.csv"
        assert results[1].fileName == "test_users_0.csv"

        # Test second page
        pagination = Pagination(page=2, page_size=2)
        results, total_count = org_management.get_user_file_status(
            db=db,
            organization_id=client_org.id,
            pagination=pagination,
            audit_service=mock_audit_service,
        )
        assert total_count == 6
        assert len(results) == 2
        assert results[0].fileName == "test_users_1.csv"
        assert results[1].fileName == "test_users_2.csv"

    def test_different_organization(
        self,
        db: Session,
        client_org: models.Organization,
        root_organization: models.Organization,
        user_file: models.UserFile,
        audit_log,
    ):
        """Test that files from a different organization are not returned."""
        pagination = Pagination(page=1, page_size=10)
        mock_audit_service = MagicMock()
        mock_audit_service.get_audit.return_value = audit_log
        with pytest.raises(NotFound) as e:
            org_management.get_user_file_status(
                db=db,
                organization_id=root_organization.id,
                pagination=pagination,
                audit_service=mock_audit_service,
            )
        assert str(e.value) == "User file not found."


class TestGetUserFileReport:
    def test_file_not_found(self, db: Session, client_org: models.Organization):
        """Test that None is returned when the file is not found."""
        report = org_management.get_user_file_report(
            db=db, organization_id=client_org.id, user_file_id=uuid.uuid4()
        )
        assert report is None

    def test_file_without_report(self, db: Session, client_org: models.Organization):
        """Test retrieval of a file without a report."""
        # Create a user file without a report
        user_file = models.UserFile(
            uuid=uuid.uuid4(),
            file_name="no_report.csv",
            path="s3://bucket/no_report.csv",
            organization_id=client_org.id,
        )
        db.add(user_file)
        db.commit()
        db.refresh(user_file)

        report = org_management.get_user_file_report(
            db=db, organization_id=client_org.id, user_file_id=user_file.uuid
        )
        assert report is not None
        assert report["totalRows"] == 0
        assert report["validRows"] == 0
        assert report["invalidRows"] == []

    def test_file_with_report_no_errors(
        self, db: Session, client_org: models.Organization, user_file: models.UserFile
    ):
        """Test retrieval of a file with a report but no error reports."""
        # Create a file report without error reports
        file_report = models.FileReport(
            user_file_id=user_file.id,
            status="completed",
            total_rows=100,
            valid_rows=100,  # All rows are valid
        )
        db.add(file_report)
        db.commit()

        report = org_management.get_user_file_report(
            db=db, organization_id=client_org.id, user_file_id=user_file.uuid
        )
        assert report is not None
        assert report["totalRows"] == 100
        assert report["validRows"] == 100
        assert report["invalidRows"] == []

    def test_file_with_report_and_errors(
        self,
        db: Session,
        client_org: models.Organization,
        user_file: models.UserFile,
        file_report: models.FileReport,
        user_reports: List[models.UserReport],
    ):
        """Test retrieval of a file with a report and error reports."""
        report = org_management.get_user_file_report(
            db=db, organization_id=client_org.id, user_file_id=user_file.uuid
        )
        assert report is not None
        assert report["totalRows"] == file_report.total_rows
        assert report["validRows"] == file_report.valid_rows
        assert len(report["invalidRows"])
