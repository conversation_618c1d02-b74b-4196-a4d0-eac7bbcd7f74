import io

import pytest
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, UploadFile
from starlette import status

from app.constants import MAX_NAME_LENGTH, MIN_NAME_LENGTH
from app.services.validation import (
    CSV,
    validate_csv_content,
    validate_email,
    validate_name,
)


class TestCSVValidator:
    @pytest.fixture
    def valid_csv_content(self):
        return (
            b"first name,last name,email\nJohn,<PERSON><PERSON>,"
            b"<EMAIL>\n<PERSON><PERSON>,<PERSON>,<EMAIL>"
        )

    @pytest.fixture
    def valid_csv_with_bom(self):
        # Add UTF-8 BOM to the beginning of the file
        return b"\xef\xbb\xbffirst name,last name,email\nJohn,<PERSON><PERSON>,<EMAIL>"

    @pytest.fixture
    def empty_csv(self):
        return b""

    @pytest.fixture
    def csv_with_wrong_extension(self):
        return b"first name,last name,email\nJohn,<PERSON><PERSON>,<EMAIL>"

    @pytest.fixture
    def csv_with_missing_headers(self):
        return b"first name,email\nJohn,<EMAIL>"

    @pytest.fixture
    def csv_with_wrong_order(self):
        return b"email,first name,last name\<EMAIL>,<PERSON>,Doe"

    @pytest.fixture
    def csv_with_extra_headers(self):
        return b"first name,last name,email,phone\nJohn,Doe,<EMAIL>,123456789"

    @pytest.fixture
    def mock_upload_file(self, valid_csv_content):
        file = UploadFile(
            filename="test.csv",
            file=io.BytesIO(valid_csv_content),
            content_type="text/csv",
        )
        return file

    @pytest.fixture
    def expected_headers(self):
        return ["first name", "last name", "email"]

    def test_init_validates_extension(self, mock_upload_file, expected_headers):
        # Test that initialization validates file extension
        mock_upload_file.filename = "test.csv"
        validator = CSV(file=mock_upload_file)
        assert validator.file == mock_upload_file
        assert validator.expected_headers == expected_headers

        # Test with invalid extension
        mock_upload_file.filename = "test.txt"
        with pytest.raises(HTTPException) as excinfo:
            CSV(file=mock_upload_file)
        assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Invalid file format. Please upload a .csv file." in excinfo.value.detail

    def test_validate_size(self, mock_upload_file, expected_headers):
        # Test with file size within limit
        validator = CSV(file=mock_upload_file, max_size_mb=1.0)
        validator._validate_size()  # Should not raise exception

    def test_validate_size_exceeds_limit(
        self, mock_upload_file, expected_headers, valid_csv_content
    ):
        # Test with file size exceeds limit
        validator = CSV(file=mock_upload_file, max_size_mb=0.0000000001)
        with pytest.raises(HTTPException) as excinfo:
            validator._validate_size()
        assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "File size exceeds" in excinfo.value.detail

    def test_validate_not_empty(self, mock_upload_file, expected_headers, empty_csv):
        # Test with non-empty file
        validator = CSV(file=mock_upload_file)
        validator._validate_not_empty()  # Should not raise exception

        # Test with empty file
        mock_upload_file.file = io.BytesIO(empty_csv)
        validator = CSV(file=mock_upload_file)
        validator.content = b""
        with pytest.raises(HTTPException) as excinfo:
            validator._validate_not_empty()
        assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "The uploaded CSV file is empty." == excinfo.value.detail

    def test_validate_headers_exist(self, mock_upload_file, expected_headers):
        validator = CSV(file=mock_upload_file)
        validator.headers = ["first name", "last name", "email"]
        validator._validate_headers_exist()  # Should not raise exception

        validator.headers = []
        with pytest.raises(HTTPException) as excinfo:
            validator._validate_headers_exist()
        assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "CSV file has no headers" in excinfo.value.detail

    def test_validate_column_count(self, mock_upload_file, expected_headers):
        validator = CSV(file=mock_upload_file)
        validator.headers = ["first name", "last name", "email"]
        validator._validate_column_count(3)  # Should not raise exception

        with pytest.raises(HTTPException) as excinfo:
            validator._validate_column_count(4)
        assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "CSV column should have exactly 3 headers." in excinfo.value.detail

    def test_validate_headers_match(self, mock_upload_file, expected_headers):
        validator = CSV(file=mock_upload_file)

        # Test with matching headers
        validator.headers = ["first name", "last name", "email"]
        validator._validate_headers_match(
            expected_headers
        )  # Should not raise exception

        # Test with headers in wrong order
        validator.headers = ["email", "first name", "last name"]
        with pytest.raises(HTTPException) as excinfo:
            validator._validate_headers_match(expected_headers)
        assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            "Headers must be in the order: First Name, Last Name, Email."
            == excinfo.value.detail
        )

        # Test with missing headers
        validator.headers = ["first name", "last name"]
        with pytest.raises(HTTPException) as excinfo:
            validator._validate_headers_match(expected_headers)
        assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            "Invalid headers. Headers should be " "First Name, Last Name, Email."
        ) == excinfo.value.detail

        # Test with extra headers
        validator.headers = ["first name", "last name", "email", "phone"]
        with pytest.raises(HTTPException) as excinfo:
            validator._validate_headers_match(expected_headers)
        assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            "Invalid headers. Headers should be " "First Name, Last Name, Email."
        ) == excinfo.value.detail

    def test_validate_with_bom(self, expected_headers, valid_csv_with_bom):
        # Test with BOM in the CSV file
        mock_file = UploadFile(
            filename="test.csv",
            file=io.BytesIO(valid_csv_with_bom),
            content_type="text/csv",
        )
        validator = CSV(file=mock_file)
        result = validator.validate()

        assert "content" in result

    def test_validate_with_case_insensitive_headers(
        self, mock_upload_file, expected_headers
    ):
        # Test with headers in different case
        content = b"First Name,Last Name,Email\nJohn,Doe,<EMAIL>"
        mock_upload_file.file = io.BytesIO(content)
        validator = CSV(file=mock_upload_file)
        validator.validate()

    def test_validate_with_whitespace_in_headers(
        self, mock_upload_file, expected_headers
    ):
        # Test with extra whitespace in headers
        content = b" first name , last name , email \nJohn,Doe,<EMAIL>"
        mock_upload_file.file = io.BytesIO(content)
        validator = CSV(file=mock_upload_file)
        validator.validate()

    def test_validate_with_unicode_decode_error(
        self, mock_upload_file, expected_headers
    ):
        # Test with invalid UTF-8 encoding
        content = b"first name,last name,email\nJohn,Doe,<EMAIL>\xFF\xFF"
        mock_upload_file.file = io.BytesIO(content)
        validator = CSV(file=mock_upload_file)

        with pytest.raises(HTTPException) as excinfo:
            validator.validate()
        assert excinfo.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "encoding error" in excinfo.value.detail

    def test_validate_full_process(self, mock_upload_file, expected_headers):
        # Test the full validation process with a valid CSV file
        validator = CSV(file=mock_upload_file)
        result = validator.validate()

        assert "content" in result
        # assert result["headers"] == ["first name", "last name", "email"]
        assert mock_upload_file.file.tell() == 0  # File pointer should be reset


class TestValidateName:
    def test_empty_name(self):
        """Test validation of empty name."""
        result = validate_name("", "First name")
        assert result == "Missing value in 'First Name' column"

    def test_name_too_long(self):
        """Test validation of name exceeding maximum length."""
        long_name = "A" * (MAX_NAME_LENGTH + 1)
        result = validate_name(long_name, "First name")
        assert result == f"First name exceeds {MAX_NAME_LENGTH} characters."

    @pytest.mark.parametrize(
        "name",
        [
            "John",
            "Jane",
            "Smith Jones",
            "OBrien",
            "Zhang Wei",
            "O'Connor",
            "D'Angelo",
            "John's",
            "John!",
            "Jane@Doe",
            "Smith#Jones",
            "User$Name",
            "First%Last",
            "Hello^World",
            "Test&Name",
            "Name*Test",
            "First(Last)",
            "User)Name",
            "First_Last",
            "User+Name",
            "First=Last",
            "User{Name}",
            "First[Last]",
            "User|Name",
            "First;Last",
            "User:Name",
            "First-Last",
            'User"Name',
            "First,Last",
            "User<Name>",
            "First?Last",
            "User/Name",
            "First~Last",
            "First123",
            "A",
        ],
    )
    def test_valid_name(self, name):
        """Test validation of valid name."""
        result = validate_name(name, "First name")
        assert result is None

    def test_name_at_boundary_conditions(self):
        """Test validation of names at boundary conditions."""
        min_length_name = "A" * MIN_NAME_LENGTH
        assert validate_name(min_length_name, "First name") is None

        max_length_name = "A" * MAX_NAME_LENGTH
        assert validate_name(max_length_name, "First name") is None

    def test_name_with_whitespace(self):
        """Test validation of names with leading/trailing whitespace."""
        name_with_whitespace = "  John Doe  "
        result = validate_name(name_with_whitespace, "First name")
        assert result is None


class TestValidateEmail:
    def test_empty_email(self):
        """Test validation of empty email."""
        emails_set = set()
        result = validate_email("", emails_set)
        assert result == "Missing value in 'Email' column."

    @pytest.mark.parametrize(
        "email",
        [
            "plainaddress",
            "#@%^%#$@#$@#.com",
            "@example.com",
            "Joe Smith <<EMAIL>>",
            "email.example.com",
            "email@<EMAIL>",
            "<EMAIL> (Joe Smith)",
            "<EMAIL>",
            "<EMAIL>",
        ],
    )
    def test_invalid_email_format(self, email):
        """Test validation of emails with invalid format."""
        emails_set = set()

        result = validate_email(email, emails_set)
        assert result == f"Invalid email format {email}"
        assert email.lower() not in emails_set

    def test_duplicate_email(self):
        """Test validation of duplicate emails."""
        emails_set = {"<EMAIL>", "<EMAIL>"}

        result = validate_email("<EMAIL>", emails_set)
        assert result == "Duplicate email in file"

        result = validate_email("<EMAIL>", emails_set)
        assert result == "Duplicate email in file"

        assert len(emails_set) == 2
        assert "<EMAIL>" in emails_set
        assert "<EMAIL>" in emails_set

    @pytest.mark.parametrize(
        "email",
        [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
    )
    def test_valid_email(self, email):
        """Test validation of valid emails."""
        emails_set = set()

        result = validate_email(email, emails_set)
        assert result is None
        assert email.lower() in emails_set

    def test_email_case_insensitivity(self):
        """Test that email validation is case-insensitive."""
        emails_set = set()

        result = validate_email("<EMAIL>", emails_set)
        assert result is None
        assert "<EMAIL>" in emails_set

        result = validate_email("<EMAIL>", emails_set)
        assert result == "Duplicate email in file"

        result = validate_email("<EMAIL>", emails_set)
        assert result == "Duplicate email in file"

    def test_email_set_modification(self):
        """Test that valid emails are added to the set."""
        emails_set = set()

        validate_email("<EMAIL>", emails_set)
        assert len(emails_set) == 1
        assert "<EMAIL>" in emails_set

        validate_email("<EMAIL>", emails_set)
        assert len(emails_set) == 2
        assert "<EMAIL>" in emails_set
        assert "<EMAIL>" in emails_set

        validate_email("<EMAIL>", emails_set)
        assert len(emails_set) == 2


class TestValidateCSVContent:
    @pytest.fixture
    def valid_csv_content(self):
        """Fixture for valid CSV content."""
        return (
            "first name,last name,email\n"
            "John,Doe,<EMAIL>\n"
            "Jane,Smith,<EMAIL>"
        ).encode("utf-8")

    @pytest.fixture
    def csv_with_empty_fields(self):
        """Fixture for CSV with empty fields."""
        return (
            "first name,last name,email\n"
            ",Doe,<EMAIL>\n"
            "Jane,,<EMAIL>\n"
            "Bob,Smith,"
        ).encode("utf-8")

    @pytest.fixture
    def csv_with_invalid_names(self):
        """Fixture for CSV with invalid names."""
        return (
            "first name,last name,email\n"
            "J,Doe,<EMAIL>\n"
            "Jane!,Smith,<EMAIL>\n"
            f"{'A' * 51},Smith,<EMAIL>"
        ).encode("utf-8")

    @pytest.fixture
    def csv_with_invalid_emails(self):
        """Fixture for CSV with invalid emails."""
        return (
            "first name,last name,email\n"
            "John,Doe,not-an-email\n"
            "Jane,Smith,<EMAIL>\n"
            "Bob,Johnson,<EMAIL>"
        ).encode("utf-8")

    def test_valid_csv_content(self, valid_csv_content):
        """Test validation of valid CSV content."""
        valid_rows, validation_errors = validate_csv_content(valid_csv_content)

        assert len(valid_rows) == 2
        assert len(validation_errors) == 0

        assert valid_rows[0]["first_name"] == "John"
        assert valid_rows[0]["last_name"] == "Doe"
        assert valid_rows[0]["email"] == "<EMAIL>"

        assert valid_rows[1]["first_name"] == "Jane"
        assert valid_rows[1]["last_name"] == "Smith"
        assert valid_rows[1]["email"] == "<EMAIL>"

    def test_csv_with_empty_fields(self, csv_with_empty_fields):
        """Test validation of CSV with empty fields."""
        valid_rows, validation_errors = validate_csv_content(csv_with_empty_fields)

        assert len(valid_rows) == 0
        assert len(validation_errors) == 3

        assert validation_errors[0]["error"] == "Missing value in 'First Name' column"
        assert validation_errors[1]["error"] == "Missing value in 'Last Name' column"
        assert validation_errors[2]["error"] == "Missing value in 'Email' column."

    def test_csv_with_invalid_names(self, csv_with_invalid_names):
        """Test validation of CSV with invalid names."""
        valid_rows, validation_errors = validate_csv_content(csv_with_invalid_names)

        assert len(valid_rows) == 2
        assert len(validation_errors) == 1
        assert "exceeds" in validation_errors[0]["error"]

    def test_csv_with_invalid_emails(self, csv_with_invalid_emails):
        """Test validation of CSV with invalid emails."""
        valid_rows, validation_errors = validate_csv_content(csv_with_invalid_emails)

        assert len(valid_rows) == 1
        assert len(validation_errors) == 2

        assert valid_rows[0]["first_name"] == "Jane"
        assert valid_rows[0]["last_name"] == "Smith"
        assert valid_rows[0]["email"] == "<EMAIL>"

        assert validation_errors[0]["error"] == "Invalid email format not-an-email"

    def test_csv_with_whitespace(self):
        """Test validation of CSV with whitespace in fields."""
        csv_content = (
            "first name,last name,email\n" "  John  ,  Doe  ,  <EMAIL>  \n"
        ).encode("utf-8")

        valid_rows, validation_errors = validate_csv_content(csv_content)
        assert len(valid_rows) == 1
        assert len(validation_errors) == 0

        assert valid_rows[0]["first_name"] == "John"
        assert valid_rows[0]["last_name"] == "Doe"
        assert valid_rows[0]["email"] == "<EMAIL>"

    def test_csv_with_mixed_errors(self):
        """Test validation of CSV with mixed error types."""
        csv_content = (
            "first name,last name,email\n"
            ",Doe,<EMAIL>\n"
            "Jane,Smith,invalid-email\n"
            "A,Johnson,<EMAIL>\n"
            "Bob,J,<EMAIL>\n"
            f"{'A' * 51},J,<EMAIL>\n"
            f"Bob,{'A' * 51},<EMAIL>\n"
        ).encode("utf-8")

        valid_rows, validation_errors = validate_csv_content(csv_content)

        assert len(valid_rows) == 1
        assert len(validation_errors) == 5
        assert "Missing value in 'First Name' column" in validation_errors[0]["error"]
        assert "Invalid email format" in validation_errors[1]["error"]
        assert "Duplicate email in file" in validation_errors[2]["error"]
        assert "exceeds" in validation_errors[3]["error"]
        assert "exceeds" in validation_errors[4]["error"]
        assert f"{'A'*10}..." in validation_errors[3]["first_name"]
        assert f"{'A'*10}..." in validation_errors[4]["last_name"]

    def test_empty_csv(self):
        """Test validation of empty CSV (only header row)."""
        csv_content = "first name,last name,email\n".encode("utf-8")

        valid_rows, validation_errors = validate_csv_content(csv_content)

        assert len(valid_rows) == 0
        assert len(validation_errors) == 0

    def test_csv_with_case_insensitive_emails(self):
        """Test validation of CSV with case-insensitive email duplicates."""
        csv_content = (
            "first name,last name,email\n"
            "John,Doe,<EMAIL>\n"
            "Jane,Smith,<EMAIL>\n"
        ).encode("utf-8")

        valid_rows, validation_errors = validate_csv_content(csv_content)

        assert len(valid_rows) == 1
        assert len(validation_errors) == 1

        assert valid_rows[0]["first_name"] == "John"
        assert valid_rows[0]["last_name"] == "Doe"
        assert valid_rows[0]["email"] == "<EMAIL>"

        assert validation_errors[0]["error"] == "Duplicate email in file"

    def test_csv_with_missing_value(self):
        csv_content = (
            "first name,last name,email\n" "John,Doe,<EMAIL>,\n" "Jane\n"
        ).encode("utf-8")

        valid_rows, validation_errors = validate_csv_content(csv_content)

        assert len(valid_rows) == 1
        assert len(validation_errors) == 1

        assert valid_rows[0]["first_name"] == "John"
        assert valid_rows[0]["last_name"] == "Doe"
        assert valid_rows[0]["email"] == "<EMAIL>"

        assert validation_errors[0]["error"] == "One or more fields are empty"
        assert validation_errors[0]["first_name"] == "Jane"
        assert validation_errors[0]["last_name"] == "-"
        assert validation_errors[0]["email"] == "-"
