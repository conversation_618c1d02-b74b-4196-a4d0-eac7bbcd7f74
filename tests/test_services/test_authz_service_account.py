import uuid

import pytest
from keycloak_client.api.urls import URL_OIDC_TOKEN
from starlette import status

from app import schemas
from app.adapters.iam.keycloak import KeycloakIAM
from app.adapters.iam.schemas import Client, ClientBase
from app.api import deps
from app.services import authz_service_account as authz


class TestSetupServiceAccount:
    @pytest.fixture
    def create_client(self, iam):
        def _create_client(**kwargs) -> Client:
            client_id = str(uuid.uuid4())
            params = dict(
                client_id=client_id,
                enabled=True,
                service_accounts_enabled=True,
                public_client=True,
            )
            params.update(kwargs)
            return iam.clients.add(ClientBase.parse_obj(params))

        return _create_client

    def test_no_client_id_error(self, iam):
        with pytest.raises(authz.ClientNotFound):
            authz.setup_service_account(
                iam,
                "foo-bar",
                config=schemas.ServiceAccountConfig(attributes={}, roles=[], scopes=[]),
            )

    def test_public_client_error(self, iam, create_client):
        client = create_client(public_client=True)
        with pytest.raises(authz.BadClient):
            authz.setup_service_account(
                iam,
                client.client_id,
                config=schemas.ServiceAccountConfig(attributes={}, roles=[], scopes=[]),
            )

    def test_bearer_only_client_error(self, iam):
        client_id = str(uuid.uuid4())
        iam.clients.add(
            ClientBase(
                client_id=client_id,
                enabled=True,
                service_accounts_enabled=True,
                public_client=True,
            )
        )
        with pytest.raises(authz.BadClient):
            authz.setup_service_account(
                iam,
                client_id,
                config=schemas.ServiceAccountConfig(attributes={}, roles=[], scopes=[]),
            )

    def test_token_attributes(self, kc, create_dummy_role, create_dummy_scope):
        client_pk = kc.clients.create({"serviceAccountsEnabled": False})
        client_data = kc.clients.get(client_pk)
        create_dummy_role("foo-role")
        create_dummy_role("bar-role")
        create_dummy_scope("baz-scope")

        authz.setup_service_account(
            KeycloakIAM(kc),
            client_data["clientId"],
            config=schemas.ServiceAccountConfig(
                attributes={"foo": ["bla-bla"]},
                roles=["foo-role", "bar-role"],
                scopes=["baz-scope"],
            ),
        )

        client_secret_data = kc.clients.get_secret(client_pk)
        client_secret = client_secret_data["value"]
        access_token = kc.get_pat(
            client_id=client_data["clientId"], client_secret=client_secret
        )
        token_payload = kc.introspect_token(access_token)
        assert "foo-role" in token_payload["realm_access"]["roles"]
        assert "bar-role" in token_payload["realm_access"]["roles"]
        assert "baz-scope" in token_payload["scope"], token_payload


class TestCreateServiceAccount:
    def test_created_client_properties(
        self,
        kc,
        root_organization,
    ):
        service_account_in = schemas.ServiceAccountCreate(
            name="new account", permissions=["contacts"]
        )
        iam = KeycloakIAM(kc)
        service_account = authz.create_service_account(
            iam,
            service_account_in,
            root_organization,
        )
        client = kc.clients.get_by_client_id(service_account.client_id)
        expected = dict(
            enabled=True,
            publicClient=False,
            serviceAccountsEnabled=True,
            directAccessGrantsEnabled=False,
            standardFlowEnabled=False,
            fullScopeAllowed=False,
            clientAuthenticatorType="client-secret",
        )
        assert client.items() >= expected.items()
        assert "contacts" in client["optionalClientScopes"]

    def test_created_client_access_token_claims(
        self,
        iam,
        root_organization,
    ):
        service_account_in = schemas.ServiceAccountCreate(
            name="new account", permissions=["contacts"]
        )
        service_account = authz.create_service_account(
            iam,
            service_account_in,
            root_organization,
        )
        response = iam.clients.ka.client.post(
            url=iam.clients.ka.get_realm_url(URL_OIDC_TOKEN),
            data=dict(
                grant_type="client_credentials",
                client_id=service_account.client_id,
                client_secret=service_account.client_secret,
                scope="contacts",
            ),
        )
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )

        access_token = response.json()["access_token"]
        payload = deps.introspect_token(access_token)

        assert payload["email_verified"]
        assert payload.get("organization") == dict(
            id=root_organization.id,
            type=root_organization.type.value,
        )
        scopes = payload["scope"].split(" ")
        assert set(service_account_in.permissions).issubset(scopes), scopes
