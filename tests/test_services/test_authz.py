import uuid

import pytest
from pydantic import AnyHttpUrl, parse_obj_as
from yarl import URL

import app.services
from app import schemas
from app.adapters.iam.in_memory import InMemoryIAM
from app.adapters.iam.schemas import FederatedIdentity, PasswordPolicy, User, UserCreate
from app.core.config import settings
from app.enums import OrganizationRoleEnum, OrganizationTypeEnum
from app.schemas.organization import OrganizationNameStr
from app.services import authz, authz_password, org_management


@pytest.fixture
def iam(in_memory_iam) -> InMemoryIAM:
    return in_memory_iam


class TestPreAuthorizeUrl:
    @pytest.fixture
    def create_user(self, iam):
        def _create(**kwargs) -> User:
            payload = dict(
                username=str(uuid.uuid4()),
                email=f"{uuid.uuid4().hex[:10]}@example.com",
                enabled=True,
                email_verified=True,
            )
            payload.update(kwargs)
            return iam.users.add(UserCreate.parse_obj(payload))

        return _create

    @pytest.fixture
    def create_organization_member(self, iam, db, create_user):
        def _create(**kwargs) -> User:
            user = create_user(**kwargs)
            org = org_management.create_organization(
                db,
                iam,
                organization_in=schemas.OrganizationCreate(
                    name=OrganizationNameStr("foo")
                ),
                org_type=OrganizationTypeEnum.DISTRIBUTOR,
            )
            organization_db = org_management.get_object(
                db, org_management.Organization, False, id=org.id
            )
            org_management.add_user_to_organization(
                iam,
                organization=organization_db,
                user_id=user.id,
                organization_role=OrganizationRoleEnum.DISTRIBUTOR_USER,
            )
            return user

        return _create

    @pytest.fixture()
    def make_action_token_url(self):
        def _make_url(**kwargs):
            action_token = authz.PreAuthTokenCodec(secret="secret").encode(kwargs)
            return parse_obj_as(AnyHttpUrl, f"https://foo.bar/?key={action_token}")

        return _make_url

    def test_if_user_doesnt_exist_then_external_landing(
        self, iam, db, make_action_token_url
    ):
        email = f"{uuid.uuid4().hex[:10]}@example.com"
        action_token_url = make_action_token_url(eml=email)
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token=action_token_url)),
        )
        assert url == authz.build_external_auth_url(email)

    def test_if_user_disabled_then_external_landing(
        self, iam, db, create_user, make_action_token_url
    ):
        user = create_user(enabled=False, email_verified=True)
        action_token_url = make_action_token_url(eml=user.email)
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token=action_token_url)),
        )
        assert url == authz.build_external_auth_url(user.email)

    def test_if_user_email_not_verified_then_external_landing(
        self, iam, db, create_user, make_action_token_url
    ):
        user = create_user(enabled=True, email_verified=False)
        action_token_url = make_action_token_url(eml=user.email)
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token=action_token_url)),
        )
        assert url == authz.build_external_auth_url(user.email)

    def test_if_user_not_a_member_of_organization_then_external_landing(
        self,
        iam,
        db,
        create_user,
        make_action_token_url,
    ):
        user = create_user(enabled=True, email_verified=True)
        action_token_url = make_action_token_url(eml=user.email)
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token=action_token_url)),
        )
        assert url == authz.build_external_auth_url(user.email)

    @pytest.mark.parametrize(
        "email,login_hint",
        [
            ("<EMAIL>", "<EMAIL>"),
            (f"two@{settings.EXTERNAL_IDP_DOMAIN_TO_REMOVE}", "two"),
            (f"Three@{settings.EXTERNAL_IDP_DOMAIN_TO_REMOVE.swapcase()}", "three"),
        ],
    )
    def test_login_hint_transformation_for_unregistered_user(
        self, iam, db, create_user, make_action_token_url, email, login_hint
    ):
        user = create_user(email=email)
        action_token_url = make_action_token_url(eml=user.email)
        url = authz.pre_authorize_url(
            iam, db, schemas.PreAuthContext(oauth_query=dict(token=action_token_url))
        )
        assert URL(url).query.get("login_hint") == login_hint

    def test_if_user_enabled_and_org_member_then_action_token_url(
        self, create_organization_member, iam, db, make_action_token_url
    ):
        user = create_organization_member()
        action_token_url = make_action_token_url(eml=user.email)
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token=action_token_url)),
        )
        assert url == authz.build_auth_url_for_user(
            user.id, user.email, None, action_token_url
        )

    @pytest.mark.parametrize(
        "email,idp,login_hint",
        [
            ("<EMAIL>", "dummy-idp", "<EMAIL>"),
            ("<EMAIL>", settings.EXTERNAL_IDP, "<EMAIL>"),
            (
                f"two@{settings.EXTERNAL_IDP_DOMAIN_TO_REMOVE}",
                settings.EXTERNAL_IDP,
                "two",
            ),
            (
                f"two@{settings.EXTERNAL_IDP_DOMAIN_TO_REMOVE}",
                "dummy-idp",
                f"two@{settings.EXTERNAL_IDP_DOMAIN_TO_REMOVE}",
            ),
            (
                f"Three@{settings.EXTERNAL_IDP_DOMAIN_TO_REMOVE.swapcase()}",
                settings.EXTERNAL_IDP,
                "three",
            ),
        ],
    )
    def test_login_hint_for_registered_user(
        self,
        create_organization_member,
        iam,
        db,
        make_action_token_url,
        email,
        idp,
        login_hint,
    ):
        user = create_organization_member(email=email)
        user.set_identity_provider(idp)
        iam.users.update(user.to_update())
        action_token_url = make_action_token_url(eml=user.email)
        url = authz.pre_authorize_url(
            iam, db, schemas.PreAuthContext(oauth_query=dict(token=action_token_url))
        )
        token = URL(url).query["app-token"]
        payload = authz.PreAuthTokenCodec.from_b64_secret(
            settings.PRE_AUTH_TOKEN_SECRET
        ).decode(token)
        assert payload.get("login_hint") == login_hint

    def test_app_token_without_idp(
        self, create_organization_member, iam, db, make_action_token_url
    ):
        user = create_organization_member()
        action_token_url = make_action_token_url(eml=user.email)
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token=action_token_url)),
        )
        token = URL(url).query["app-token"]
        payload = authz.PreAuthTokenCodec.from_b64_secret(
            settings.PRE_AUTH_TOKEN_SECRET
        ).decode(token)
        assert payload == {"sub": str(user.id), "login_hint": user.email}

    def test_app_token_with_idp(
        self, create_organization_member, iam, db, make_action_token_url
    ):
        user = create_organization_member()
        user.set_identity_provider("okta")
        iam.users.update(user.to_update())
        action_token_url = make_action_token_url(eml=user.email)
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token=action_token_url)),
        )
        assert url == authz.build_auth_url_for_user(
            user.id, user.email, "okta", action_token_url
        )

    def test_app_token_with_idp_from_federated_identities(
        self, create_organization_member, iam, db, make_action_token_url
    ):
        """
        When the identity provider attribute missing.
        Then get the first configured in Keycloak and set it to the attribute.
        """
        user = create_organization_member()
        iam.users.add_federated_identity(
            user.id,
            FederatedIdentity(identity_provider="okta", user_id="foo", user_name="bar"),
        )
        iam.users.add_federated_identity(
            user.id,
            FederatedIdentity(
                identity_provider="auth0", user_id="foo", user_name="bar"
            ),
        )
        action_token_url = make_action_token_url(eml=user.email)
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token=action_token_url)),
        )
        assert url == authz.build_auth_url_for_user(
            user.id, user.email, "okta", action_token_url
        )

    def test_if_bad_keycloak_token_then_landing(
        self,
        iam,
        db,
    ):
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token="https://foo.bar/?key=baz")),
        )
        assert url == settings.LANDING_PAGE_URL

    def test_if_no_email_in_keycloak_action_token_then_landing(
        self, iam, db, make_action_token_url
    ):
        action_token_url = make_action_token_url()
        url = authz.pre_authorize_url(
            iam,
            db,
            schemas.PreAuthContext(oauth_query=dict(token=action_token_url)),
        )
        assert url == settings.LANDING_PAGE_URL


def test_get_password_policies(iam):
    iam.password_policies.configure(
        [
            PasswordPolicy(id="length", value=8),
            PasswordPolicy(id="notUsername", value=None),
            PasswordPolicy(id="regexPattern", value="[^ ]+"),
        ]
    )
    policies = app.services.authz.get_password_policies(iam)
    expected = [
        authz_password.LengthPasswordPolicy(8),
        authz_password.NotUsernamePasswordPolicy(None),
        authz_password.RegexPatternPasswordPolicy("[^ ]+"),
    ]
    assert policies == expected


@pytest.mark.parametrize(
    "context",
    [
        {},
        {"email": "<EMAIL>"},
        {"username": "bob"},
        {"username": "bob", "email": "<EMAIL>"},
    ],
)
def test_generate_password(iam, context):
    policies = [
        authz_password.LengthPasswordPolicy(10),
        authz_password.MaxLengthPasswordPolicy(12),
        authz_password.DigitsPasswordPolicy(4),
        authz_password.SpecialCharsPasswordPolicy(1),
        authz_password.LowerCasePasswordPolicy(3),
        authz_password.UpperCasePasswordPolicy(2),
        authz_password.NotUsernamePasswordPolicy(None),
        authz_password.NotEmailPasswordPolicy(None),
        authz_password.RegexPatternPasswordPolicy("[^ ]+"),
        authz_password.HashAlgorithmPasswordPolicy("pbkdf2-sha256"),
        authz_password.HashIterationsPasswordPolicy(10),
        authz_password.HistoryPasswordPolicy(3),
        authz_password.ForceExpiredPasswordChange(1),
        # this policy requires mounting of the file to docker image
        # authz_password.BlacklistPasswordPolicy('blacklist.txt'),
    ]

    registry = authz_password.BasePasswordPolicy._registry
    # check that all policies(except for blacklist) are covered
    assert len(policies) == len(registry) - 1, [
        v for v in registry.values() if v not in list(map(type, policies))
    ]
    iam.password_policies.configure(
        [PasswordPolicy(id=p.id, value=p.value) for p in policies]
    )

    authz.generate_password(iam, **context)
