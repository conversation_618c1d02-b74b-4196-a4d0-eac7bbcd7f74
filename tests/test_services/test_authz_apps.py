from operator import attrgetter

import pytest

from app.adapters.iam.in_memory import InMemoryIAM
from app.services.authz_apps import (
    APP_MANAGEMENT_SCOPE_NAME,
    APP_ROLE_NAME,
    create_app_management_scope,
    create_application_role,
    get_app_management_scope,
    get_application_role,
)


@pytest.fixture
def iam(in_memory_iam) -> InMemoryIAM:
    return in_memory_iam


def test_role_created(iam):
    create_application_role(iam)
    role = iam.roles.get(APP_ROLE_NAME)
    assert role is not None


def test_create_app_management_client_scope(iam):
    create_application_role(iam)

    create_app_management_scope(iam)
    scope = get_app_management_scope(iam)
    assert scope is not None
    assert scope.name == APP_MANAGEMENT_SCOPE_NAME
    assert len(scope.protocol_mappers) == 2
    mapper_names = list(map(attrgetter("name"), scope.protocol_mappers))
    assert "Application ID" in mapper_names
    assert "application-management audience" in mapper_names
    assert scope.attributes == {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "false",
    }

    roles = iam.client_scopes.get_roles(APP_MANAGEMENT_SCOPE_NAME)
    assert get_application_role(iam).name in roles
