import pytest

from app.services import authz_password


class TestPasswordGenerator:
    @pytest.mark.parametrize(
        "default,min_length,max_length,expected",
        [
            (1, None, None, 1),
            (1, 2, None, 2),
            (11, None, 10, 10),
            (9, None, 10, 9),
            (3, 5, None, 5),
            (1, 2, 10, 2),
            (2, 2, 10, 2),
            (3, 2, 10, 3),
            (11, 2, 10, 10),
        ],
    )
    def test_get_length(self, default, min_length, max_length, expected):
        actual = authz_password.PasswordGenerator.get_length(
            default, min_length, max_length
        )
        assert actual == expected

    @pytest.mark.parametrize(
        "default,min_length,max_length",
        [(0, None, None), (-1, None, None), (18, 19, 10)],
    )
    def test_get_length_error(self, default, min_length, max_length):
        with pytest.raises(ValueError):
            authz_password.PasswordGenerator.get_length(default, min_length, max_length)
