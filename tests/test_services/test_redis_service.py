import json
from unittest.mock import <PERSON>Mock, patch

import pytest
from redis.exceptions import RedisError

from src.app.common.ordering import OrderDirection, Ordering
from src.app.common.pagination import Pagination
from src.app.db.redis_service import RedisService


@pytest.fixture
def redis_service():
    with patch("src.app.db.redis_service.redis.Redis"):
        service = RedisService()
        service.redis_client = MagicMock()
        yield service


@pytest.fixture
def user_hash_data():
    """Fixture providing sample Redis user hash data in bytes format"""
    return {
        b"enabled": b"True",
        b"roles": b'["fd02dcab-9d50-4608-9759-6eed0cf83631"]',
        b"lastName": b"test1",
        b"email": b"<EMAIL>",
        b"attributes": b"{}",
        b"groups": b"9c599b33-1330-49b9-a46a-9930322919bf",
        b"username": b"<EMAIL>",
        b"timestamp": b"1747981431465",
        b"firstName": b"client",
        b"emailVerified": b"True",
        b"id": b"4d1ac803-56b0-427c-8b5e-c9d5786f6392",
    }


@pytest.fixture
def user_data_dict():
    """Fixture providing sample user data in Python dict format"""
    return {
        "user1": {
            "enabled": "True",
            "roles": ["fd02dcab-9d50-4608-9759-6eed0cf83631"],
            "lastName": "test1",
            "email": "<EMAIL>",
            "attributes": {},
            "groups": "9c599b33-1330-49b9-a46a-9930322919bf",
            "username": "<EMAIL>",
            "timestamp": 1747981431465,
            "firstName": "client",
            "emailVerified": "True",
            "id": "4d1ac803-56b0-427c-8b5e-c9d5786f6392",
        },
        "user2": {
            "enabled": "True",
            "roles": ["fd02dcab-9d50-4608-9759-6eed0cf83631"],
            "lastName": "Smith",
            "email": "<EMAIL>",
            "attributes": {},
            "groups": "9c599b33-1330-49b9-a46a-9930322919bf",
            "username": "<EMAIL>",
            "timestamp": 1747981431466,
            "firstName": "Jane",
            "emailVerified": "True",
            "id": "5e2bc904-67c1-438d-9b6f-d8a7f5e7e4c3",
        },
        "user3": {
            "enabled": "True",
            "roles": ["fd02dcab-9d50-4608-9759-6eed0cf83631"],
            "lastName": "Johnson",
            "email": "<EMAIL>",
            "attributes": {},
            "groups": "9c599b33-1330-49b9-a46a-9930322919bf",
            "username": "<EMAIL>",
            "timestamp": 1747981431467,
            "firstName": "Bob",
            "emailVerified": "True",
            "id": "6f3cd015-78d2-449e-8a7g-e9b8f6g7h5i4",
        },
    }


@pytest.fixture
def multiple_user_hash_data():
    """Fixture providing multiple user hash data for pipeline returns"""
    return [
        {
            b"enabled": "True",
            b"roles": b'["fd02dcab-9d50-4608-9759-6eed0cf83631"]',
            b"lastName": b"test1",
            b"email": b"<EMAIL>",
            b"attributes": b"{}",
            b"groups": b"9c599b33-1330-49b9-a46a-9930322919bf",
            b"username": b"<EMAIL>",
            b"timestamp": b"1747981431465",
            b"firstName": b"client",
            b"emailVerified": "True",
            b"id": b"4d1ac803-56b0-427c-8b5e-c9d5786f6392",
        },
        {
            b"enabled": "True",
            b"roles": b'["fd02dcab-9d50-4608-9759-6eed0cf83631"]',
            b"lastName": b"Smith",
            b"email": b"<EMAIL>",
            b"attributes": b"{}",
            b"groups": b"9c599b33-1330-49b9-a46a-9930322919bf",
            b"username": b"<EMAIL>",
            b"timestamp": b"1747981431466",
            b"firstName": b"Jane",
            b"emailVerified": "True",
            b"id": b"5e2bc904-67c1-438d-9b6f-d8a7f5e7e4c3",
        },
        {
            b"enabled": "True",
            b"roles": b'["fd02dcab-9d50-4608-9759-6eed0cf83631"]',
            b"lastName": b"Johnson",
            b"email": b"<EMAIL>",
            b"attributes": b"{}",
            b"groups": b"9c599b33-1330-49b9-a46a-9930322919bf",
            b"username": b"<EMAIL>",
            b"timestamp": b"1747981431467",
            b"firstName": b"Bob",
            b"emailVerified": "True",
            b"id": b"6f3cd015-78d2-449e-8a7g-e9b8f6g7h5i4",
        },
    ]


class TestRedisService:
    def test_try_json_loads_valid_json(self, redis_service):
        # valid JSON
        value = b'{"firstName": "John", "lastName": "Doe"}'
        result = redis_service._try_json_loads(value)
        assert result == {"firstName": "John", "lastName": "Doe"}

    def test_try_json_loads_invalid_json(self, redis_service):
        # invalid JSON
        value = b"not a json"
        result = redis_service._try_json_loads(value)
        assert result == "not a json"

    def test_deserialize_none(self, redis_service):
        # Test with None
        result = redis_service._deserialize(None)
        assert result is None

    def test_deserialize_set(self, redis_service):
        # Test with set of bytes
        data = {
            b"6f3cd015-78d2-449e-8a7g-e9b8f6g7h5i4",
            b"5e2bc904-67c1-438d-9b6f-d8a7f5e7e4c3",
            b"4d1ac803-56b0-427c-8b5e-c9d5786f6392",
        }
        result = redis_service._deserialize(data)
        assert result == {
            "6f3cd015-78d2-449e-8a7g-e9b8f6g7h5i4",
            "5e2bc904-67c1-438d-9b6f-d8a7f5e7e4c3",
            "4d1ac803-56b0-427c-8b5e-c9d5786f6392",
        }

    def test_deserialize_dict(self, redis_service, user_hash_data):
        # Test with dict of bytes
        # Convert user_hash_data to a string representation first
        user_data_str = {
            k.decode("utf-8"): v.decode("utf-8") for k, v in user_hash_data.items()
        }
        data = {b"": json.dumps(user_data_str).encode()}
        result = redis_service._deserialize(data)
        assert "" in result
        assert isinstance(result[""], dict)

    def test_deserialize_bytes(self, redis_service):
        # Test with bytes
        data = b'{"roles": ["fd02dcab-9d50-4608-9759-6eed0cf83631"]}'
        result = redis_service._deserialize(data)
        assert result == {"roles": ["fd02dcab-9d50-4608-9759-6eed0cf83631"]}

    def test_get_user_success(self, redis_service, user_hash_data):
        redis_service.redis_client.hgetall.return_value = user_hash_data
        result = redis_service.get_user("4d1ac803-56b0-427c-8b5e-c9d5786f6392")
        redis_service.redis_client.hgetall.assert_called_once_with(
            "users:4d1ac803-56b0-427c-8b5e-c9d5786f6392"
        )
        assert result["firstName"] == "client"
        assert result["lastName"] == "test1"
        assert result["email"] == "<EMAIL>"
        assert result["id"] == "4d1ac803-56b0-427c-8b5e-c9d5786f6392"

    def test_get_user_empty(self, redis_service):
        # user doesn't exist
        redis_service.redis_client.hgetall.return_value = {}
        result = redis_service.get_user("6d1ac803-56b0-427c-8b5e-c9d5786f6392")
        assert result is None

    def test_get_user_redis_error(self, redis_service):
        # Redis error handling
        redis_service.redis_client.hgetall.side_effect = RedisError("Connection error")
        result = redis_service.get_user("4d1ac803-56b0-427c-8b5e-c9d5786f6392")
        assert result is None

    def test_get_sort_key(self, redis_service, user_data_dict):
        redis_field = "email"
        item = ("user1", user_data_dict["user1"])
        reverse = False
        result = redis_service.get_sort_key(item, redis_field, reverse)
        assert result[0] == 1
        assert result[1] == "<EMAIL>"

    def test_get_sort_key_string_field_reverse(self, redis_service, user_data_dict):
        # Test with a string field (email), reverse order
        redis_field = "email"
        item = ("user1", user_data_dict["user1"])
        reverse = True
        result = redis_service.get_sort_key(item, redis_field, reverse)
        # Should return (1, reversed normalized_value)
        assert result[0] == 1
        # The reversed string is not human-readable, just check it's a string
        assert isinstance(result[1], str)
        assert result[1] != "<EMAIL>"

    def test_get_sort_key_numeric_field(self, redis_service, user_data_dict):
        # Test with a numeric field (timestamp)
        redis_field = "timestamp"
        item = ("user1", user_data_dict["user1"])
        reverse = False
        result = redis_service.get_sort_key(item, redis_field, reverse)
        # Should return (0, timestamp as float)
        assert result[0] == 0
        assert result[1] == float(user_data_dict["user1"]["timestamp"])

    def test_get_sort_key_numeric_field_reverse(self, redis_service, user_data_dict):
        # Test with a numeric field (timestamp), reverse order
        redis_field = "timestamp"
        item = ("user1", user_data_dict["user1"])
        reverse = True
        result = redis_service.get_sort_key(item, redis_field, reverse)
        # Should return (0, -timestamp as float)
        assert result[0] == 0
        assert result[1] == -float(user_data_dict["user1"]["timestamp"])

    def test_get_sort_key_missing_field(self, redis_service, user_data_dict):
        # Test with a missing field
        redis_field = "nonexistent"
        item = ("user1", user_data_dict["user1"])
        reverse = False
        result = redis_service.get_sort_key(item, redis_field, reverse)
        # Should return (2, '')
        assert result == (2, "")

    def test_get_all_users_empty(self, redis_service):
        # Test when no users exist
        redis_service.redis_client.keys.return_value = []

        ordering = Ordering(field="firstName", order=OrderDirection.ASC)
        result, total = redis_service.get_all_users(ordering, pagination=None)

        assert result == {}
        assert total == 0

    def test_get_all_users_with_specific_ids(
        self, redis_service, multiple_user_hash_data, user_data_dict
    ):
        # Test getting specific users by ID
        mock_redis = redis_service.redis_client.pipeline.return_value.__enter__
        mock_redis.return_value.execute.return_value = multiple_user_hash_data[:2]
        redis_service._deserialize_hash = MagicMock(
            side_effect=[user_data_dict["user1"], user_data_dict["user2"]]
        )
        ordering = Ordering(field="firstName", order=OrderDirection.ASC)
        result, total = redis_service.get_all_users(
            ordering, pagination=None, user_ids=["user1", "user2"]
        )
        assert len(result) == 2
        assert total == 2

    def test_get_all_users_with_search(self, redis_service, multiple_user_hash_data):
        # Test getting users with search
        redis_service.redis_client.keys.return_value = [
            b"users:user1",
            b"users:user2",
            b"users:user3",
        ]
        mock_redis = redis_service.redis_client.pipeline.return_value.__enter__
        mock_redis.return_value.execute.return_value = multiple_user_hash_data

        # Mock _deserialize_hash to return proper Python objects
        # redis_service._deserialize_hash = MagicMock(
        #     side_effect=[
        #         {
        #             "firstName": "client",
        #             "lastName": "test1",
        #             "email": "<EMAIL>",
        #         },
        #         {
        #             "firstName": "Jane",
        #             "lastName": "Smith",
        #             "email": "<EMAIL>",
        #         },
        #         {
        #             "firstName": "Bob",
        #             "lastName": "Johnson",
        #             "email": "<EMAIL>",
        #         },
        #     ]
        # )
        redis_service._deserialize_hash = MagicMock(
            side_effect=lambda x: {
                (k.decode("utf-8") if isinstance(k, bytes) else k): (
                    v.decode("utf-8") if isinstance(v, bytes) else v
                )
                for k, v in x.items()
            }
        )
        ordering = Ordering(field="firstName", order=OrderDirection.ASC)
        result, total = redis_service.get_all_users(
            ordering, pagination=None, search_value="client"
        )

        assert total == 1  # Only "client test1" matches

    def test_get_all_users_with_pagination(self, redis_service):
        # Test pagination
        user_data_list = [
            {
                b"enabled": b"true",
                b"roles": b'["fd02dcab-9d50-4608-9759-6eed0cf83631"]',
                b"lastName": b"User" + str(i).encode(),
                b"email": f"user{i}@example.com".encode(),
                b"firstName": b"User" + str(i).encode(),
                b"id": f"id-{i}".encode(),
            }
            for i in range(1, 21)
        ]

        redis_service.redis_client.keys.return_value = [
            f"users:user{i}".encode() for i in range(1, 21)
        ]
        mock_redis = redis_service.redis_client.pipeline.return_value.__enter__
        mock_redis.return_value.execute.return_value = user_data_list

        # Mock _deserialize_hash to return proper Python objects
        redis_service._deserialize_hash = MagicMock(
            side_effect=lambda x: {
                (k.decode("utf-8") if isinstance(k, bytes) else k): (
                    v.decode("utf-8") if isinstance(v, bytes) else v
                )
                for k, v in x.items()
            }
        )

        ordering = Ordering(field="firstName", order=OrderDirection.ASC)
        result, total = redis_service.get_all_users(
            ordering, pagination=Pagination(page=2, page_size=5)
        )

        assert len(result) == 5  # 5 items per page
        assert total == 20  # 20 total users

    def test_get_all_users_redis_error(self, redis_service):
        # Test Redis error handling
        redis_service.redis_client.keys.side_effect = RedisError("Connection error")

        ordering = Ordering(field="firstName", order=OrderDirection.ASC)
        result, total = redis_service.get_all_users(ordering, pagination=None)

        assert result == {}
        assert total == 0

    def test_get_group_users_success(self, redis_service):
        # Test successful group users retrieval
        group_users = {b"user1", b"user2", b"user3"}
        redis_service.redis_client.smembers.return_value = group_users

        result = redis_service.get_group_users("group123")

        redis_service.redis_client.smembers.assert_called_once_with("groups:group123")
        assert result == {"user1", "user2", "user3"}

    def test_get_group_users_empty(self, redis_service):
        # Test when group has no users
        redis_service.redis_client.smembers.return_value = set()

        result = redis_service.get_group_users("group123")

        assert result is None

    def test_get_group_users_redis_error(self, redis_service):
        # error handling
        redis_service.redis_client.smembers.side_effect = RedisError("Connection error")

        result = redis_service.get_group_users("group123")

        assert result == {}

    def test_deserialize_hash(self, redis_service, user_hash_data, user_data_dict):
        # Test deserializing hash data

        result = redis_service._deserialize_hash(user_hash_data)

        assert result == user_data_dict["user1"]
