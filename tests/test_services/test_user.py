import uuid
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, call, patch

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status

from app import schemas
from app.adapters.core_api.in_memory import InMemoryCoreAPI
from app.adapters.iam.exc import GroupAlreadyExists
from app.adapters.iam.in_memory import InMemoryIAM
from app.adapters.iam.schemas import User, UserCreate
from app.enums import OrganizationRoleEnum, OrganizationScopeEnum, OrganizationTypeEnum
from app.models import Organization
from app.oso import oso
from app.services import org_management
from app.services.users import (
    add_contact,
    add_user,
    add_user_in_to_organization,
    alias_to_user_identity_provider,
    check_org_role,
    get_by_email,
    get_keycloak_user_response,
    get_user_org,
    get_user_organization_roles,
    get_user_v2,
    send_email,
    validate_identity_provider,
    validate_user_in,
)


@pytest.fixture
def iam(in_memory_iam) -> InMemoryIAM:
    return in_memory_iam


@pytest.fixture()
def core_api() -> InMemoryCoreAPI:
    return InMemoryCoreAPI()


class TestUserService:
    def db():
        return MagicMock()

    def actor():
        return Mock()

    def user_in():
        return Mock(role=OrganizationRoleEnum.CLIENT_ADMIN)

    def org_id():
        return 1

    @pytest.fixture
    def create_user(self, iam):
        def _create(**kwargs) -> User:
            payload = dict(
                username=str(uuid.uuid4()),
                email=f"{uuid.uuid4().hex[:10]}@example.com",
                enabled=True,
                email_verified=True,
            )
            payload.update(kwargs)
            return iam.users.add(UserCreate.parse_obj(payload))

        return _create

    def test_get_by_email_success(self, iam, create_user):
        user = create_user(enabled=False, email_verified=True)
        user_data = {
            "email": user.email,
            "username": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "enabled": False,
            "email_verified": False,
            "attributes": {},
        }

        user_in = UserCreate(**user_data)
        result = get_by_email(iam, user_in)
        assert result.email == user_in.email
        assert result.username == user_in.username
        assert result.first_name == user_in.first_name
        assert result.last_name == user_in.last_name
        assert result.enabled == user.enabled
        assert result.email_verified == user.email_verified
        assert result.attributes == {}
        assert result.id == user.id

    def test_get_by_email_not_found(self, iam):
        # user = create_user(enabled=False, email_verified=True)
        user_data = {
            "email": "<EMAIL>",
            "username": "user_username",
            "first_name": "user_first_name",
            "last_name": "user_last_name",
            "enabled": False,
            "email_verified": False,
            "attributes": {},
        }

        user_in = UserCreate(**user_data)
        result = get_by_email(iam, user_in)
        assert result is None

    @pytest.mark.skip(reason="Not implemented")
    def test_get_by_email_exception_handling(self, iam):
        user_data = {
            "email": "<EMAIL>",
            "username": "user_username",
            "first_name": "user_first_name",
            "last_name": "user_last_name",
            "enabled": False,
            "email_verified": False,
            "attributes": {},
        }
        expected_exception = Exception("Test exception message")
        iam.users.get_by_email.side_effect = expected_exception
        user_in = UserCreate(**user_data)
        with pytest.raises(Exception) as e:
            get_by_email(iam, user_in)

        assert str(e.value) == "Test exception"

    @pytest.mark.skip(reason="Not implemented")
    def test_get_user_org_success(self):
        # Arrange
        db_mock = Mock()
        iam_mock = Mock()
        user_in_mock = Mock()
        user_mock = Mock()
        actor_mock = Mock()

        existing_org_mock = Mock(name="TestOrg")

        org_management.get_user_org.return_value = existing_org_mock
        oso.is_allowed.return_value = False

        # Act
        get_user_org(db_mock, iam_mock, user_in_mock, user_mock, actor_mock)

        # Assert
        org_management.get_user_org.assert_called_once_with(
            db_mock, iam_mock, user_id=user_mock.id
        )
        oso.is_allowed.assert_called_once_with(
            actor_mock, OrganizationScopeEnum.READ, existing_org_mock
        )

    def test_get_user_org_existing_org(self, iam, create_user):
        user = create_user(enabled=False, email_verified=True)
        db_mock = Mock()
        actor_mock = Mock()
        user_data = {
            "email": user.email,
            "username": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "enabled": False,
            "email_verified": False,
            "attributes": {},
        }

        user_in = UserCreate(**user_data)

        org_management.get_user_org.return_value = None

        result = get_user_org(db_mock, iam, user_in, user, actor_mock)
        assert result is None

    @patch("app.services.org_management.get_user_org")
    def test_get_user_org_conflict_already_member(
        self, get_user_org_mock, iam, create_user
    ):
        user = create_user(enabled=False, email_verified=True)
        db_mock = Mock()
        actor_mock = Mock()
        oso = Mock()
        user_data = {
            "email": user.email,
            "username": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "enabled": False,
            "email_verified": False,
            "attributes": {},
        }

        user_in = UserCreate(**user_data)

        organization_data = {
            "type": OrganizationTypeEnum.DISTRIBUTOR,
            "name": "Sample Organization",
            "parent_id": 1,
            "parent_type": OrganizationTypeEnum.PARTNER,
            "external_id": "sample_external_id",
            "group_id": "sample_group_id",
        }

        organization_instance = Organization(**organization_data)

        get_user_org_mock.return_value = organization_instance
        oso.is_allowed.return_value = True

        msg = "The user with the email address"
        with pytest.raises(HTTPException) as context:
            get_user_org(db_mock, iam, user_in, user, actor_mock)

        assert context.value.status_code == status.HTTP_409_CONFLICT
        assert msg in context.value.detail
        assert user_in.email in context.value.detail

    @patch("app.services.org_management.get_user_org")
    def test_get_user_org_conflict_already_member_with_organization(
        self, get_user_org_mock, iam, create_user
    ):
        user = create_user(enabled=False, email_verified=True)
        db_mock = Mock()
        actor_mock = Mock()
        oso = Mock()
        user_data = {
            "email": user.email,
            "username": user.username,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "enabled": False,
            "email_verified": False,
            "attributes": {},
        }

        user_in = UserCreate(**user_data)

        organization_data = {
            "type": OrganizationTypeEnum.DISTRIBUTOR,
            "name": "Sample Organization",
            "parent_id": 1,
            "parent_type": OrganizationTypeEnum.PARTNER,
            "external_id": "sample_external_id",
            "group_id": "sample_group_id",
        }

        organization_instance = Organization(**organization_data)

        get_user_org_mock.return_value = organization_instance
        oso.is_allowed.return_value = True

        msg = "The user with the email address"
        with pytest.raises(HTTPException) as context:
            get_user_org(db_mock, iam, user_in, user, actor_mock)

        assert context.value.status_code == status.HTTP_409_CONFLICT
        assert msg in context.value.detail
        assert user_in.email in context.value.detail

    @patch("app.services.users.org_management.add_user_to_organization_v2")
    def test_add_user_to_organization_with_user(
        self, mock_add_user_to_organization, iam
    ):
        target_org_mock = Mock()
        user_mock = Mock(id=123)
        user_in_mock = Mock(roles=["member"])

        add_user_in_to_organization(iam, target_org_mock, user_mock, user_in_mock)

        # Assert that the function was called twice with 2 roles
        expected_calls = [
            call(
                iam,
                organization=target_org_mock,
                user_id=user_mock.id,
                organization_role=user_in_mock.roles,
            )
        ]
        mock_add_user_to_organization.assert_has_calls(expected_calls)

    @patch("app.services.users.org_management.add_user_to_organization_v2")
    def test_add_user_to_organization_exception(
        self, mock_add_user_to_organization, iam, create_user
    ):
        user = create_user(enabled=False, email_verified=True)
        target_org_mock = Mock()
        user_in_mock = Mock(roles=["member"])
        mock_add_user_to_organization.side_effect = GroupAlreadyExists("Test error")

        with pytest.raises(HTTPException) as context:
            add_user_in_to_organization(iam, target_org_mock, user, user_in_mock)

        assert context.value.status_code == status.HTTP_404_NOT_FOUND
        assert context.value.detail == "error in add user into org"
        mock_add_user_to_organization.assert_called_once_with(
            iam,
            organization=target_org_mock,
            user_id=user.id,
            organization_role=user_in_mock.roles,
        )

    def test_add_user_to_organization_without_user(self):
        iam_mock = Mock()
        target_org_mock = Mock()
        user_mock = None
        user_in_mock = Mock(role="member")

        add_user_in_to_organization(iam_mock, target_org_mock, user_mock, user_in_mock)

    @patch("app.services.users.org_management.fetch_user_org_roles")
    def test_get_user_organization_roles_success(self, mock_fetch_user_org_roles):
        iam_mock = Mock()
        user_mock = Mock(id=123)
        expected_roles = [
            OrganizationRoleEnum.CLIENT_ADMIN,
            OrganizationRoleEnum.DISTRIBUTOR_ADMIN,
        ]

        mock_fetch_user_org_roles.return_value = expected_roles

        result = get_user_organization_roles(iam_mock, user_mock)

        assert result == expected_roles
        mock_fetch_user_org_roles.assert_called_once_with(iam_mock, user_mock.id)

    @patch("app.services.users.org_management.fetch_user_org_roles")
    def test_get_user_organization_roles_exception(self, mock_fetch_user_org_roles):
        iam_mock = Mock()
        user_mock = Mock(id=123)

        mock_fetch_user_org_roles.side_effect = Exception("Test error")

        with pytest.raises(Exception) as context:
            get_user_organization_roles(iam_mock, user_mock)

        assert context.value.args[0] == "Test error"
        mock_fetch_user_org_roles.assert_called_once_with(iam_mock, user_mock.id)

    def test_get_user_v2_success(self, create_user):
        user = create_user(enabled=False, email_verified=True)

        organization_data = {
            "id": 1,
            "parent_id": 123,
            "type": OrganizationTypeEnum.DISTRIBUTOR,
            "name": "Sample Organization",
        }
        organization_obj = schemas.OrganizationSimple(**organization_data)

        result = get_user_v2(user, organization_obj)

        expected_user_v2 = schemas.UserV2(
            id=user.id,
            name="John Doe",
            email=user.email,
            username=user.username,
            organization=schemas.OrganizationSimple.from_orm(organization_obj),
        )
        assert result.email == expected_user_v2.email
        assert result.first_name == expected_user_v2.first_name
        assert result.id == expected_user_v2.id
        assert result.last_name == expected_user_v2.last_name
        assert result.organization == expected_user_v2.organization
        assert result.username == expected_user_v2.username

    @patch("app.services.users.schemas.UserV2")
    def test_get_user_v2_exception(self, mock_user_v2, create_user):
        user = create_user(enabled=False, email_verified=True)

        target_org_mock = Mock()
        mock_user_v2.side_effect = Exception("Test error")
        with pytest.raises(Exception) as context:
            get_user_v2(user, target_org_mock)

        assert context.value.args[0] == status.HTTP_404_NOT_FOUND
        assert context.value.args[1] == "error in get user v2"

    @pytest.mark.skip(reason="Not implemented")
    def test_add_contact_success(self, core_api):
        user_v2_mock = Mock()
        add_contact(core_api, user_v2_mock)
        core_api.add_contact.assert_called_once_with(user_v2_mock)

    @pytest.mark.skip(reason="Not implemented")
    def test_add_contact_exception(self, core_api):
        user_v2_mock = Mock()
        core_api.side_effect = Exception("Test error")

        with pytest.raises(Exception) as context:
            add_contact(core_api, user_v2_mock)

        self.assertEqual(str(context.exception), "Test error")

    def test_get_keycloak_user_response_success(self, create_user):
        user = create_user(enabled=False, email_verified=True)

        roles_mock = ["admin", "user"]
        idp_data = {
            "alias": "some_alias",
            "display_name": "Some Display Name",
        }

        user_idp = schemas.KeycloakIdentityProvider(**idp_data)

        result = get_keycloak_user_response(user, roles_mock, user_idp)

        expected_response = schemas.KeycloakUser(
            id=user.id,
            email=user.email,
            username=user.username,
            firstName="John",
            lastName="Doe",
            enabled=True,
            emailVerified=True,
            createdTimestamp=123456789,
            roles=["admin", "user"],
            identityProvider=None,
        )
        assert result.email == expected_response.email
        assert result.emailVerified is True
        assert result.username == expected_response.username
        assert result.roles == roles_mock

    @patch("app.services.users.schemas.KeycloakUser")
    def test_get_keycloak_user_response_exception(self, mock_keycloak_user):
        user_mock = Mock()
        roles_mock = ["admin", "user"]
        user_idp_mock = "keycloak"
        mock_keycloak_user.side_effect = Exception("Test error")
        with pytest.raises(Exception) as context:
            get_keycloak_user_response(user_mock, roles_mock, user_idp_mock)

        assert context.value.args[0] == status.HTTP_404_NOT_FOUND
        assert context.value.args[1] == "error in get user response"

    @patch("app.services.users.authz.generate_password")
    @patch("app.services.users._render_password_email")
    def test_send_email_success(
        self, mock_render_email, mock_generate_password, iam, create_user, core_api
    ):
        user = create_user(enabled=False, email_verified=True)
        mock_generate_password.return_value = "test_password"
        mock_render_email.return_value = "test_message"
        send_email(iam, core_api, user)
        mock_generate_password.assert_called_once_with(iam, email=user.email)
        mock_render_email.assert_called_once_with(user, "test_password")

    @patch(
        "app.services.users.authz.generate_password",
    )
    def test_send_email_password_generation_failure(
        self, mock_generate_password, iam, create_user, core_api
    ):
        user = create_user(enabled=False, email_verified=True)
        mock_generate_password.side_effect = Exception("error_send_email")
        with pytest.raises(HTTPException) as context:
            send_email(iam, core_api, user)
        assert context.value.args[0] == status.HTTP_409_CONFLICT
        assert context.value.args[1] == "error_send_email"

    @patch("app.services.users.alias_to_identity_provider")
    def test_alias_to_user_identity_provider_success(
        self, mock_alias_to_identity_provider, iam, create_user
    ):
        user = create_user(enabled=False, email_verified=True)

        idp_data = {
            "alias": "Okta",
            "displayName": "Okta Identity Provider",
        }

        user_idp = schemas.KeycloakIdentityProvider(**idp_data)
        mock_alias_to_identity_provider.return_value = user_idp

        result = alias_to_user_identity_provider(iam, user)

        # Assert
        mock_alias_to_identity_provider.assert_called_once_with(
            iam, user.identity_provider
        )
        assert result.alias == "Okta"
        assert result.displayName == "Okta Identity Provider"

    @patch("app.services.users.alias_to_identity_provider", return_value=None)
    def test_alias_to_user_identity_provider_none_returned(
        self, mock_alias_to_identity_provider, iam, create_user
    ):
        user = create_user(enabled=False, email_verified=True)
        result = alias_to_user_identity_provider(iam, user)

        # Assert
        mock_alias_to_identity_provider.assert_called_once_with(
            iam, user.identity_provider
        )
        assert result is None

    @patch("app.services.users.get_object_or_404")
    @patch("app.services.users.oso.authorize")
    @patch("app.services.users.check_org_role")
    def test_validate_user_in_success(
        self, mock_check_org_role, mock_authorize, mock_get_object_or_404
    ):
        user_in = Mock(roles=[OrganizationRoleEnum.CLIENT_ADMIN])
        org_id = 1
        db = MagicMock()
        target_org = MagicMock()
        actor_mock = Mock()
        mock_get_object_or_404.return_value = target_org
        result = validate_user_in(db, actor_mock, org_id, user_in)
        mock_get_object_or_404.assert_called_once_with(
            db, Organization, False, id=org_id
        )
        mock_authorize.assert_called_once_with(
            actor_mock, OrganizationScopeEnum.UPDATE, target_org
        )
        mock_check_org_role.assert_called_once_with(
            target_org, OrganizationRoleEnum.CLIENT_ADMIN
        )
        assert result == target_org

    @patch("app.services.users.get_object_or_404")
    @patch("app.services.users.oso.authorize")
    @patch("app.services.users.check_org_role")
    def test_validate_user_in_success_with_custom_role(
        self, mock_check_org_role, mock_authorize, mock_get_object_or_404
    ):
        org_id = 1
        db = MagicMock()
        target_org = MagicMock()
        actor_mock = Mock()
        mock_get_object_or_404.return_value = target_org

        user_in = Mock(roles=[OrganizationRoleEnum.CLIENT_ADMIN, "Custom role"])
        result = validate_user_in(db, actor_mock, org_id, user_in)
        mock_get_object_or_404.assert_called_once_with(
            db, Organization, False, id=org_id
        )
        mock_authorize.assert_called_once_with(
            actor_mock, OrganizationScopeEnum.UPDATE, target_org
        )
        mock_check_org_role.assert_called_once_with(
            target_org, OrganizationRoleEnum.CLIENT_ADMIN
        )

        assert result == target_org

    @patch("app.services.users.get_object_or_404")
    @patch("app.services.users.oso.authorize")
    def test_validate_user_in_more_than_one_default_role(
        self, mock_authorize, mock_get_object_or_404
    ):
        user_in = Mock(
            roles=[
                OrganizationRoleEnum.CLIENT_ADMIN,
                OrganizationRoleEnum.DISTRIBUTOR_ADMIN,
            ]
        )
        org_id = 1
        db = MagicMock()
        target_org = MagicMock()
        actor_mock = Mock()
        mock_get_object_or_404.return_value = target_org
        with pytest.raises(HTTPException) as context:
            validate_user_in(db, actor_mock, org_id, user_in)

        assert context.value.status_code == status.HTTP_400_BAD_REQUEST
        assert context.value.detail == "There should be exactly one default role."

    @patch("app.services.users.get_object_or_404")
    @patch("app.services.users.oso.authorize")
    def test_validate_user_in_no_default_role(
        self, mock_authorize, mock_get_object_or_404
    ):
        user_in = Mock(roles=["string"])
        org_id = 1
        db = MagicMock()
        target_org = MagicMock()
        actor_mock = Mock()
        mock_get_object_or_404.return_value = target_org
        with pytest.raises(HTTPException) as context:
            validate_user_in(db, actor_mock, org_id, user_in)

        assert context.value.status_code == status.HTTP_400_BAD_REQUEST
        assert context.value.detail == "There should be exactly one default role."

    @patch(
        "app.services.users.get_object_or_404",
        side_effect=HTTPException(status_code=status.HTTP_404_NOT_FOUND),
    )
    def test_validate_user_in_org_not_found(self, mock_get_object_or_404):
        db = MagicMock()
        with pytest.raises(HTTPException) as context:
            validate_user_in(db, self.actor, self.org_id, self.user_in)
        assert context.value.status_code == status.HTTP_404_NOT_FOUND
        assert context.value.detail == "Not Found"

    @patch("app.services.users.oso.authorize")
    def test_validate_user_in_forbidden_error(self, mock_authorize):
        mock_authorize.side_effect = oso.forbidden_error
        db = MagicMock()
        with pytest.raises(HTTPException) as context:
            validate_user_in(db, self.actor, self.org_id, self.user_in)
        assert context.value.status_code == status.HTTP_403_FORBIDDEN
        assert context.value.detail == "Forbidden"

    @patch("app.services.users.check_org_role")
    def test_validate_user_in_role_check_failure(self, mock_check_org_role):
        mock_check_org_role.side_effect = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND
        )
        db = MagicMock()
        with pytest.raises(HTTPException) as context:
            validate_user_in(db, self.actor, self.org_id, self.user_in)
        assert context.value.status_code == status.HTTP_404_NOT_FOUND
        assert context.value.detail == "Not Found"

    def test_check_org_role_does_not_exist(self):
        organization = Organization(type=OrganizationTypeEnum.DISTRIBUTOR)
        role = OrganizationRoleEnum.CLIENT_USER

        with pytest.raises(HTTPException) as exc_info:
            check_org_role(organization, role)
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Bad role" in str(exc_info.value.detail)

    def test_check_org_role_exists(self):
        organization = Organization(type=OrganizationTypeEnum.DISTRIBUTOR)
        role = OrganizationRoleEnum.DISTRIBUTOR_ADMIN
        check_org_role(organization, role)

    def test_check_org_role_is_none(self):
        organization = Organization(type=OrganizationTypeEnum.DISTRIBUTOR)
        role = OrganizationRoleEnum.PARTNER_USER
        with pytest.raises(HTTPException) as exc_info:
            check_org_role(organization, role)
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Bad role for organization of type" in str(exc_info.value.detail)

    @pytest.mark.skip(reason="Not implemented")
    def test_validate_identity_provider_exists(self, iam):
        idp_data = {
            "alias": "some_alias",
            "display_name": "Some Display Name",
        }
        user_idp = schemas.KeycloakIdentityProvider(**idp_data)
        idp_alias = "some_alias"
        result = validate_identity_provider(iam, idp_alias)
        assert result == user_idp

    def test_validate_identity_provider_not_exists(self, iam):
        idp_alias = "nonexistent_idp"

        with pytest.raises(HTTPException) as exc_info:
            validate_identity_provider(iam, idp_alias)
        exception_value = exc_info.value.detail
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Bad identity provider" in exception_value

    @patch("app.services.users.alias_to_user_identity_provider")
    @patch("app.services.users.add_contact")
    @patch("app.services.users.get_user_v2")
    @patch("app.services.users.add_user_in_to_organization")
    @patch("app.services.users.get_by_email")
    def test_add_user_existing_user_in_org(
        self,
        mock_get_by_email,
        mock_add_user,
        mock_get_user_v2,
        mock_add_contact,
        mock_identity_provider,
        iam,
    ):
        user_data = {
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe",
            "mobile_phone": "************",
            "id": str(uuid.uuid4()),
            "username": "john_doe_username",
            "organization": {
                "id": 1,
                "name": "Sample Organization",
                "type": OrganizationTypeEnum.DISTRIBUTOR,
            },
        }
        mock_get_user_v2.return_value = schemas.UserV2(**user_data)
        mock_add_contact.return_value = None
        mock_get_by_email.return_value = None
        mock_identity_provider.return_value = schemas.KeycloakIdentityProvider(
            alias="okta",
            displayName="Company Okta",
        )
        target_org_mock = Mock()
        user_in = schemas.UserCreate(
            role=OrganizationRoleEnum.CLIENT_ADMIN,
            username="user_username",
            email="<EMAIL>",
            first_name="user_first_name",
            last_name="user_last_name",
            emailVerified=True,
            enabled=False,
        )

        user, user_idp = add_user(self.db, iam, self.actor, user_in, target_org_mock)

        assert user.email == user_in.email
        assert user.username == user_in.username
        assert user.enabled == user_in.enabled
        assert user_idp.alias == "okta"
        assert user_idp.displayName == "Company Okta"
