import pytest
from fastapi.encoders import jsonable_encoder
from starlette import status

from app.adapters.iam.in_memory import IAMStore
from app.adapters.iam.keycloak import KeycloakIdentityProvider, to_keycloak
from app.adapters.iam.schemas import IdentityProvider
from app.enums import OrganizationRoleEnum, OrganizationTypeEnum


@pytest.fixture
def iam_store():
    return IAMStore()


@pytest.fixture
def iam(in_memory_iam):
    return in_memory_iam


@pytest.fixture
def identity_providers(iam_store):
    _identity_providers = [
        IdentityProvider(
            alias="nextgen-okta",
            display_name="Nextgen Okta",
        ),
        IdentityProvider(
            alias="flyaps-okta",
            display_name="Flyaps Okta",
        ),
    ]
    iam_store.identity_providers = _identity_providers
    return _identity_providers


class TestReadIdentityProviders:
    def test_200_ok(self, client, distributor_admin, login_as, identity_providers):
        login_as(distributor_admin)
        expected_providers = jsonable_encoder(
            [to_keycloak(p, KeycloakIdentityProvider) for p in identity_providers]
        )

        response = self.get_identity_providers(client)
        assert response.status_code == status.HTTP_200_OK

        actual_providers = response.json()
        assert actual_providers == expected_providers

    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_ok_for_distributor_users(
        self, role, client, create_org_user, create_distributor_org, login_as
    ):
        org = create_distributor_org(name="org")
        user = create_org_user(org, role=role)
        login_as(user)
        response = self.get_identity_providers(client)
        assert response.status_code == status.HTTP_200_OK

    def test_ok_for_client_admin(self, client, client_admin, login_as):
        login_as(client_admin)
        response = self.get_identity_providers(client)
        assert response.status_code == status.HTTP_200_OK

    @classmethod
    def get_identity_providers(cls, client):
        url = client.app.url_path_for("read_identity_providers")
        return client.get(url)

    def test_returns_401_for_non_authorized_user(self, client):
        response = self.get_identity_providers(client)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_returns_403_for_client_user(
        self,
        client,
        client_user,
        login_as,
    ):
        login_as(client_user)
        response = self.get_identity_providers(client)
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_returns_403_for_partner_user(
        self, client, create_org_user, create_partner_org, login_as
    ):
        org = create_partner_org("test")
        partner_user = create_org_user(
            org,
            role=OrganizationRoleEnum.PARTNER_USER,
        )
        login_as(partner_user)
        response = self.get_identity_providers(client)
        assert response.status_code == status.HTTP_403_FORBIDDEN
