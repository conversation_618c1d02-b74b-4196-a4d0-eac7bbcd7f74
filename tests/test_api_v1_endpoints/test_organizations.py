import io
import uuid
from collections.abc import Iterable
from operator import itemgetter
from typing import List
from unittest.mock import MagicMock, patch

import pytest
from fastapi import status
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import HTTPException
from pydantic import EmailStr
from requests import Response
from sqlalchemy.orm import Session
from starlette.testclient import TestClient

from app import models, schemas
from app.adapters.iam.schemas import IdentityProvider
from app.adapters.platform_api_client.audit_service import AbstractAuditAPI
from app.api.api_v1.endpoints.organizations import (
    ERR_USER_IS_A_MEMBER_WITH_EMAIL,
    ERR_USER_IS_A_MEMBER_WITH_EMAIL_AND_ORG_NAME,
    ERR_USER_SELF_REMOVE_ATTEMPT,
)
from app.core.config import settings
from app.enums import OrganizationRoleEnum, OrganizationTypeEnum
from app.models import FileReport, Organization, UserFile, UserReport
from app.schemas.organization import OrganizationNameStr
from app.services import mappers, org_management
from app.services.org_management import extract_org_members, extract_organization_roles
from tests.factories import ClientFactory, DistributorFactory


@pytest.fixture
def iam(in_memory_iam):
    return in_memory_iam


@pytest.fixture
def identity_provider(iam_store) -> IdentityProvider:
    identity_provider = IdentityProvider(alias="okta", display_name="Nextgen Okta")
    iam_store.identity_providers.append(identity_provider)
    return identity_provider


class TestReadOrganizations:
    def test_authentication_missing(self, client, api_url):
        r = client.get(api_url("read_organizations"))
        assert r.status_code == status.HTTP_401_UNAUTHORIZED

    def test_distributor_admin_access(
        self,
        db,
        client,
        api_url,
        root_organization,
        distributor_admin,
        create_client_org,
        login_as,
    ):
        client_org = create_client_org(name="foo", parent=root_organization)
        login_as(distributor_admin)
        r = client.get(api_url("read_organizations"))
        assert r.status_code == 200, r.content
        data = r.json()
        org_ids = [o["id"] for o in data]
        assert set(org_ids) == {
            client_org.id,
            root_organization.id,
        }
        assert set(data[0].keys()) == {
            "id",
            "parent_id",
            "type",
            "name",
        }

    def test_service_has_access(
        self, db, client, api_url, root_organization, patch_service_info
    ):
        patch_service_info(schemas.ServiceInfo(client_id=settings.OIDC_CLIENT_ID))
        r = client.get(api_url("read_organizations"))
        assert r.status_code == 200, r.content

    def test_client_user_access(
        self, db, client, api_url, login_as, client_org, client_admin
    ):
        login_as(client_admin)
        r = client.get(api_url("read_organizations"))
        assert r.status_code == 200, r.content
        data = r.json()
        assert len(data) == 1, data
        assert data[0]["id"] == client_org.id

    def test_filter_by_organization_id(
        self,
        db,
        client,
        api_url,
        create_client_org,
        login_as,
        root_organization,
        distributor_admin,
    ):
        client_org_a, client_org_b, client_org_c = (
            create_client_org(name=org_name, parent=root_organization)
            for org_name in ["clientA", "clientB", "clientC"]
        )
        login_as(distributor_admin)
        r = client.get(
            api_url("read_organizations"),
            params={"org_id": f"{client_org_a.id},{client_org_c.id}"},
        )
        assert r.status_code == 200, r.content
        data = r.json()
        org_ids = [o["id"] for o in data]
        assert set(org_ids) == {
            client_org_a.id,
            client_org_c.id,
        }

    def test_invalid_organization_id(
        self,
        db,
        client,
        api_url,
        create_client_org,
        login_as,
        root_organization,
        distributor_admin,
    ):
        client_org_a, client_org_b, client_org_c = (
            create_client_org(name=org_name, parent=root_organization)
            for org_name in ["clientA", "clientB", "clientC"]
        )
        login_as(distributor_admin)
        r = client.get(
            api_url("read_organizations"),
            params={"org_id": "dfddf"},
        )
        print(f"r-status_code {r.status_code}")
        assert r.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, (
            r.url,
            r.content,
        )

    def test_invalid_null_organization_id(
        self,
        db,
        client,
        api_url,
        create_client_org,
        login_as,
        root_organization,
        distributor_admin,
    ):
        client_org_a, client_org_b, client_org_c = (
            create_client_org(name=org_name, parent=root_organization)
            for org_name in ["clientA", "clientB", "clientC"]
        )
        login_as(distributor_admin)
        r = client.get(
            api_url("read_organizations"),
            params={"org_id": "  "},
        )
        print(f"r-status_code {r.status_code}")
        assert r.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY, (
            r.url,
            r.content,
        )

    def test_clients_only(
        self,
        db,
        client,
        login_as,
        root_organization,
        create_client_org,
        distributor_admin,
    ):
        client_org_a, client_org_b, client_org_c = (
            create_client_org(org_name, root_organization)
            for org_name in ["clientA", "clientB", "clientC"]
        )
        login_as(distributor_admin)
        url = client.app.url_path_for("read_organizations")
        r = client.get(
            url,
            params={
                "org_id": ",".join(
                    map(str, [client_org_a.id, client_org_c.id, root_organization.id])
                ),
                "org_type": OrganizationTypeEnum.CLIENT,
            },
        )
        assert r.status_code == 200, r.content
        data = r.json()
        org_ids = [o["id"] for o in data]
        assert set(org_ids) == {
            client_org_a.id,
            client_org_c.id,
        }

    def test_distributors_only(
        self,
        db,
        client,
        login_as,
        root_organization,
        create_client_org,
        distributor_admin,
    ):
        client_a, client_b = (
            create_client_org(org_name, root_organization)
            for org_name in ["clientA", "clientB"]
        )
        login_as(distributor_admin)
        url = client.app.url_path_for("read_organizations")
        r = client.get(
            url,
            params={
                "org_id": ",".join(
                    map(str, [client_a.id, client_b.id, root_organization.id])
                ),
                "org_type": OrganizationTypeEnum.DISTRIBUTOR,
            },
        )
        assert r.status_code == 200, r.content
        data = r.json()
        org_ids = [o["id"] for o in data]
        assert set(org_ids) == {root_organization.id}


class TestReadOrganization:
    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_distributor_users_read_own_org_200(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        create_org_user,
        role,
        patch_audit,
    ):
        user = create_org_user(root_organization, role=role)
        login_as(user)
        patch_audit()
        r = client.get(api_url("read_organization", org_id=root_organization.id))
        assert r.status_code == 200, r.content
        assert r.json() == {
            "allowed_domains": [],
            "pmn_codes": [],
            "id": root_organization.id,
            "type": root_organization.type.value,
            "name": root_organization.name,
            "parent": None,
            "created_at": root_organization.created_at.isoformat(),
            "created_by": None,
        }

    def test_distributor_admin_read_own_client_200(
        self, client, api_url, login_as, distributor_admin, client_org, patch_audit
    ):
        login_as(distributor_admin)
        patch_audit()
        r = client.get(api_url("read_organization", org_id=client_org.id))
        assert r.status_code == 200, r.content

    @pytest.mark.parametrize("role", OrganizationTypeEnum.CLIENT.roles)
    def test_client_users_read_distributor_org_404(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        client_org,
        create_org_user,
        role,
        patch_audit,
    ):
        user = create_org_user(client_org, role=role)
        login_as(user)
        patch_audit()
        r = client.get(api_url("read_organization", org_id=root_organization.id))
        assert r.status_code == status.HTTP_404_NOT_FOUND, r.content

    @pytest.mark.parametrize("role", OrganizationTypeEnum.CLIENT.roles)
    def test_client_users_read_own_org_200(
        self, client, api_url, login_as, create_org_user, client_org, role, patch_audit
    ):
        user = create_org_user(client_org, role=role)
        login_as(user)
        patch_audit()
        r = client.get(api_url("read_organization", org_id=client_org.id))
        assert r.status_code == status.HTTP_200_OK, r.content

    def test_read_with_service_token(
        self, patch_service_info, client_org, client, api_url, patch_audit
    ):
        service_info = schemas.ServiceInfo(client_id=settings.OIDC_CLIENT_ID)
        patch_service_info(service_info)
        patch_audit()
        r = client.get(
            api_url("read_organization", org_id=client_org.id),
        )
        assert r.status_code == status.HTTP_200_OK, r.content


class FakeAuditAPI(AbstractAuditAPI):
    def add_audit(self, message) -> None:
        return None


@pytest.fixture
def patch_audit(patch_audit_api):
    def _patch():
        class WithOrg(FakeAuditAPI):
            def add_audit(self, message) -> None:
                return None

            def get_audit(self, id=None, event=None) -> None:
                return None

        patch_audit_api(WithOrg())

    return _patch


class TestDeleteOrganization:
    def test_happy_path_is_ok(
        self, client, api_url, login_as, client_org, distributor_admin, patch_audit
    ):
        login_as(distributor_admin)
        patch_audit()
        r = client.delete(api_url("delete_organization", org_id=client_org.id))
        assert r.status_code == status.HTTP_204_NO_CONTENT

    @pytest.mark.parametrize("role", OrganizationTypeEnum.CLIENT.roles)
    def test_forbidden_for_client_users(
        self, client, api_url, login_as, client_org, create_org_user, role, patch_audit
    ):
        user = create_org_user(client_org, role=role)
        login_as(user)
        patch_audit()
        url = api_url("delete_organization", org_id=client_org.id)
        r = client.delete(url)
        assert r.status_code == status.HTTP_403_FORBIDDEN

    def test_forbidden_for_distributor_user(
        self, client, api_url, login_as, client_org, distributor_user, patch_audit
    ):
        login_as(distributor_user)
        patch_audit()
        url = api_url("delete_organization", org_id=client_org.id)
        patch_audit()
        r = client.delete(url)
        assert r.status_code == status.HTTP_403_FORBIDDEN

    def test_only_client_organization(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        patch_audit,
    ):
        login_as(distributor_admin)
        patch_audit()
        url = api_url("delete_organization", org_id=root_organization.id)
        patch_audit()
        r = client.delete(url)
        assert r.status_code == status.HTTP_404_NOT_FOUND


class TestCreateClient:
    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_distributor_roles_is_201(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        create_org_user,
        role,
        patch_audit,
    ):
        user = create_org_user(root_organization, role=role)
        login_as(user)
        patch_audit()
        r = client.post(
            api_url("create_client", org_id=root_organization.id),
            json={"name": "client-org"},
        )
        assert r.status_code == status.HTTP_201_CREATED, r.content

    def test_service_is_201(
        self,
        client,
        api_url,
        patch_service_info,
        root_organization,
        patch_audit,
        login_as,
        distributor_admin,
    ):
        patch_service_info(schemas.ServiceInfo(client_id=settings.OIDC_CLIENT_ID))
        login_as(distributor_admin)
        patch_audit()
        r = client.post(
            api_url("create_client", org_id=root_organization.id),
            json={"name": "client-org"},
        )
        assert r.status_code == status.HTTP_201_CREATED, r.content

    def test_client_admin_is_404(
        self, client, api_url, login_as, root_organization, client_admin, patch_audit
    ):
        login_as(client_admin)
        patch_audit()
        r = client.post(
            api_url("create_client", org_id=root_organization.id),
            json={"name": "client-org"},
        )
        assert r.status_code == status.HTTP_404_NOT_FOUND, r.content


class TestUpdateClient:
    def test_authentication_missing_401(self, client, api_url, client_org):
        url = api_url("update_client_organization", org_id=client_org.id)
        response = client.put(url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED, (
            response.url,
            response.content,
        )

    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_distributor_roles_is_ok(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        create_org_user,
        create_client_org,
        role,
        patch_audit,
    ):
        client_org = create_client_org("AcmeClient", root_organization)
        client_org_update = schemas.OrganizationUpdate(
            name=client_org.name[:-1] + "abc",
        )
        user = create_org_user(root_organization, role=role)
        login_as(user)
        patch_audit()
        url = api_url("update_client_organization", org_id=client_org.id)
        response = client.put(url, json=client_org_update.dict())
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )

    def test_service_is_ok(
        self,
        client,
        api_url,
        root_organization,
        create_client_org,
        patch_service_info,
        patch_audit,
        login_as,
        distributor_admin,
    ):
        client_org = create_client_org("AcmeClient", root_organization)
        client_org_update = schemas.OrganizationUpdate(
            name=client_org.name[:-1] + "abc",
        )
        patch_service_info(schemas.ServiceInfo(client_id=settings.OIDC_CLIENT_ID))
        login_as(distributor_admin)
        patch_audit()
        url = api_url("update_client_organization", org_id=client_org.id)
        response = client.put(url, json=client_org_update.dict())
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )

    def test_client_admin_is_ok(
        self,
        client,
        api_url,
        db,
        login_as,
        root_organization,
        create_client_org,
        create_org_user,
        patch_audit,
    ):
        client_org = create_client_org("AcmeClient", root_organization)
        client_org_update = schemas.OrganizationUpdate(
            name=client_org.name[:-1] + "abc",
        )
        client_admin = create_org_user(
            client_org, role=OrganizationRoleEnum.CLIENT_ADMIN
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("update_client_organization", org_id=client_org.id)
        response = client.put(url, json=client_org_update.dict())
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )

    def test_changes_applied_in_db(
        self, client, api_url, db, login_as, client_org, client_admin, patch_audit
    ):
        client_org_update = schemas.OrganizationUpdate(
            name=OrganizationNameStr(client_org.name[:-1] + "abc"),
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("update_client_organization", org_id=client_org.id)
        client.put(url, json=client_org_update.dict())
        org = org_management.get_object(
            db, models.Organization, False, id=client_org.id
        )
        db.refresh(org)
        assert org.name == client_org_update.name

    def test_response_schema_for_client_admin(
        self, client, api_url, db, login_as, client_org, client_admin, patch_audit
    ):
        client_org_update = schemas.OrganizationUpdate(
            name=OrganizationNameStr(client_org.name[:-1] + "abc"),
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("update_client_organization", org_id=client_org.id)
        response = client.put(url, json=client_org_update.dict())
        assert response.status_code == status.HTTP_200_OK
        org = org_management.get_object(
            db, org_management.Organization, False, id=client_org.id
        )
        db.refresh(org)
        expected = jsonable_encoder(schemas.OrganizationFull.from_orm(org))
        # Client Admin has no access to parent distributor
        # expected["parent"] = None
        assert response.json() == expected

    def test_response_schema_for_distributor_admin(
        self, client, api_url, db, login_as, client_org, distributor_admin, patch_audit
    ):
        client_org_update = schemas.OrganizationUpdate(
            name=OrganizationNameStr(client_org.name[:-1] + "abc"),
        )
        login_as(distributor_admin)
        patch_audit()
        url = api_url("update_client_organization", org_id=client_org.id)
        response = client.put(url, json=client_org_update.dict())
        assert response.status_code == status.HTTP_200_OK
        org = org_management.get_object(
            db, models.Organization, False, id=client_org.id
        )
        db.refresh(org)
        expected = jsonable_encoder(schemas.OrganizationFull.from_orm(org))
        assert response.json() == expected

    def test_use_existing_name_409(
        self,
        client,
        api_url,
        login_as,
        client_org,
        client_admin,
        root_organization,
        patch_audit,
    ):
        client_org_update = schemas.OrganizationUpdate(
            name=OrganizationNameStr(root_organization.name),
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("update_client_organization", org_id=client_org.id)
        response = client.put(url, json=client_org_update.dict())
        assert response.status_code == status.HTTP_409_CONFLICT, (
            response.url,
            response.content,
        )


class TestReadOrganizationUsers:
    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_distributor_roles_200(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        create_org_user,
        role,
    ):
        user = create_org_user(root_organization, role=role)
        login_as(user)
        r = client.get(
            api_url("read_organization_users", org_id=root_organization.id),
        )
        assert r.status_code == 200, r.content
        users = r.json()
        assert users

    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_distributor_roles_on_child_client_200(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        create_org_user,
        client_org,
        client_user,
        role,
    ):
        user = create_org_user(root_organization, role=role)
        login_as(user)
        r = client.get(
            api_url("read_organization_users", org_id=client_org.id),
        )
        assert r.status_code == 200, r.content
        users = r.json()
        assert users

    @pytest.mark.parametrize("role", OrganizationTypeEnum.CLIENT.roles)
    def test_client_users_200(
        self,
        client,
        api_url,
        login_as,
        client_org,
        create_org_user,
        role,
    ):
        user = create_org_user(client_org, role=role)
        login_as(user)
        r = client.get(api_url("read_organization_users", org_id=client_org.id))
        assert r.status_code == 200, r.content
        users = r.json()
        assert users

    @pytest.mark.parametrize("role", OrganizationTypeEnum.CLIENT.roles)
    def test_client_users_parent_org_404(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        client_org,
        create_org_user,
        role,
    ):
        user = create_org_user(client_org, role=role)
        login_as(user)
        r = client.get(api_url("read_organization_users", org_id=root_organization.id))
        assert r.status_code == status.HTTP_404_NOT_FOUND, r.content
        users = r.json()
        assert users

    @pytest.mark.parametrize("role", OrganizationTypeEnum.CLIENT.roles)
    def test_client_users_other_client_404(
        self,
        db,
        client,
        api_url,
        login_as,
        root_organization,
        client_org,
        create_org_user,
        create_client_org,
        role,
    ):
        client_org_b = create_client_org("new", root_organization)
        user = create_org_user(client_org_b, role=role)
        login_as(user)
        r = client.get(api_url("read_organization_users", org_id=client_org.id))
        assert r.status_code == status.HTTP_404_NOT_FOUND, r.content
        users = r.json()
        assert users


@pytest.mark.usefixtures("patch_core_api")
class TestCreateClientUser:
    def test_client_admin_on_parent_distributor_404(
        self,
        db,
        client,
        api_url,
        login_as,
        client_admin,
        root_organization,
        patch_audit,
    ):
        new_user = schemas.UserCreate(
            email=EmailStr("<EMAIL>"),
            role=OrganizationRoleEnum.CLIENT_ADMIN,
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=root_organization.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.parametrize("role", OrganizationTypeEnum.CLIENT.roles)
    def test_non_existing_user_created(
        self,
        db,
        iam,
        client,
        api_url,
        login_as,
        client_admin,
        client_org,
        role,
        patch_audit,
    ):
        new_user = schemas.UserCreate(
            username="bob",
            firstName="random",
            lastName="example",
            email=EmailStr("<EMAIL>"),
            role=role,
            enabled=True,
            emailVerified=False,
            identityProvider=None,
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=client_org.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_201_CREATED, response.content
        payload = response.json()
        assert payload["username"] == new_user.username
        assert payload["email"] == new_user.email
        assert payload["roles"] == [role]
        assert payload["enabled"] is True
        assert payload["emailVerified"] is False

        user_id = uuid.UUID(payload["id"])
        org = org_management.get_user_org(db, iam, user_id=user_id)
        assert org.id == client_org.id
        user_roles = org_management.fetch_user_org_roles(iam, user_id)
        assert user_roles == [role]

    def test_create_without_username(
        self, db, iam, client, api_url, login_as, client_admin, client_org, patch_audit
    ):
        new_user = schemas.UserCreate(
            email=EmailStr("<EMAIL>"),
            role=OrganizationRoleEnum.CLIENT_ADMIN,
            enabled=True,
            emailVerified=False,
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=client_org.id)
        upload = jsonable_encoder(new_user)
        upload.pop("username")
        response = client.post(url, json=upload)
        assert response.status_code == status.HTTP_201_CREATED, response.content
        payload = response.json()
        assert payload["username"] == new_user.email

    def test_contact_added(
        self,
        db,
        client,
        api_url,
        login_as,
        client_admin,
        client_org,
        core_api,
        patch_audit,
    ):
        new_user = schemas.UserCreate(
            email=EmailStr("<EMAIL>"),
            role=OrganizationRoleEnum.CLIENT_USER,
            username=EmailStr("<EMAIL>"),
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=client_org.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        data = response.json()
        assert core_api.contacts[-1] == schemas.UserV2(
            username=new_user.email,
            email=new_user.email,
            first_name=new_user.firstName,
            last_name=new_user.lastName,
            id=data["id"],
            organization=schemas.OrganizationSimple(
                name=OrganizationNameStr(client_org.name),
                id=client_org.id,
                parent_id=client_org.parent_id,
                type=client_org.type,
            ),
        )

    def test_membership_for_unbound_user_created(
        self,
        db,
        iam,
        client,
        api_url,
        login_as,
        iam_store,
        client_admin,
        client_org,
        unbound_user,
        identity_provider,
        patch_audit,
    ):
        new_user = schemas.UserCreate(
            email=unbound_user.email,
            firstName="New first name",
            lastName="New last name",
            role=OrganizationRoleEnum.CLIENT_USER,
            identityProvider=identity_provider.alias,
        )

        login_as(client_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=client_org.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_201_CREATED, response.content
        payload = response.json()
        assert payload["email"] == new_user.email
        assert payload["firstName"] == unbound_user.firstName
        assert payload["lastName"] == unbound_user.lastName
        assert payload["roles"] == [new_user.role]
        assert payload["identityProvider"]["alias"] == identity_provider.alias
        user_id = uuid.UUID(payload["id"])
        org = org_management.get_user_org(db, iam, user_id=user_id)
        assert org.id == client_org.id
        user_roles = org_management.fetch_user_org_roles(iam, user_id)
        assert user_roles == [new_user.role]

    def test_error_for_user_from_other_org(
        self,
        db,
        iam,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_user,
        client_admin,
        client_org,
        patch_audit,
    ):
        new_user = schemas.UserCreate(
            email=distributor_user.email,
            role=OrganizationRoleEnum.CLIENT_USER,
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=client_org.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_409_CONFLICT, response.content
        assert response.json() == {
            "detail": ERR_USER_IS_A_MEMBER_WITH_EMAIL.format(email=new_user.email)
        }
        user = iam.users.get_by_email(distributor_user.email)
        user_org = org_management.get_user_org(db, iam, user_id=user.id)
        assert user_org.id == root_organization.id

    def test_error_for_user_from_this_org(
        self,
        db,
        iam,
        client,
        api_url,
        login_as,
        client_user,
        client_admin,
        client_org,
        patch_audit,
    ):
        new_user = schemas.UserCreate(
            email=client_user.email,
            role=OrganizationRoleEnum.CLIENT_ADMIN,
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=client_org.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_409_CONFLICT, response.content
        assert response.json() == {
            "detail": ERR_USER_IS_A_MEMBER_WITH_EMAIL_AND_ORG_NAME.format(
                email=new_user.email,
                org_name=client_org.name,
            )
        }
        user = iam.users.get_by_email(email=client_user.email)
        user_org = org_management.get_user_org(db, iam, user_id=user.id)
        assert user_org.id == client_org.id
        user_roles = org_management.fetch_user_org_roles(iam, user_id=user.id)
        assert user_roles == [OrganizationRoleEnum.CLIENT_USER]

    def test_error_msg_for_distributor_admin_includes_org_name(
        self,
        db,
        client,
        api_url,
        login_as,
        client_user,
        client_admin,
        client_org,
        create_client_org,
        distributor_admin,
        root_organization,
        patch_audit,
    ):
        new_client_org = create_client_org("Acme 2", root_organization)
        new_user = schemas.UserCreate(
            email=client_user.email,
            role=OrganizationRoleEnum.CLIENT_ADMIN,
        )
        login_as(distributor_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=new_client_org.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_409_CONFLICT, response.content
        assert response.json() == {
            "detail": ERR_USER_IS_A_MEMBER_WITH_EMAIL_AND_ORG_NAME.format(
                email=new_user.email, org_name=client_org.name
            )
        }

    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_add_user_to_sub_distributor_client_is_ok(
        self,
        client,
        api_url,
        login_as,
        create_client_org,
        create_distributor_org,
        create_org_user,
        root_organization,
        role,
        patch_audit,
    ):
        sub_dist = create_distributor_org("SubDist", root_organization)
        sub_dist_client = create_client_org("SubDistClient", sub_dist)
        user = create_org_user(root_organization, role=role)
        login_as(user)
        new_user = schemas.UserCreate(
            email=EmailStr("<EMAIL>"),
            role=OrganizationRoleEnum.CLIENT_ADMIN,
        )
        patch_audit()
        url = api_url("create_organization_user", org_id=sub_dist_client.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_201_CREATED, (
            response.url,
            response.content,
        )

    def test_create_with_identity_provider(
        self,
        client,
        client_admin,
        client_org,
        api_url,
        login_as,
        identity_provider,
        patch_audit,
    ):
        new_user = schemas.UserCreate(
            email=EmailStr("<EMAIL>"),
            role=OrganizationRoleEnum.CLIENT_ADMIN,
            identityProvider=identity_provider.alias,
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=client_org.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_201_CREATED
        created_user = response.json()
        user_idp = created_user["identityProvider"]
        assert user_idp["alias"] == identity_provider.alias
        assert user_idp["displayName"] == identity_provider.display_name

    def test_create_with_non_existing_identity_provider(
        self, client, client_admin, client_org, api_url, login_as, patch_audit
    ):
        bad_idp_alias = "non-existing-idp-alias"
        new_user = schemas.UserCreate(
            email=EmailStr("<EMAIL>"),
            role=OrganizationRoleEnum.CLIENT_ADMIN,
            identityProvider=bad_idp_alias,
        )
        login_as(client_admin)
        patch_audit()
        url = api_url("create_organization_user", org_id=client_org.id)
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()["detail"]
        assert len(response_data) == 1
        error = response_data[0]
        assert error["loc"] == ["body", "identityProvider"]
        assert error["msg"] == f"Bad identity provider: {bad_idp_alias}"

    def test_create_with_service_token(
        self,
        client,
        api_url,
        client_org,
        patch_service_info,
        patch_audit,
        login_as,
        client_admin,
    ):
        patch_service_info(schemas.ServiceInfo(client_id=settings.OIDC_CLIENT_ID))
        new_user = schemas.UserCreate(
            email=EmailStr("<EMAIL>"),
            role=OrganizationRoleEnum.CLIENT_ADMIN,
        )
        login_as(client_admin)
        url = api_url("create_organization_user", org_id=client_org.id)
        patch_audit()
        response = client.post(url, json=jsonable_encoder(new_user))
        assert response.status_code == status.HTTP_201_CREATED


class TestReadOrganizationUser:
    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_distributor_roles_200(
        self,
        client,
        iam,
        api_url,
        login_as,
        root_organization,
        create_user,
        role,
        distributor_admin,
    ):
        user = create_user(username="bob-sponge")
        org_management.add_user_to_organization(
            iam,
            organization=root_organization,
            user_id=user.id,
            organization_role=role,
        )
        login_as(user)
        r = client.get(
            api_url(
                "read_organization_user",
                org_id=root_organization.id,
                user_id=user.id,
            ),
        )
        assert r.status_code == 200, r.content
        user = r.json()
        assert user

    @pytest.mark.parametrize("role", OrganizationTypeEnum.CLIENT.roles)
    def test_client_users_200(
        self,
        client,
        iam,
        create_user,
        api_url,
        login_as,
        client_org,
        role,
    ):
        user = create_user(username="bob-sponge")
        org_management.add_user_to_organization(
            iam,
            organization=client_org,
            user_id=user.id,
            organization_role=role,
        )

        login_as(user)
        r = client.get(
            api_url(
                "read_organization_user",
                org_id=client_org.id,
                user_id=user.id,
            ),
        )
        assert r.status_code == 200, r.content
        users = r.json()
        assert users

    def test_user_not_from_org_404(
        self,
        client,
        iam,
        create_user,
        api_url,
        login_as,
        client_org,
        create_org_user,
        root_organization,
    ):
        target_user = create_user(username="bob-sponge")
        org_management.add_user_to_organization(
            iam,
            organization=root_organization,
            user_id=target_user.id,
            organization_role=OrganizationRoleEnum.DISTRIBUTOR_USER,
        )
        user = create_org_user(client_org, role=OrganizationRoleEnum.CLIENT_ADMIN)
        login_as(user)
        r = client.get(
            api_url(
                "read_organization_user",
                org_id=client_org.id,
                user_id=target_user.id,
            ),
        )
        assert r.status_code == status.HTTP_404_NOT_FOUND, r.content


class TestSetOrganizationMembership:
    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_create_distributor_user(
        self,
        iam,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_user,
        role,
        patch_audit,
    ):
        user = create_user(username="distributor-user")
        login_as(distributor_admin)
        patch_audit()
        r = client.put(
            api_url(
                "set_organization_membership",
                org_id=root_organization.id,
                user_id=user.id,
            ),
            json={"role": role},
        )
        assert r.status_code == 204, r.content

        user_groups = iam.users.get_groups(user_id=user.id)
        assert user_groups
        assert user_groups[0].path == root_organization.path

        user_roles = iam.users.get_roles(user_id=user.id)
        assert [role] == extract_organization_roles(user_roles)

    @pytest.mark.parametrize("role", [*OrganizationRoleEnum])
    def test_without_access_404(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_user,
        client_admin,
        role,
        patch_audit,
    ):
        user = create_user()
        login_as(client_admin)
        patch_audit()
        r = client.put(
            api_url(
                "set_organization_membership",
                org_id=root_organization.id,
                user_id=user.id,
            ),
            json={"role": role},
        )
        assert r.status_code == status.HTTP_404_NOT_FOUND, r.content

    @pytest.mark.parametrize(
        "role,status_code",
        [
            (OrganizationRoleEnum.DISTRIBUTOR_USER, status.HTTP_400_BAD_REQUEST),
            (OrganizationRoleEnum.DISTRIBUTOR_ADMIN, status.HTTP_400_BAD_REQUEST),
            (OrganizationRoleEnum.CLIENT_USER, status.HTTP_204_NO_CONTENT),
            (OrganizationRoleEnum.CLIENT_ADMIN, status.HTTP_204_NO_CONTENT),
        ],
    )
    def test_client_allowed_roles(
        self,
        db,
        client,
        api_url,
        login_as,
        client_org,
        distributor_admin,
        create_user,
        role,
        status_code,
        patch_audit,
    ):
        user = create_user()
        login_as(distributor_admin)
        patch_audit()
        r = client.put(
            api_url(
                "set_organization_membership", org_id=client_org.id, user_id=user.id
            ),
            json={"role": role},
        )
        assert r.status_code == status_code, r.content

    @pytest.mark.parametrize(
        "role,status_code",
        [
            (OrganizationRoleEnum.CLIENT_USER, status.HTTP_400_BAD_REQUEST),
            (OrganizationRoleEnum.CLIENT_ADMIN, status.HTTP_400_BAD_REQUEST),
            (OrganizationRoleEnum.DISTRIBUTOR_USER, status.HTTP_204_NO_CONTENT),
            (OrganizationRoleEnum.DISTRIBUTOR_ADMIN, status.HTTP_204_NO_CONTENT),
        ],
    )
    def test_distributor_allowed_roles(
        self,
        db,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_user,
        role,
        status_code,
        patch_audit,
    ):
        user = create_user()
        login_as(distributor_admin)
        patch_audit()
        r = client.put(
            api_url(
                "set_organization_membership",
                org_id=root_organization.id,
                user_id=user.id,
            ),
            json={"role": role},
        )
        assert r.status_code == status_code, r.content

    def test_non_existent_user_404(
        self,
        db,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_user,
        patch_audit,
    ):
        user_id = str(uuid.uuid4())
        login_as(distributor_admin)
        patch_audit()
        r = client.put(
            api_url(
                "set_organization_membership",
                org_id=root_organization.id,
                user_id=user_id,
            ),
            json={"role": OrganizationRoleEnum.DISTRIBUTOR_USER},
        )
        assert r.status_code == status.HTTP_404_NOT_FOUND, r.content

    def test_distributor_user_400(
        self,
        iam,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_org_user,
        patch_audit,
        monkeypatch,
    ):
        monkeypatch.setattr(
            "app.core.config.settings.KC_SVC_USER_NAME", "<EMAIL>"
        )
        user = MagicMock()
        user.email = "<EMAIL>"

        iam.users.get_by_id = MagicMock(return_value=user)

        login_as(distributor_admin)
        patch_audit()
        # new role - DistributorUser

        user_id = str(uuid.uuid4())
        login_as(distributor_admin)
        patch_audit()
        r = client.put(
            api_url(
                "set_organization_membership",
                org_id=root_organization.id,
                user_id=user_id,
            ),
            json={"role": OrganizationRoleEnum.DISTRIBUTOR_USER},
        )
        assert r.status_code == status.HTTP_400_BAD_REQUEST, r.content

    def test_update_role(
        self,
        iam,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_org_user,
        patch_audit,
    ):
        user = create_org_user(
            root_organization,
            role=OrganizationRoleEnum.DISTRIBUTOR_ADMIN,
        )
        login_as(distributor_admin)
        patch_audit()
        # new role - DistributorUser
        data = schemas.OrganizationMembership(
            role=OrganizationRoleEnum.DISTRIBUTOR_USER
        )
        r = client.put(
            api_url(
                "set_organization_membership",
                org_id=root_organization.id,
                user_id=user.id,
            ),
            data=data.json(),
        )
        assert r.status_code == 204, r.content

        user_roles = iam.users.get_roles(user_id=user.id)
        actual_roles = extract_organization_roles(user_roles)
        assert [OrganizationRoleEnum.DISTRIBUTOR_USER] == actual_roles


class TestRemoveOrganizationMember:
    def test_happy_path_is_ok(
        self,
        db,
        iam,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_user,
        patch_audit,
    ):
        user = create_user()
        org_management.add_user_to_organization(
            iam,
            organization=root_organization,
            user_id=user.id,
            organization_role=OrganizationRoleEnum.DISTRIBUTOR_USER,
        )
        login_as(distributor_admin)
        patch_audit()
        r = client.delete(
            url=api_url(
                "remove_organization_member",
                org_id=root_organization.id,
                user_id=user.id,
            ),
        )
        assert r.status_code == 204
        user = iam.users.get_by_email(email=user.email)
        assert user is None

    def test_if_user_not_allowed_to_update_organization_raises_403(
        self,
        db,
        client,
        api_url,
        login_as,
        client_org,
        client_user,
        client_admin,
        patch_audit,
    ):
        login_as(client_user)
        patch_audit()
        r = client.delete(
            url=api_url(
                "remove_organization_member",
                org_id=client_org.id,
                user_id=client_admin.id,
            ),
        )
        assert r.status_code == status.HTTP_403_FORBIDDEN

    def test_distributor_admin_can_delete_member_of_descendant(
        self,
        db,
        iam,
        client,
        api_url,
        login_as,
        distributor_admin,
        root_organization,
        create_org_user,
        patch_audit,
    ):
        sub_distributor = org_management.create_organization(
            db,
            iam,
            organization_in=schemas.OrganizationCreate(name=OrganizationNameStr("sub")),
            org_type=OrganizationTypeEnum.DISTRIBUTOR,
            parent=root_organization,
        )
        sub_distributor_model = org_management.get_object(
            db, models.Organization, False, id=sub_distributor.id
        )
        org_management.create_organization(
            db,
            iam,
            organization_in=schemas.OrganizationCreate(
                name=OrganizationNameStr("sub-client")
            ),
            org_type=OrganizationTypeEnum.CLIENT,
            parent=sub_distributor_model,
        )

        assert sub_distributor_model.parent is root_organization
        assert sub_distributor_model.parent.parent is None

        user = create_org_user(
            sub_distributor_model,
            role=OrganizationRoleEnum.CLIENT_ADMIN,
        )
        login_as(distributor_admin)
        patch_audit()
        r = client.delete(
            url=api_url(
                "remove_organization_member",
                org_id=sub_distributor_model.id,
                user_id=user.id,
            ),
        )
        assert r.status_code == status.HTTP_204_NO_CONTENT

    def test_if_user_not_a_member_of_any_organization_raises_404(
        self,
        db,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        unbound_user,
        patch_audit,
    ):
        login_as(distributor_admin)
        patch_audit()
        r = client.delete(
            url=api_url(
                "remove_organization_member",
                org_id=root_organization.id,
                user_id=unbound_user.id,
            ),
        )
        assert r.status_code == status.HTTP_404_NOT_FOUND

    def test_if_user_not_a_member_of_organization_raises_404(
        self,
        db,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        client_user,
        patch_audit,
    ):
        login_as(distributor_admin)
        patch_audit()
        r = client.delete(
            url=api_url(
                "remove_organization_member",
                org_id=root_organization.id,
                user_id=client_user.id,
            ),
        )
        assert r.status_code == status.HTTP_404_NOT_FOUND

    def test_if_user_id_does_not_exist_raises_404(
        self,
        db,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        patch_audit,
    ):
        login_as(distributor_admin)
        patch_audit()
        r = client.delete(
            url=api_url(
                "remove_organization_member",
                org_id=root_organization.id,
                user_id=str(uuid.uuid4()),
            ),
        )
        assert r.status_code == status.HTTP_404_NOT_FOUND

    def test_remove_itself(
        self,
        client,
        api_url,
        distributor_admin,
        root_organization,
        login_as,
        patch_audit,
    ):
        login_as(distributor_admin)
        patch_audit()
        r = client.delete(
            url=api_url(
                "remove_organization_member",
                org_id=root_organization.id,
                user_id=distributor_admin.id,
            ),
        )
        assert r.status_code == status.HTTP_400_BAD_REQUEST
        assert r.json()["detail"] == ERR_USER_SELF_REMOVE_ATTEMPT


@pytest.mark.parametrize(
    "org_factory,roles",
    [
        (
            DistributorFactory,
            [
                dict(name="DistributorUser", display_name="User"),
                dict(name="DistributorAdmin", display_name="Admin"),
            ],
        ),
        (
            ClientFactory,
            [
                dict(name="ClientUser", display_name="User"),
                dict(name="ClientAdmin", display_name="Admin"),
            ],
        ),
    ],
)
def test_read_organization_roles(
    client,
    api_url,
    login_as,
    org_factory,
    roles,
    distributor_admin,
    root_organization,
):
    org = org_factory(parent=root_organization)
    login_as(distributor_admin)
    url = api_url("read_organization_roles", org_id=org.id)
    response = client.get(url)
    assert response.status_code == status.HTTP_200_OK, (
        response.url,
        response.content,
    )
    assert response.json() == roles


class TestReadOrganizationDescendants:
    @classmethod
    def _sort(cls, items: Iterable) -> list:
        items = jsonable_encoder(list(items))
        return list(sorted(items, key=itemgetter("id")))

    def test_not_distributor_org_404(
        self, client, api_url, login_as, client_org, distributor_admin, patch_audit
    ):
        login_as(distributor_admin)
        patch_audit()
        url = api_url("read_organization_descendants")
        response = client.get(url, params=dict(org_id=client_org.id))
        assert response.status_code == status.HTTP_404_NOT_FOUND, response.content

    @pytest.mark.parametrize("role", OrganizationTypeEnum.CLIENT.roles)
    def test_user_has_client_role_404(
        self, client, api_url, login_as, client_org, create_org_user, role, patch_audit
    ):
        user = create_org_user(client_org, role=role)
        login_as(user)
        patch_audit()
        url = api_url("read_organization_descendants")
        response = client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND, response.content

    def test_for_root_org_without_parameters_200(
        self,
        db,
        client,
        api_url,
        login_as,
        root_organization,
        create_client_org,
        create_distributor_org,
        distributor_admin,
        patch_audit,
    ):
        client_a = create_client_org("ClientA", root_organization)
        dist_a = create_distributor_org("DistA", root_organization)
        dist_a_client_a = create_client_org("DistAClientA", dist_a)
        dist_a_client_b = create_client_org("DistAClientB", dist_a)
        with patch("app.services.org_management.extract_org_members") as mock_extract:
            mock_extract.return_value = [
                {
                    "id": "user1",
                    "firstName": "Test",
                    "lastName": "User",
                    "email": "<EMAIL>",
                }
            ]
        login_as(distributor_admin)
        patch_audit()
        url = api_url("read_organization_descendants")
        response = client.get(url)
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )
        actual_result = response.json()
        to_schema = mappers.create_descendant_mapper(
            pmns=[],
            domains=[],
            get_users=extract_org_members,
            get_impersonation_url=lambda users: None,
            get_created_by=lambda org: None,
        )
        expected = map(
            to_schema,
            [
                root_organization,
                client_a,
                dist_a,
                dist_a_client_a,
                dist_a_client_b,
            ],
        )
        # Need to check this summary variable
        assert (
            set(actual_result.keys())
            - {"results", "totalCount", "page", "pageSize", "lastPage", "summary"}
            == set()
        )
        assert actual_result["totalCount"] == 5
        assert actual_result["page"] == 1
        assert actual_result["pageSize"] == 50
        assert actual_result["lastPage"] == 1
        assert self._sort(actual_result["results"]) == self._sort(expected)

    @pytest.mark.parametrize("role", OrganizationTypeEnum.DISTRIBUTOR.roles)
    def test_sub_distributor_descendants_is_ok(
        self,
        client,
        api_url,
        login_as,
        create_distributor_org,
        create_client_org,
        create_org_user,
        role,
        patch_audit,
    ):
        root_dist = create_distributor_org(name="Root")
        root_dist_user = create_org_user(root_dist, role=role)
        dist_a = create_distributor_org(name="DistA", parent=root_dist)
        dist_a_client = create_client_org(name="DistAClient", parent=dist_a)

        dist_b = create_distributor_org(name="DistB", parent=dist_a)
        dist_b_client = create_client_org(name="DistBClient", parent=dist_b)

        dist_c = create_distributor_org(name="DistC", parent=root_dist)
        create_client_org(name="DistCClient", parent=dist_c)

        create_client_org(name="RootOrgClient", parent=root_dist)
        with patch("app.services.org_management.extract_org_members") as mock_extract:
            mock_extract.return_value = [
                {
                    "id": "user1",
                    "firstName": "Test",
                    "lastName": "User",
                    "email": "<EMAIL>",
                }
            ]
        login_as(root_dist_user)
        patch_audit()
        url = api_url("read_organization_descendants")
        response = client.get(url, params={"org_id": dist_a.id})
        assert response.status_code == status.HTTP_200_OK
        actual_result = response.json()
        to_schema = mappers.create_descendant_mapper(
            pmns=[],
            domains=[],
            get_users=lambda org: [],
            get_impersonation_url=lambda users: None,
            get_created_by=lambda org: None,
        )
        expected = map(
            to_schema,
            [
                dist_a,
                dist_a_client,
                dist_b,
                dist_b_client,
            ],
        )
        assert (
            set(actual_result.keys())
            - {"results", "totalCount", "page", "pageSize", "lastPage", "summary"}
            == set()
        )
        assert actual_result["totalCount"] == 4
        assert actual_result["lastPage"] == 1
        assert self._sort(actual_result["results"]) == self._sort(expected)

    def test_number_of_users_is_correct(
        self,
        client,
        api_url,
        db,
        login_as,
        root_organization,
        distributor_admin,
        create_distributor_org,
        create_client_org,
        create_org_user,
        patch_audit,
    ):
        user = create_org_user(
            root_organization, role=OrganizationRoleEnum.DISTRIBUTOR_USER
        )
        login_as(distributor_admin)
        with patch("app.services.org_management.RedisService") as mock_redis_cls:
            mock_redis = mock_redis_cls.return_value
            mock_redis.get_group_users.return_value = {
                str(user.id): user,
                "e299958a-6b22-4e79-9c1d-905b174b3e35": user,
            }
            patch_audit()
            url = api_url("read_organization_descendants")
            response = client.get(url=url)
            assert response.status_code == status.HTTP_200_OK, (
                response.url,
                response.content,
            )
            payload = response.json()["results"]
            assert payload[0]["number_of_users"] == 2

    def get_group_users_side_effect(
        self, root_organization, client_org, distributor_admin, client_admin
    ):
        def _side_effect(org_id):
            if org_id == str(root_organization.external_id):
                # Return distributor admin for distributor org
                return {str(distributor_admin.id): distributor_admin}
            elif org_id == str(client_org.external_id):
                # Return client admin for client org
                return {str(client_admin.id): client_admin}
            else:
                return {}

        return _side_effect

    def test_descendant_representation(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        client_org,
        distributor_admin,
        client_admin,
        patch_audit,
    ):
        url = api_url("read_organization_descendants")
        login_as(distributor_admin)

        with patch("app.services.org_management.RedisService") as mock_redis_cls:
            mock_redis = mock_redis_cls.return_value

            mock_redis.get_group_users.side_effect = (
                lambda external_id: {
                    str(distributor_admin.id): {
                        "id": str(distributor_admin.id),
                        "firstName": distributor_admin.firstName,
                        "lastName": distributor_admin.lastName,
                        "email": distributor_admin.email,
                    }
                }
                if external_id == root_organization.external_id
                else {
                    str(client_admin.id): {
                        "id": str(client_admin.id),
                        "firstName": client_admin.firstName,
                        "lastName": client_admin.lastName,
                        "email": client_admin.email,
                    }
                }
                if external_id == client_org.external_id
                else {}
            )

            patch_audit()
            response = client.get(url)
            assert response.status_code == status.HTTP_200_OK, (
                response.url,
                response.content,
            )

            payload = response.json()
            assert payload["totalCount"] == 2
            assert payload["lastPage"] == 1
            assert self._sort(payload["results"]) == self._sort(
                [
                    {
                        "allowed_domains": [],
                        "pmn_codes": [],
                        "id": root_organization.id,
                        "name": root_organization.name,
                        "number_of_users": 1,
                        "parent": None,
                        "type": OrganizationTypeEnum.DISTRIBUTOR.value,
                        "created_at": root_organization.created_at,
                        "impersonation_url": None,
                        "created_by": None,
                    },
                    {
                        "allowed_domains": [],
                        "pmn_codes": [],
                        "id": client_org.id,
                        "name": client_org.name,
                        "number_of_users": 1,
                        "parent": {
                            "id": root_organization.id,
                            "name": root_organization.name,
                            "type": root_organization.type,
                            "parent_id": None,
                        },
                        "type": OrganizationTypeEnum.CLIENT.value,
                        "created_at": root_organization.created_at,
                        "impersonation_url": str(
                            org_management.build_impersonation_url(client_admin.id)
                        ),
                        "created_by": None,
                    },
                ]
            )

    def test_only_clients_is_ok(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_client_org,
        create_distributor_org,
        patch_audit,
    ):
        root_org_client = create_client_org("RootClient", root_organization)
        dist_a = create_distributor_org("DistA", root_organization)
        dist_a_client = create_client_org("DistAClient", dist_a)

        url = api_url("read_organization_descendants")
        login_as(distributor_admin)
        patch_audit()
        response = client.get(
            url,
            params=dict(descendant_type=OrganizationTypeEnum.CLIENT.value),
        )
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )
        payload = response.json()
        assert len(payload["results"]) == 2, payload
        assert set(map(itemgetter("id"), payload["results"])) == {
            root_org_client.id,
            dist_a_client.id,
        }, payload

    def test_only_distributors_is_ok(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_client_org,
        create_distributor_org,
        patch_audit,
    ):
        create_client_org("RootClient", root_organization)
        dist_a = create_distributor_org("DistA", root_organization)
        create_client_org("DistAClient", dist_a)
        dist_b = create_distributor_org("DistB", dist_a)

        url = api_url("read_organization_descendants")
        login_as(distributor_admin)
        patch_audit()
        response = client.get(
            url, params=dict(descendant_type=OrganizationTypeEnum.DISTRIBUTOR.value)
        )
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )
        payload = response.json()
        assert len(payload["results"]) == 3, payload
        assert set(map(itemgetter("id"), payload["results"])) == {
            root_organization.id,
            dist_a.id,
            dist_b.id,
        }, payload

    @pytest.mark.parametrize(
        "user_params",
        [
            dict(enabled=False),
            dict(email_verified=False),
            dict(enabled=False, email_verified=False),
        ],
    )
    def test_disabled_and_unverified_users_not_included_in_impersonation(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_client_org,
        create_org_user,
        user_params,
        patch_audit,
    ):
        client_b = create_client_org("clientB", root_organization)
        create_org_user(client_b, role=OrganizationRoleEnum.CLIENT_ADMIN, **user_params)
        url = api_url("read_organization_descendants")
        login_as(distributor_admin)
        patch_audit()
        response = client.get(
            url,
            params=dict(descendant_type=OrganizationTypeEnum.CLIENT.value),
        )
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )
        payload = response.json()["results"]
        assert len(payload) == 1, payload
        assert payload[0]["impersonation_url"] is None, payload

    def test_read_organization_pagination_failed(
        self,
        client,
        api_url,
        login_as,
        root_organization,
        distributor_admin,
        create_client_org,
        create_org_user,
        patch_audit,
    ):
        create_client_org("ClientA", root_organization)
        create_client_org("ClientB", root_organization)
        login_as(distributor_admin)
        patch_audit()
        url = api_url("read_organization_descendants")
        response = client.get(url, params={"page": 99, "page_size": 1})
        assert response.status_code == 400, (response.url, response.content)


class TestPatchUpdateOrganizationUser:
    def test_distributor_admin_is_allowed_to_update(
        self,
        distributor_admin,
        root_organization,
        create_org_user,
        client,
        login_as,
        patch_audit,
    ):
        user_to_be_updated = create_org_user(
            root_organization,
            role=OrganizationRoleEnum.DISTRIBUTOR_USER,
            email="<EMAIL>",
        )
        login_as(distributor_admin)
        patch_audit()
        response = self.update_user(
            client=client,
            org_id=root_organization.id,
            user_id=user_to_be_updated.id,
            data={},
        )
        assert response.status_code == status.HTTP_200_OK

    def test_distributor_user_is_not_allowed_to_update(
        self,
        distributor_user,
        root_organization,
        create_org_user,
        client,
        login_as,
        patch_audit,
    ):
        user_to_be_updated = create_org_user(
            root_organization,
            role=OrganizationRoleEnum.DISTRIBUTOR_USER,
            email="<EMAIL>",
        )
        login_as(distributor_user)
        patch_audit()
        response = self.update_user(
            client=client,
            org_id=root_organization.id,
            user_id=user_to_be_updated.id,
            data={},
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_client_admin_is_allowed_to_update(
        self, client_admin, client_org, create_org_user, client, login_as, patch_audit
    ):
        user_to_be_updated = create_org_user(
            client_org,
            role=OrganizationRoleEnum.CLIENT_USER,
            email="<EMAIL>",
        )
        login_as(client_admin)
        patch_audit()
        response = self.update_user(
            client=client,
            org_id=client_org.id,
            user_id=user_to_be_updated.id,
            data={},
        )
        assert response.status_code == status.HTTP_200_OK

    def test_client_user_is_not_allowed_to_update(
        self, client_user, client_org, create_org_user, client, login_as, patch_audit
    ):
        user_to_be_updated = create_org_user(
            client_org,
            role=OrganizationRoleEnum.CLIENT_USER,
            email="<EMAIL>",
        )
        login_as(client_user)
        patch_audit()
        response = self.update_user(
            client=client,
            org_id=client_org.id,
            user_id=user_to_be_updated.id,
            data={},
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_update_for_non_existing_organization(
        self,
        root_organization,
        distributor_admin,
        client,
        distributor_user,
        login_as,
        patch_audit,
    ):
        login_as(distributor_admin)
        patch_audit()
        response = self.update_user(
            client=client,
            org_id=10001,  # non-existing organization id
            user_id=distributor_user.id,
            data={},
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_update_non_existing_user(
        self, root_organization, distributor_admin, client, login_as, patch_audit
    ):
        login_as(distributor_admin)
        patch_audit()
        response = self.update_user(
            client=client,
            org_id=root_organization.id,
            user_id=uuid.uuid4(),  # non-existing user id
            data={},
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_set_identity_provider(
        self,
        distributor_admin,
        distributor_user,
        root_organization,
        iam,
        client,
        login_as,
        identity_provider,
        patch_audit,
    ):
        target_user = iam.users.get_by_id(distributor_user.id)
        assert target_user.identity_provider is None

        login_as(distributor_admin)
        patch_audit()
        data = schemas.UserUpdate(identityProvider=identity_provider.alias)

        response = self.update_user(
            client=client,
            org_id=root_organization.id,
            user_id=target_user.id,
            data=data.dict(exclude_unset=True),
        )
        assert response.status_code == status.HTTP_200_OK, response.content

        updated_user = iam.users.get_by_id(target_user.id)
        assert updated_user.identity_provider == data.identityProvider

    def test_remove_identity_provider(
        self,
        distributor_admin,
        distributor_user,
        root_organization,
        iam,
        client,
        login_as,
        identity_provider,
        patch_audit,
    ):
        target_user = iam.users.get_by_id(distributor_user.id)
        target_user.set_identity_provider(identity_provider.alias)
        target_user = iam.users.update(target_user.to_update())

        login_as(distributor_admin)
        patch_audit()
        data = schemas.UserUpdate(identityProvider=None)

        response = self.update_user(
            client=client,
            org_id=root_organization.id,
            user_id=target_user.id,
            data=data.dict(exclude_unset=True),
        )
        assert response.status_code == status.HTTP_200_OK

        updated_user = iam.users.get_by_id(target_user.id)
        assert updated_user.identity_provider is None

    def test_idp_is_not_change_when_it_is_unset(
        self,
        distributor_admin,
        distributor_user,
        root_organization,
        iam,
        client,
        login_as,
        identity_provider,
        patch_audit,
    ):
        target_user = iam.users.get_by_id(distributor_user.id)
        target_user.set_identity_provider(identity_provider.alias)
        target_user = iam.users.update(target_user)

        login_as(distributor_admin)
        patch_audit()
        response = self.update_user(
            client=client,
            org_id=root_organization.id,
            user_id=target_user.id,
            data={},
        )
        assert response.status_code == status.HTTP_200_OK

        updated_user = iam.users.get_by_id(target_user.id)
        assert updated_user.identity_provider == identity_provider.alias

    @classmethod
    def update_user(
        cls,
        client: TestClient,
        org_id: int,
        user_id: uuid.UUID,
        data: dict,
    ) -> Response:
        url = client.app.url_path_for(
            "update_organization_user",
            org_id=org_id,
            user_id=user_id,
        )
        response = client.patch(url, json=data)
        return response

    def test_udpate_with_service_token(
        self,
        iam,
        client,
        api_url,
        client_org,
        patch_service_info,
        client_admin,
        identity_provider,
        patch_audit,
        login_as,
    ):
        patch_service_info(schemas.ServiceInfo(client_id=settings.OIDC_CLIENT_ID))
        target_user = iam.users.get_by_id(client_admin.id)
        login_as(client_admin)
        patch_audit()
        response = self.update_user(
            client=client, org_id=client_org.id, user_id=target_user.id, data={}
        )
        assert response.status_code == status.HTTP_200_OK

    def test_username_updated(
        self,
        iam,
        client,
        api_url,
        client_org,
        patch_service_info,
        client_admin,
        identity_provider,
        patch_audit,
        login_as,
    ):
        patch_service_info(schemas.ServiceInfo(client_id=settings.OIDC_CLIENT_ID))
        target_user = iam.users.get_by_id(client_admin.id)
        username = str(uuid.uuid4())
        login_as(client_admin)
        patch_audit()
        response = self.update_user(
            client=client,
            org_id=client_org.id,
            user_id=target_user.id,
            data={"username": username},
        )
        assert response.status_code == status.HTTP_200_OK, response.json()
        updated_user = iam.users.get_by_id(target_user.id)
        assert updated_user.username == username


class TestUploadUsersCSV:
    @pytest.fixture
    def csv_file(self):
        """Fixture to create a valid CSV file for testing."""
        content = (
            b"first name,last name,email\nJohn,Doe,"
            b"<EMAIL>\nJane,Smith,<EMAIL>"
        )
        return io.BytesIO(content)

    @pytest.fixture
    def mock_storage(self):
        """Fixture to mock the file storage service."""
        storage = MagicMock()
        storage.upload_file.return_value = "test-s3-key"
        storage.generate_file_url.return_value = "https://example.com/test-s3-key"
        return storage

    @pytest.fixture
    def mock_csv_validator(self):
        """Fixture to mock the CSV validator."""
        validator = MagicMock()
        validator.validate.return_value = {
            "content": (
                "first name,last name,email\nJohn,Doe,"
                "<EMAIL>\nJane,Smith,<EMAIL>"
            ),
            "headers": ["first name", "last name", "email"],
        }
        return validator

    @patch("app.core.file_storage.S3FileStorage.upload_file")
    def test_upload_users_csv_success(
        self,
        mock_upload_file,
        client,
        api_url,
        login_as,
        distributor_admin,
        csv_file,
        monkeypatch,
    ):
        class MockOso:
            def authorize(self, user, scope, org):
                pass

            def is_allowed(self, user, scope, org):
                return True

            def forbidden_error(self):
                return Exception("Forbidden")

            def not_found_error(self):
                return Exception("Not Found")

        mock_oso = MockOso()
        monkeypatch.setattr("app.api.api_v1.endpoints.organizations.oso", mock_oso)
        login_as(distributor_admin)
        files = {"file": ("users.csv", csv_file, "text/csv")}
        mock_upload_file.return_value = "users.csv"
        response = client.post(api_url("upload_users_csv"), files=files)

        assert (
            response.status_code == status.HTTP_201_CREATED
        ), f"Response: {response.text}"
        data = response.json()
        assert "users.csv" in data["key"]
        assert "users.csv" in data["fileName"]
        assert "http://localhost:9000/organizations/users.csv" in data["url"]

    def test_upload_users_csv_forbidden(
        self,
        client,
        api_url,
        login_as,
        client_user,
        csv_file,
        monkeypatch,
    ):
        """Test CSV upload with insufficient permissions."""

        class MockOso:
            def authorize(self, user, scope, org):
                raise Exception("Forbidden")

            def is_allowed(self, user, scope, org):
                return False

            @property
            def forbidden_error(self):
                return Exception

            @property
            def not_found_error(self):
                return Exception

        mock_oso = MockOso()
        monkeypatch.setattr("app.api.api_v1.endpoints.organizations.oso", mock_oso)

        login_as(client_user)
        files = {"file": ("users.csv", csv_file, "text/csv")}

        response = client.post(api_url("upload_users_csv"), files=files)

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_upload_users_csv_invalid_format(
        self,
        client,
        api_url,
        login_as,
        distributor_admin,
        monkeypatch,
    ):
        """Test CSV upload with invalid file format."""
        invalid_csv = io.BytesIO(b"invalid,headers\nJohn,Doe")

        class MockOso:
            def authorize(self, user, scope, org):
                pass

            def is_allowed(self, user, scope, org):
                return True

            @property
            def forbidden_error(self):
                return Exception("Forbidden")

            @property
            def not_found_error(self):
                return Exception("Not Found")

        mock_oso = MockOso()
        monkeypatch.setattr("app.api.api_v1.endpoints.organizations.oso", mock_oso)

        mock_validator = MagicMock()
        mock_validator.validate.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing required headers: first name, last name, email",
        )
        monkeypatch.setattr(
            "app.services.validation.CSV", lambda **kwargs: mock_validator
        )

        login_as(distributor_admin)

        files = {"file": ("users.csv", invalid_csv, "text/csv")}

        response = client.post(api_url("upload_users_csv"), files=files)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert "Missing required headers" in response.json()["detail"]


@pytest.fixture
def user_file(db: Session, client_org: Organization) -> UserFile:
    """Create a test user file."""
    user_file = UserFile(
        uuid=uuid.uuid4(),
        file_name=f"{str(uuid.uuid4())[:6]}_test_users.csv",
        path="s3://bucket/test_users.csv",
        organization_id=client_org.id,
    )
    db.add(user_file)
    db.commit()
    db.refresh(user_file)
    return user_file


@pytest.fixture
def file_report(db: Session, user_file: UserFile) -> FileReport:
    """Create a test file report."""
    file_report = FileReport(
        user_file_id=user_file.id,
        status="completed",
        total_rows=100,
        valid_rows=98,
    )
    db.add(file_report)
    db.commit()
    db.refresh(file_report)
    return file_report


@pytest.fixture
def user_reports(db: Session, user_file: UserFile) -> List[UserReport]:
    """Create test user reports."""
    reports = [
        UserReport(
            user_file_id=user_file.id,
            first_name="User1",
            last_name="Test",
            email="<EMAIL>",
            reason="Invalid first name format.",
        ),
        UserReport(
            user_file_id=user_file.id,
            first_name="User2",
            last_name="Test",
            email="user2example.com",
            reason="Invalid email format.",
        ),
    ]
    db.add_all(reports)
    db.commit()
    for report in reports:
        db.refresh(report)
    return reports


class TestReadBulkUserFileStatus:
    def test_auth_missing(self, client, api_url, client_org):
        """Test that authentication is required."""
        url = api_url("read_bulk_user_file_status", org_id=client_org.id)
        response = client.get(url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_organization_not_found(
        self, client, api_url, client_user, login_as, patch_audit
    ):
        """Test that a 404 is returned for a non-existent organization."""
        login_as(client_user)
        patch_audit()
        url = api_url("read_bulk_user_file_status", org_id=999999)
        response = client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_unauthorized_access(
        self, client, api_url, client_user, login_as, root_organization, patch_audit
    ):
        """Test that a user cannot access another organization's files."""
        login_as(client_user)
        patch_audit()
        url = api_url("read_bulk_user_file_status", org_id=root_organization.id)
        response = client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_empty_result(
        self, client, api_url, client_user, login_as, client_org, patch_audit
    ):
        """Test that an empty result is returned when no files exist."""
        login_as(client_user)
        patch_audit()
        url = api_url("read_bulk_user_file_status", org_id=client_org.id)
        response = client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_successful_retrieval(
        self,
        client,
        api_url,
        client_user,
        login_as,
        client_org,
        user_file,
        file_report,
        patch_audit,
    ):
        """Test successful retrieval of bulk user file status."""
        login_as(client_user)
        patch_audit()
        url = api_url("read_bulk_user_file_status", org_id=client_org.id)
        response = client.get(url)
        assert response.status_code == status.HTTP_200_OK

        # Check that the response is a paginated response with the expected results
        data = response.json()
        assert len(data["results"]) == 1
        assert data["page"] == 1
        assert data["totalCount"] == 1

        # Check the file status details
        file_status = data["results"][0]
        assert file_status["fileName"] == user_file.file_name[7:]
        assert file_status["id"] == str(user_file.uuid)
        assert file_status["status"] == file_report.status
        assert file_status["createdBy"] is None
        assert file_status["createdAt"] is None

    def test_pagination(
        self,
        client,
        api_url,
        client_user,
        login_as,
        client_org,
        db,
        user_file,
        file_report,
        patch_audit,
    ):
        """Test pagination of bulk user file status."""
        # Create additional user files
        for i in range(5):
            new_file = UserFile(
                uuid=uuid.uuid4(),
                file_name=f"test_users_{i}.csv",
                path=f"s3://bucket/test_users_{i}.csv",
                organization_id=client_org.id,
            )
            db.add(new_file)
            db.commit()
            db.refresh(new_file)

            new_report = FileReport(
                user_file_id=new_file.id,
                status="completed",
                total_rows=100,
                valid_rows=98,
            )
            db.add(new_report)
            db.commit()

        login_as(client_user)

        # Test first page with page_size=2
        url = (
            api_url("read_bulk_user_file_status", org_id=client_org.id)
            + "?page=1&page_size=2"
        )
        patch_audit()
        response = client.get(url)
        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert len(data["results"]) == 2
        assert data["page"] == 1
        assert data["totalCount"] == 6  # 5 new files + 1 from fixture

        # Test second page
        url = (
            api_url("read_bulk_user_file_status", org_id=client_org.id)
            + "?page=2&page_size=2"
        )
        response = client.get(url)
        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert len(data["results"]) == 2
        assert data["page"] == 2


class TestReadBulkUserFileReports:
    def test_auth_missing(self, client, api_url, client_org, user_file, patch_audit):
        """Test that authentication is required."""
        patch_audit()
        url = api_url(
            "read_bulk_user_file_reports",
            org_id=client_org.id,
            user_file_id=user_file.uuid,
        )
        response = client.get(url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_organization_not_found(
        self, client, api_url, client_user, login_as, user_file, patch_audit
    ):
        """Test that a 404 is returned for a non-existent organization."""
        login_as(client_user)
        patch_audit()
        url = api_url(
            "read_bulk_user_file_reports", org_id=999999, user_file_id=user_file.uuid
        )
        response = client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_notification_not_found(
        self, client, api_url, distributor_admin, login_as, client_org, patch_audit
    ):
        """Test that a 404 is returned for a non-existent notification."""
        login_as(distributor_admin)
        patch_audit()
        url = api_url(
            "read_bulk_user_file_reports",
            org_id=client_org.id,
            user_file_id=uuid.uuid4(),
        )
        response = client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_unauthorized_access(
        self,
        client,
        api_url,
        client_user,
        login_as,
        root_organization,
        user_file,
        patch_audit,
    ):
        """Test that a user cannot access another organization's files."""
        login_as(client_user)
        patch_audit()
        url = api_url(
            "read_bulk_user_file_reports",
            org_id=root_organization.id,
            user_file_id=user_file.uuid,
        )
        response = client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_file_without_report(
        self, client, api_url, distributor_admin, login_as, client_org, db, patch_audit
    ):
        """Test retrieval of a file without a report."""
        # Create a user file without a report
        user_file = UserFile(
            uuid=uuid.uuid4(),
            file_name="no_report.csv",
            path="s3://bucket/no_report.csv",
            organization_id=client_org.id,
        )
        db.add(user_file)
        db.commit()
        db.refresh(user_file)

        login_as(distributor_admin)
        patch_audit()
        url = api_url(
            "read_bulk_user_file_reports",
            org_id=client_org.id,
            user_file_id=user_file.uuid,
        )
        response = client.get(url)
        assert response.status_code == status.HTTP_200_OK

        # Check that the response has default values
        data = response.json()
        assert data["totalRows"] == 0
        assert data["validRows"] == 0
        assert data["invalidRows"] == []

    def test_successful_retrieval(
        self,
        client,
        api_url,
        distributor_admin,
        login_as,
        client_org,
        user_file,
        file_report,
        user_reports,
        patch_audit,
    ):
        """Test successful retrieval of bulk user file reports."""
        login_as(distributor_admin)
        patch_audit()
        url = api_url(
            "read_bulk_user_file_reports",
            org_id=client_org.id,
            user_file_id=user_file.uuid,
        )
        response = client.get(url)
        assert response.status_code == status.HTTP_200_OK

        # Check the report details
        data = response.json()
        assert data["totalRows"] == file_report.total_rows
        assert data["validRows"] == file_report.valid_rows
        assert len(data["invalidRows"]) == len(user_reports)

        # Check the invalid rows
        for i, report in enumerate(user_reports):
            assert data["invalidRows"][i]["email"] == report.email
            assert data["invalidRows"][i]["firstName"] == report.first_name
            assert data["invalidRows"][i]["lastName"] == report.last_name
            assert data["invalidRows"][i]["reason"] == report.reason
