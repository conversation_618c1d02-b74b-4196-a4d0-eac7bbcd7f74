import pytest
from fastapi import status

from app.adapters.iam.in_memory import InMemoryIAM
from app.core.config import settings
from app.schemas import ServiceInfo


@pytest.fixture
def iam(iam_store):
    return InMemoryIAM(iam_store)


@pytest.mark.usefixtures("root_organization")
@pytest.mark.usefixtures("patch_core_api")
class TestCreateUserForPartner:
    def test_without_token_return_401(self, client, api_url):
        url = api_url("create_user_for_partner")
        response = client.post(url, json={})
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_happy_path_returns_201_and_created_user(
        self, client, api_url, patch_service_info
    ):
        patch_service_info(ServiceInfo(client_id=settings.OIDC_CLIENT_ID))
        url = api_url("create_user_for_partner")
        payload = {
            "username": "<EMAIL>",
            "email": "<EMAIL>",
            "first_name": "<PERSON>",
            "last_name": "Borg",
            "organization_name": "Acme",
        }
        response = client.post(
            url,
            json=payload,
        )
        assert response.status_code == status.HTTP_201_CREATED, (
            response.url,
            response.content,
        )
        created_user = response.json()
        assert created_user["email"] == payload["email"]
        assert created_user["first_name"] == payload["first_name"]
        assert created_user["last_name"] == payload["last_name"]
        assert created_user["organization"]["name"] == (payload["organization_name"])
