import uuid

import pytest
from fastapi.encoders import jsonable_encoder
from pydantic import EmailStr, SecretStr
from starlette import status

from app import schemas
from app.adapters.iam.schemas import PasswordPolicy
from app.enums import OrganizationTypeEnum
from app.schemas.organization import OrganizationNameStr
from app.services import authz_password, mappers


@pytest.fixture
def iam(in_memory_iam):
    return in_memory_iam


class TestReadAuthenticatedUser:
    def test_user_without_organization_401(
        self,
        client,
        api_url,
        unbound_user,
        login_as,
    ):
        login_as(unbound_user)
        response = client.get(api_url("read_authenticated_user"))
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_representation(
        self, client, api_url, iam, login_as, client_user, client_org
    ):
        login_as(client_user)
        response = client.get(api_url("read_authenticated_user"))
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )
        assert response.json() == {
            "id": str(client_user.id),
            "email": client_user.email,
            "username": client_user.username,
            "firstName": client_user.firstName,
            "lastName": client_user.lastName,
            "organization": {
                "id": client_org.id,
                "name": client_org.name,
                "type": client_org.type.value,
                "parent_id": client_org.parent_id,
            },
        }

    @pytest.mark.parametrize("role", [*OrganizationTypeEnum.CLIENT.roles])
    def test_client_users_is_200(
        self, client, api_url, client_org, create_org_user, role, login_as
    ):
        user = create_org_user(client_org, role=role)
        login_as(user)
        response = client.get(api_url("read_authenticated_user"))
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )

    @pytest.mark.parametrize("role", [*OrganizationTypeEnum.DISTRIBUTOR.roles])
    def test_distributor_users_is_200(
        self, client, api_url, login_as, root_organization, create_org_user, role
    ):
        user = create_org_user(root_organization, role=role)
        login_as(user)
        response = client.get(api_url("read_authenticated_user"))
        assert response.status_code == status.HTTP_200_OK, (
            response.url,
            response.content,
        )


class TestReadUsers:
    def test_auth_missing(self, client, api_url):
        r = client.get(api_url("read_users"))
        assert r.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.usefixtures("login_as_core_service")
    def test_for_service_with_same_client_id_is_ok(
        self,
        client,
        unbound_user,
        distributor_admin,
        api_url,
    ):
        r = client.get(api_url("read_users"))
        assert r.status_code == status.HTTP_200_OK

        response_data = r.json()
        assert len(response_data) == 2

        emails = [item["email"] for item in response_data]
        assert unbound_user.email in emails
        assert distributor_admin.email in emails

    @pytest.mark.usefixtures("login_as_core_service")
    def test_representation(self, client, api_url, create_user):
        user = create_user(
            email=f"{uuid.uuid4().hex[:10]}@example.com",
            email_verified=True,
            enabled=False,
            first_name="Bob",
            last_name="Tompkins",
        )
        r = client.get(api_url("read_users"))
        actual_data = r.json()[0]
        timestamp = actual_data.pop("createdTimestamp", None)
        assert isinstance(timestamp, int)
        assert actual_data == {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "firstName": user.firstName,
            "lastName": user.lastName,
            "emailVerified": user.emailVerified,
            "enabled": user.enabled,
        }

    def test_for_unbound_user(self, client, api_url, unbound_user, login_as):
        login_as(unbound_user)
        r = client.get(api_url("read_users"))
        assert r.status_code == status.HTTP_401_UNAUTHORIZED


class TestCreateUser:
    @pytest.fixture()
    def user_to_create(self) -> schemas.KeycloakUserCreate:
        return schemas.KeycloakUserCreate(
            email=EmailStr("<EMAIL>"),
            username="another_one",
            password=SecretStr("passWORM"),
        )

    def test_auth_missing(self, client, api_url, user_to_create):
        r = client.post(api_url("create_user"), data=user_to_create.json())
        assert r.status_code == status.HTTP_401_UNAUTHORIZED

    def test_with_distributor_admin_role(
        self, client, api_url, distributor_admin, user_to_create, login_as, iam
    ):
        login_as(distributor_admin)
        r = client.post(api_url("create_user"), data=user_to_create.json())
        assert r.status_code == status.HTTP_200_OK

        user = iam.users.get_by_email(user_to_create.email)
        assert user is not None

    def test_for_unbound_user(
        self, client, api_url, unbound_user, user_to_create, login_as
    ):
        login_as(unbound_user)
        r = client.post(api_url("create_user"), data=user_to_create.json())
        assert r.status_code == status.HTTP_401_UNAUTHORIZED


class TestReadUser:
    def test_auth_missing(self, client, api_url, unbound_user, distributor_admin):
        r = client.get(api_url("read_user", user_id=unbound_user.id))
        assert r.status_code == status.HTTP_401_UNAUTHORIZED

    def test_with_distributor_admin_role(
        self, client, api_url, distributor_admin, create_user, login_as
    ):
        user = create_user(username="some-existing-user")
        login_as(distributor_admin)
        r = client.get(api_url("read_user", user_id=user.id))
        assert r.status_code == status.HTTP_200_OK
        assert r.json()["id"] == str(user.id)

    def test_for_unbound_user(self, client, api_url, unbound_user, login_as):
        login_as(unbound_user)
        r = client.get(api_url("read_user", user_id=unbound_user.id))
        assert r.status_code == status.HTTP_401_UNAUTHORIZED


class TestDeleteUser:
    def test_auth_missing(self, client, api_url, unbound_user):
        r = client.get(api_url("delete_user", user_id=unbound_user.id))
        assert r.status_code == status.HTTP_401_UNAUTHORIZED

    def test_with_distributor_admin_role(
        self, client, api_url, distributor_admin, unbound_user, login_as
    ):
        login_as(distributor_admin)
        r = client.get(api_url("delete_user", user_id=unbound_user.id))
        assert r.status_code == status.HTTP_200_OK
        assert r.json()["id"] == str(unbound_user.id)

    def test_for_unbound_user(
        self, client, api_url, unbound_user, client_user, login_as
    ):
        login_as(unbound_user)
        r = client.get(api_url("delete_user", user_id=client_user.id))
        assert r.status_code == status.HTTP_401_UNAUTHORIZED


class TestReadUserOrganization:
    def test_auth_missing(self, client, api_url, client_user):
        r = client.get(api_url("read_user_organization", user_id=client_user.id))
        assert r.status_code == status.HTTP_401_UNAUTHORIZED

    def test_for_unbound_user(
        self, client, api_url, unbound_user, client_user, login_as
    ):
        login_as(unbound_user)
        r = client.get(api_url("read_user", user_id=client_user.id))
        assert r.status_code == status.HTTP_401_UNAUTHORIZED

    def test_for_distributor_admin(
        self,
        db,
        client,
        distributor_admin,
        root_organization,
        login_as,
        api_url,
        create_client_org,
    ):
        create_client_org(name="clientA", parent=root_organization)
        login_as(distributor_admin)
        r = client.get(api_url("read_user_organization", user_id=distributor_admin.id))
        assert r.status_code == status.HTTP_200_OK
        expected = schemas.OrganizationFull(
            id=root_organization.id,
            name=OrganizationNameStr(root_organization.name),
            type=root_organization.type,
            created_at=root_organization.created_at,
            parent=None,
        )
        assert r.json() == jsonable_encoder(expected)

    @pytest.mark.usefixtures("login_as_core_service")
    def test_for_core_service(
        self,
        db,
        client,
        client_user,
        client_org,
        api_url,
    ):
        url = api_url("read_user_organization", user_id=client_user.id)
        response = client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["id"] == client_org.id

    def test_without_read_permission_on_organization_raises_404(
        self,
        db,
        client,
        client_user,
        client_org,
        distributor_user,
        root_organization,
        api_url,
        login_as,
    ):
        login_as(client_user)
        url = api_url("read_user_organization", user_id=distributor_user.id)
        response = client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.usefixtures("patch_core_api")
class TestResetUserPassword:
    def test_auth_missing(self, client, unbound_user, api_url):
        url = api_url("reset_user_password", user_id=unbound_user.id)
        r = client.post(url)
        assert r.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.usefixtures("login_as_core_service")
    def test_for_core_service(
        self,
        db,
        client,
        api_url,
        client_user,
        client_org,
    ):
        url = api_url("reset_user_password", user_id=client_user.id)
        response = client.post(url, json={})
        assert response.status_code == status.HTTP_204_NO_CONTENT, (
            response.url,
            response.content,
        )
        assert "content-length" not in response.headers

    @pytest.mark.usefixtures("login_as_core_service")
    def test_mail_sent(
        self,
        db,
        client,
        api_url,
        client_user,
        client_org,
        core_api,
    ):
        url = api_url("reset_user_password", user_id=client_user.id)
        client.post(url, json={})
        assert core_api.mails[-1].recipients[0].email == client_user.email


class TestPreAuthorizeUser:
    def test_auth_missing_returns_401(self, client, api_url):
        url = api_url("pre_authorize_user")
        response = client.post(url, json={})
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.usefixtures("login_as_core_service")
    def test_for_core_service_returns_200(self, client, api_url, client_user):
        payload = schemas.PreAuthContext(email=client_user.email)
        response = client.post(api_url("pre_authorize_user"), json=payload.dict())
        assert response.status_code == status.HTTP_200_OK
        assert "login_url" in response.json()


class TestReadPasswordPolicies:
    def test_auth_missing_returns_401(self, client, api_url):
        url = api_url("read_password_policies")
        response = client.get(url)
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.usefixtures("login_as_core_service")
    def test_happy_path_returns_200_and_policies(self, iam, client, api_url):
        iam.password_policies.configure([PasswordPolicy(id="length", value=13)])
        url = api_url("read_password_policies")
        response = client.get(url)
        assert response.status_code == status.HTTP_200_OK
        policy = authz_password.LengthPasswordPolicy(13)
        assert response.json() == [mappers.password_policy_to_schema(policy)]
