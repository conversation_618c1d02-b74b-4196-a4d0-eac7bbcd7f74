import uuid

import pytest
from starlette import status

from app import schemas
from app.adapters.iam.schemas import ClientBase, ClientScope


class TestSetupServiceAccount:
    @pytest.mark.usefixtures("login_as_core_service")
    def test_setup_service_account_happy_path_204(
        self, client, iam, kc, api_url, create_dummy_role, create_dummy_scope
    ):
        auth_client = iam.clients.add(
            ClientBase(
                client_id=str(uuid.uuid4()),
                service_accounts_enabled=False,
                name=None,
                enabled=True,
                public_client=False,
                bearer_only=False,
            )
        )
        scope = create_dummy_scope()
        role = create_dummy_role()
        iam.client_scopes.add_roles(scope.name, [role.name])

        url = api_url("setup_service_account", client_id=auth_client.client_id)
        config = schemas.ServiceAccountConfig(
            roles=[role.name], scopes=[scope.name], attributes={"foo": ["bar"]}
        )
        response = client.post(url, json=config.dict())
        assert response.status_code == status.HTTP_204_NO_CONTENT, (
            response.url,
            response.content,
            response.request.body,
        )
        assert "content-length" not in response.headers

        client_secret = iam.clients.get_secret(auth_client.client_id)
        access_token = kc.get_pat(
            client_id=auth_client.client_id, client_secret=client_secret
        )
        token_payload = kc.introspect_token(access_token)

        roles = token_payload.get("realm_access", {}).get("roles", [])
        assert role.name in roles, token_payload
        assert scope.name in token_payload["scope"]

        service_account = iam.clients.get_service_account_user(auth_client.client_id)
        assert service_account.attributes == {"foo": ["bar"]}


class TestCreateServiceAccount:
    @pytest.fixture
    def iam(self, in_memory_iam):
        return in_memory_iam

    def test_without_token_returns_401(self, client, api_url):
        response = client.post(api_url("create_service_account"), json={})
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_non_distributor_admin_returns_403(
        self, client, api_url, distributor_user, login_as
    ):
        login_as(distributor_user)
        response = client.post(
            api_url("create_service_account"),
            json=dict(name="foo", permissions=["contacts"]),
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN, (
            response.url,
            response.content,
        )

    def test_happy_path_is_ok(
        self, client, api_url, login_as, distributor_admin, root_organization, iam
    ):
        iam.client_scopes.add(ClientScope(name="contacts"))
        login_as(distributor_admin)
        response = client.post(
            api_url("create_service_account"),
            json=dict(name="Foo", permissions=["contacts"]),
        )
        assert response.status_code == status.HTTP_201_CREATED, (
            response.url,
            response.content,
        )
