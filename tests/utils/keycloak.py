import json
import logging
from pathlib import Path

import httpx
from pydantic import EmailStr, SecretStr, ValidationError
from tenacity import after_log, before_log, retry, stop_after_attempt, wait_fixed

from app import schemas
from app.adapters import iam
from app.adapters.iam.keycloak import Key<PERSON>loakIAM
from app.api.deps import introspect_token
from app.core.config import settings
from app.core.utils import get_keycloak
from app.enums import OrganizationRoleEnum
from app.models import Distributor
from app.services import org_management
from app.typedefs import Username


def import_test_realm(
    admin_username: str = "admin",
    admin_password: str = "admin",
):
    realm_data = get_test_realm_data()
    access_token = get_access_token(admin_password, admin_username)
    with httpx.Client() as client:
        response = client.post(
            settings.KEYCLOAK_URL + "/auth/admin/realms",
            headers=dict(authorization=f"Bearer {access_token}"),
            json=realm_data,
        )
        response.raise_for_status()
    test_organizations_claim()
    return realm_data


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@retry(
    stop=stop_after_attempt(10),
    wait=wait_fixed(1),
    before=before_log(logger, logging.INFO),
    after=after_log(logger, logging.WARN),
)
def test_organizations_claim():
    with get_keycloak() as ka:
        identity_api = KeycloakIAM(ka)
        group = org_management.create_organization_group(
            identity_api,
            organization=Distributor(name="test-claim-root-org", id=1),
        )
        user = identity_api.users.add(
            iam.schemas.UserCreate(
                username=Username("test-claim-admin"),
                email=EmailStr("<EMAIL>"),
                enabled=True,
                email_verified=True,
            )
        )
        password = SecretStr("password")
        identity_api.users.set_password(user.id, password, temporary=False)
        identity_api.groups.add_member(group.path, user.id)
        role = ka.realm_roles.get_by_name(OrganizationRoleEnum.DISTRIBUTOR_ADMIN.value)
        ka.users.add_realm_roles(str(user.id), roles=[role])
        token = get_access_token(
            username=user.email,
            password=password.get_secret_value(),
            realm=settings.KEYCLOAK_REALM,
            client_id=settings.OIDC_UI_CLIENT_ID,
        )
        try:
            introspect_token(token)
        except ValidationError as e:
            logging.warning(str(e))
            raise e
        finally:
            identity_api.groups.remove(group.path)
            identity_api.users.remove(user.id)


def delete_test_realm(admin_username: str = "admin", admin_password: str = "admin"):
    realm_data = get_test_realm_data()
    realm_name = realm_data["realm"]
    access_token = get_access_token(admin_username, admin_password)
    with httpx.Client() as http_client:
        response = http_client.delete(
            settings.KEYCLOAK_URL + f"/auth/admin/realms/{realm_name}",
            headers=dict(authorization=f"Bearer {access_token}"),
        )
        response.raise_for_status()


def get_access_token(
    username: str,
    password: str,
    realm: str = "master",
    client_id: str = "admin-cli",
) -> str:
    token_path = f"/auth/realms/{realm}/protocol/openid-connect/token"
    with httpx.Client() as http_client:
        response = http_client.post(
            settings.KEYCLOAK_URL + token_path,
            data=dict(
                grant_type="password",
                username=username,
                password=password,
                client_id=client_id,
                client_secret="203dfcd3-cee1-4aa3-acd5-98604cdd465b",
            ),
        )
        response.raise_for_status()
        token_data = response.json()
        access_token = token_data["access_token"]
        return access_token


def get_test_realm_data():
    filename = "realm.json"
    realm_json_path = Path(__file__).parent.parent / "data" / filename
    realm_data = json.load(realm_json_path.open())
    return realm_data


def access_token_for(user: schemas.KeycloakUserCreate) -> str:
    password = user.password.get_secret_value()  # type:ignore
    token = get_access_token(
        username=user.username,  # type:ignore
        password=password,
        realm=settings.KEYCLOAK_REALM,
        client_id=settings.OIDC_UI_CLIENT_ID,
    )
    assert token
    return token
