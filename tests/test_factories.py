import datetime

import pytest
from factory import FactoryError
from factory.base import Factory

from app.adapters.platform_api_client.schemas import (
    AuditLogModel,
    AuditLogResponseModel,
)
from app.enums import OrganizationTypeEnum
from app.models import Client, Distributor
from app.models.organization import Partner
from tests import factories


def test_organization():
    with pytest.raises(FactoryError):
        factories.OrganizationFactory()


def test_distributor(db):
    factories.DistributorFactory(name="Acme")
    distributor = db.query(Distributor).filter_by(name="Acme").first()
    assert distributor.type == OrganizationTypeEnum.DISTRIBUTOR


def test_distributor_parent(db):
    root = factories.DistributorFactory()
    factories.DistributorFactory(name="Acme", parent=root)
    distributor = db.query(Distributor).filter_by(name="Acme").first()
    assert distributor.type == OrganizationTypeEnum.DISTRIBUTOR
    assert distributor.parent.id == root.id
    assert distributor.parent.type == root.type


def test_client_default_distributor(db):
    factories.ClientFactory(name="EvilCo")
    client_org = db.query(Client).filter_by(name="EvilCo").first()
    assert client_org.type == OrganizationTypeEnum.CLIENT
    assert client_org.parent_type == OrganizationTypeEnum.DISTRIBUTOR
    assert client_org.parent_id


def test_partner_default_distributor(db):
    factories.PartnerFactory(name="Buddy")
    partner = db.query(Partner).filter_by(name="Buddy").first()
    assert partner.type == OrganizationTypeEnum.PARTNER
    assert partner.parent_type == OrganizationTypeEnum.DISTRIBUTOR
    assert partner.parent_id


def test_client_custom_distributor(db):
    distributor = factories.DistributorFactory()
    factories.ClientFactory(name="EvilCo", parent=distributor)
    client_org = db.query(Client).filter_by(name="EvilCo").first()
    assert client_org.type == OrganizationTypeEnum.CLIENT
    assert client_org.parent.id == distributor.id
    assert client_org.parent.type == distributor.type


def test_default_for_created_at_is_static(db):
    client = factories.ClientFactory()
    assert client.created_at == factories.static_utc_now()
    assert client.parent.created_at == factories.static_utc_now()


def test_override_now_for_created_at(db):
    now = datetime.datetime.now(datetime.timezone.utc)
    client = factories.ClientFactory(now=lambda: now)
    assert client.created_at == now
    assert client.parent.created_at == factories.static_utc_now()


def test_override_now_for_created_at_sub_factory(db):
    now = datetime.datetime.now(datetime.timezone.utc)
    now_sub = datetime.datetime.now(datetime.timezone.utc)
    client = factories.ClientFactory(now=lambda: now, parent__now=lambda: now_sub)
    assert client.created_at == now
    assert client.parent.created_at == now_sub


class Audit(Factory):
    user = "<EMAIL>"
    ipAddress = "*********"
    event = "POST"
    request = "organization/report"
    source = "organization"
    payload = {"id": "key"}

    class Meta:
        model = AuditLogModel


class AuditLogResponseModel(Factory):
    id = (None,)
    status = (None,)
    error = (None,)
    message = None

    class Meta:
        model = AuditLogResponseModel
