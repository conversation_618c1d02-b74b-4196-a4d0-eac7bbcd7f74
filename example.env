APPLICATION_ENV=local
#https://fastapi.tiangolo.com/advanced/behind-a-proxy/#proxy-with-a-stripped-path-prefix
ROOT_PATH=<url-base-path>

# DATABASE

DATABASE_URI=postgresql://<DB_USER>:<DB_PASSWORD>>@<DB_SERVER>/<DB_NAME>

# Keycloak

KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=realm-name
OIDC_AUDIENCE=client-name
OIDC_CLIENT_ID=client-name
OIDC_CLIENT_SECRET=secret

EMAIL_DELIVERY_URL=http://localhost:8000
PARTNER_APPS_URL=http://localhost:8000
LANDING_PAGE_URL=http://localhost
EXTERNAL_LANDING_PAGE=
EXTERNAL_IDP=
PRE_AUTH_TOKEN_SECRET=
CONTACTS_URL=http://localhost

ROOT_ORG_NAME=
ROOT_ORG_ADMIN_EMAIL=
ROOT_ORG_ADMIN_PASSWORD=
AUDIT_LOG_URI=http://localhost:8000

# Optional

# For default CLIENT_ID field in the Swagger UI
OIDC_UI_CLIENT_ID=organizations-ui

# For k8s readiness and liveness, comment it out for local setup
HEALTH_CHECK_SECRET=some-secret
