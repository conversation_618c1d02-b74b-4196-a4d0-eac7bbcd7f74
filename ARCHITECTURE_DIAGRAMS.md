# Organizations API - Architecture Diagrams

This document contains comprehensive architectural diagrams for the Organizations API project. These diagrams provide visual representations of the system architecture, data models, workflows, and deployment patterns.

## Diagram Overview

### 1. System Architecture Diagram
**Purpose**: Shows the high-level system architecture and component interactions
**Key Components**:
- Client layer (Web UI, API clients, Mobile apps)
- API Gateway for load balancing and routing
- Organizations API with FastAPI, authentication middleware, and Oso authorization
- External services integration (Keycloak, Email, Partner Apps, Contacts, Audit)
- Data layer (PostgreSQL, Redis, S3)
- Monitoring stack (Prometheus, OpenTelemetry, Centralized Logs)

**Use Cases**:
- Understanding overall system design
- Identifying service dependencies
- Planning integration points
- Troubleshooting system-wide issues

### 2. Database Entity Relationship Diagram (ERD)
**Purpose**: Illustrates the database schema and relationships between entities
**Key Entities**:
- **Organization**: Base entity with polymorphic inheritance
- **Distributor, Client, Partner**: Specialized organization types
- **Domain**: Email domains associated with organizations
- **PMNCode**: Payment network codes for organizations
- **UserFile**: File uploads with associated reports
- **FileReport, UserReport**: Processing results and user data

**Relationships**:
- Self-referential hierarchy for organizations (parent-child)
- One-to-many relationships for domains, PMN codes, and files
- Polymorphic inheritance for organization types

**Use Cases**:
- Database design and optimization
- Understanding data relationships
- Query planning and optimization
- Migration planning

### 3. Organization Hierarchy Structure
**Purpose**: Visualizes the hierarchical organization structure
**Hierarchy Levels**:
- **Root**: Nextgen Clearing (top-level distributor)
- **Level 1**: Main distributors (Regional, National, International)
- **Level 2**: Sub-distributors and direct clients
- **Level 3**: End clients under sub-distributors

**Color Coding**:
- Orange: Root organization
- Blue: Main distributors
- Light Blue: Sub-distributors
- Green: Clients
- Purple: Partners

**Use Cases**:
- Understanding business hierarchy
- Authorization scope planning
- User access control design
- Reporting structure visualization

### 4. Authentication & Authorization Flow
**Purpose**: Shows the complete auth flow from request to response
**Flow Steps**:
1. Client sends request with JWT token
2. API Gateway forwards to Organizations API
3. Token validation with Keycloak
4. User/Service info extraction
5. Organization context retrieval
6. Oso authorization policy evaluation
7. Business logic execution or error response

**Decision Points**:
- Token validity check
- Authorization policy evaluation
- Success/failure response paths

**Use Cases**:
- Security implementation understanding
- Debugging authentication issues
- Policy design and testing
- Integration planning

### 5. API Request Processing Flow
**Purpose**: Detailed flowchart of API request processing
**Processing Stages**:
- **Authentication**: Middleware token validation
- **Dependency Injection**: Actor info extraction
- **Input Validation**: Schema validation with Pydantic
- **Authorization**: Oso policy evaluation
- **Business Logic**: Core functionality execution
- **External Calls**: Integration with external services
- **Response**: Schema application and response formatting

**Error Handling**:
- 401 Unauthorized (invalid token)
- 422 Validation Error (invalid input)
- 403 Forbidden (authorization failure)
- 404 Not Found (resource not found)
- 500 Database Error (internal errors)
- 502/503 Service Error (external service issues)

**Use Cases**:
- API development and testing
- Error handling implementation
- Performance optimization
- Debugging request issues

### 6. Deployment Architecture
**Purpose**: Shows the production deployment setup
**Infrastructure Components**:
- **Load Balancer**: Traffic distribution and SSL termination
- **Kubernetes Cluster**: Container orchestration
- **API Pods**: Multiple instances with Gunicorn + Uvicorn
- **Configuration**: ConfigMaps and Secrets management
- **Storage**: Persistent volumes for file storage

**External Dependencies**:
- **Databases**: PostgreSQL (primary), Redis (cache)
- **External Services**: Keycloak, AWS S3
- **Connected Platform**: Email, Partner Apps, Contacts, Audit services

**Monitoring Stack**:
- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboards
- **Jaeger**: Distributed tracing
- **ELK Stack**: Centralized logging

**Use Cases**:
- Deployment planning and execution
- Scaling strategy development
- Monitoring setup and configuration
- Disaster recovery planning

### 7. User Management Workflow
**Purpose**: Detailed sequence diagram for user lifecycle management
**Key Operations**:
- **User Creation**: Admin creates user in organization with role assignment
- **Role Updates**: Modify user permissions within organization
- **User Removal**: Remove user from organization (with self-removal protection)

**Integration Points**:
- Keycloak for user account and group management
- Email service for welcome notifications
- Audit service for compliance logging
- Database for organization validation

**Use Cases**:
- Implementing user management features
- Understanding permission flows
- Debugging user access issues
- Compliance and audit trail design

### 8. Service Account Creation Flow
**Purpose**: Shows the complete service account setup process
**Key Steps**:
- Service account client creation in Keycloak
- OAuth2 scope configuration
- Organization group membership assignment
- Role-based permission setup
- Client credentials flow usage

**Security Features**:
- Client ID validation against requesting token
- Scope-based permission control
- Organization context enforcement
- Audit logging for service account operations

**Use Cases**:
- Implementing service-to-service authentication
- Configuring application permissions
- Debugging service account issues
- Security review and compliance

### 9. File Upload and Processing Workflow
**Purpose**: Complete file upload and background processing flow
**Processing Stages**:
- **Upload**: File validation and S3 storage
- **Metadata Storage**: Database record creation
- **Background Processing**: Asynchronous file parsing
- **Result Storage**: User data extraction and validation
- **Notification**: Email alerts for completion/errors

**Components**:
- S3 for file storage
- Database for metadata and results
- Background processor for file parsing
- Email service for notifications

**Use Cases**:
- Implementing file upload features
- Designing background processing systems
- Error handling and user notifications
- Performance optimization for large files

### 10. Code Architecture and Layer Structure
**Purpose**: Shows the layered architecture and code organization
**Architecture Layers**:
- **Presentation Layer**: API endpoints, schemas, and dependency injection
- **Business Logic Layer**: Core services and business rules
- **Authorization Layer**: Oso engine and Polar policies
- **Data Access Layer**: SQLAlchemy models and database sessions
- **Integration Layer**: External service adapters
- **Infrastructure Layer**: Configuration, security, and utilities

**Key Components**:
- **API Endpoints**: RESTful endpoints organized by version
- **Services**: Business logic separated by domain (org, user, auth)
- **Models**: Database entities with relationships
- **Adapters**: External service integration patterns
- **Configuration**: Environment-based settings management

**Design Principles**:
- Separation of concerns across layers
- Dependency injection for testability
- Adapter pattern for external integrations
- Policy-based authorization
- Schema validation at boundaries

**Use Cases**:
- Understanding code organization
- Planning new feature development
- Refactoring and maintenance
- Onboarding new developers

## Diagram Usage Guidelines

### For Developers
- **System Architecture**: Understand service boundaries and integration points
- **ERD**: Design database queries and understand data relationships
- **Auth Flow**: Implement security features and debug access issues
- **Request Flow**: Optimize API performance and handle errors properly

### For DevOps/SRE
- **Deployment Architecture**: Plan infrastructure and scaling strategies
- **System Architecture**: Monitor service dependencies and health
- **Request Flow**: Set up monitoring and alerting for error conditions

### For Product/Business
- **Organization Hierarchy**: Understand business model and user access patterns
- **System Architecture**: Assess integration capabilities and limitations
- **Deployment Architecture**: Evaluate scalability and reliability

### For Security Teams
- **Auth Flow**: Review security controls and access patterns
- **System Architecture**: Identify security boundaries and data flows
- **Deployment Architecture**: Assess infrastructure security posture

## Diagram Maintenance

### When to Update Diagrams
- **New Features**: Add new components or modify existing flows
- **Architecture Changes**: Update system boundaries or integration points
- **Database Changes**: Reflect schema modifications in ERD
- **Deployment Changes**: Update infrastructure or monitoring components

### Diagram Versioning
- Keep diagrams in sync with code changes
- Update during major releases or architectural changes
- Document changes in commit messages
- Review diagrams during architecture reviews

### Tools and Standards
- **Mermaid**: Primary diagramming tool for version control compatibility
- **Consistent Styling**: Use standard colors and shapes across diagrams
- **Clear Labels**: Ensure all components and relationships are clearly labeled
- **Regular Reviews**: Include diagram updates in code review process

## Integration with Documentation

These diagrams complement the main project documentation (`PROJECT_DOCUMENTATION.md`) by providing:
- Visual representation of written descriptions
- Quick reference for complex relationships
- Onboarding materials for new team members
- Architecture decision documentation

## Troubleshooting with Diagrams

### Common Issues and Relevant Diagrams
- **Authentication Problems**: Reference Auth Flow diagram
- **Database Issues**: Check ERD for relationship constraints
- **Performance Issues**: Review Request Flow for bottlenecks
- **Deployment Problems**: Consult Deployment Architecture
- **Integration Failures**: Check System Architecture for service dependencies

### Debugging Workflow
1. Identify the problem area (auth, data, deployment, etc.)
2. Reference the relevant diagram
3. Trace the flow or relationship causing issues
4. Verify configuration and implementation against diagram
5. Update diagram if architecture has changed

This comprehensive set of diagrams provides a complete visual reference for the Organizations API architecture, supporting development, deployment, and maintenance activities across all team roles.
