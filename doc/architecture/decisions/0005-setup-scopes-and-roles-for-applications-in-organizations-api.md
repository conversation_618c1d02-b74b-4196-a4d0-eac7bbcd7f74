# 5. Setup scope and role for Applications in organizations-api

Date: 2022-01-14

## Status

Accepted

## Context

Specific scope and role is required in Keycloak for allowing applications to perform service-to-service calls.
organizations-api is the only core service that depends on Keycloak Admin API.

## Decision

Add the Keycloak setup for applications to organizations-api manage command.

## Consequences

Keycloak setup for applications' service accounts is done during deployment of organizations-api. organizations-api
contains code related to applications.
