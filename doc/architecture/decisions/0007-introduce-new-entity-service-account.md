# 7. Introduce new entity - Service Account

Date: 2022-04-29

## Status

Accepted

Extended by [8. Create service accounts under organization](0008-create-service-accounts-under-organization.md)

Extended by [9. Add service account to the organization group](0009-add-service-account-to-the-organization-group.md)

Extended by [10. Use OAuth scopes to configure permissions of the service account](0010-use-oauth-scopes-to-configure-permissions-of-the-service-account.md)

## Context

Organizations represented on the platform need the ability to integrate their programming services with the API of the
platform.
Those services cannot be considered as the applications because they do not have a user interface and intended to
perform service-to-service communication only.

## Decision

Introduce a new entity - Service Account.

## Consequences

A service account allows organizations to integrate their services with the API of the platform.
Change requires the support of the new entity and its relationship.
Apps in the App-Store also use service accounts(in terms of Keycloak) too, which can lead to
confusion in understanding the meaning of these terms in different contexts.
