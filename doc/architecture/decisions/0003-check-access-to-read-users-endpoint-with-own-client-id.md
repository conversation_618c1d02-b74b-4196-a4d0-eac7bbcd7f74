# 3. Check access to `read_users` endpoint with own `client_id`

Date: 2021-12-10

## Status

Accepted

Related to:  [6. Accept only service tokens for the setup service account endpoint](0006-check-access-to-setup-service-account-endpoint-with-own-client-id.md)

## Context

We need to make a call from subscriptions-api to `read_users`(`/users`) endpoint.

The call cannot be made on behalf of a user.

The call on behalf of a service will allow to read the endpoint directly and we don't want that.

We need to check that the caller service is the trusted one.

subscriptions-api and organizations-api using the same `client_id`.

## Decision

Check that the `client_id` of the calling service matches the `client_id` of the organizations-api to authorize the
service.

## Consequences

subscriptions-api service can make a call to the `read_users` endpoint.

With the growth of services and/or using different clients for different core services the approach will not work.
