# 4. Add endpoint for service account setup

Date: 2022-01-14

## Status

Accepted

## Context

Applications requires service accounts to perform service-to-service calls. applications-api make calls to
organizations-api. Keycloak is used for authentication and identity management. Identity management is done via Keycloak
Admin API. Currently, only organizations-api depends on Keycloak Admin API.

## Decision

Add API endpoint to setup service account in organizations-api.

## Consequences

applications-api still depends only on organizations-api. organizations-api provide extra functionality(the service
account setup).
