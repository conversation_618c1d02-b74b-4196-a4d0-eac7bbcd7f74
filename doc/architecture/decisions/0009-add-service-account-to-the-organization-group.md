# 9. Add service account to the organization group

Date: 2022-04-29

## Status

Accepted

Extends [7. Introduce new entity - Service Account](0007-introduce-new-entity-service-account.md)

## Context

Service accounts are created under organizations and have alot in common with users from the perspective of
authorization and resource access. Keycloak uses users to implement the service account functionality.
Attributes of the organization are added to the corresponding group and passed to the token claims of the users.

## Decision

Add service account to the organization group.

## Consequences

Allows the platform to use the relation between organization and service account in authorization policies.
