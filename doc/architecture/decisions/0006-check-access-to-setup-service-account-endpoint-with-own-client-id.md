# 6. Check access to setup service account endpoint with own `client_id`

Date: 2022-01-14

## Status

Accepted

Related to: [3. Check access to `read_users` endpoint with own `client_id`](0003-check-access-to-read-users-endpoint-with-own-client-id.md)

## Context

We need to setup service account from applications-api for application.

The level of the interface is too low and we don't want to expose it to users.

## Decision

Check that the `client_id` of the calling service matches the `client_id` of the organizations-api to authorize the
service.

## Consequences

applications-api service can make a call to the `setup_service_account` endpoint.

With the growth of services and/or using different clients for different core services the approach will not work.
