# 10. Use OAuth scopes to configure permissions of the service account

Date: 2022-04-29

## Status

Proposed

Extends [7. Introduce new entity - Service Account](0007-introduce-new-entity-service-account.md)

## Context

There is a need to limit the set of permissions that are granted to the service account: per resource type, per action.
Keycloak provide the ability to choose allowed scopes for the OAuth client of the service account.
The scopes can be included to the token claims and used during the authorization(for example in Istio
Authorization Policy).

## Decision

Use OAuth scopes to configure the permissions of the service account.

Scope examples:

```shell
contacts/
  contacts.list-contacts
  contacts.view-contact 
  contacts.create-contact
  contacts.delete-contact
  contacts.update-hubspot
organizations/
  organizations.create-client
  organizations.list-descendants
  organizations.create-user
  organizations.list-users
  organizations.impersonate-user
```

## Consequences

This will introduce the coarse grained permission configurations for the service accounts(resource type /
action).
Fine-grained permission configurations still need to be developed(per resource, per field, etc.) if required.
Scopes can be used to authorize requests in Istio, for example:

```yaml
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  namespace: core-dev
  name: contacts-authorization-policy
spec:
  selector:
    matchLabels:
      app: contacts-api
  action: AUDIT
  rules:
    - when:
        - key: request.auth.claims[scope]
          values: [ "contacts*" ]
```
