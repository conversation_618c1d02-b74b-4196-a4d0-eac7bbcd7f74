# Changelog

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).
### Unreleased

### Added

### Changed

### Removed

### Fixed

## Released
1.4.20 18-07-2025

### Added

### Changed

### Removed

### Fixed
[SIM] - Validation-in-number-of-role
[SIM] - Bug-for-keyerror-in-userdetail

## Released
1.4.19 18-07-2025

### Added

### Changed

### Removed

### Fixed
[SIM] - Validation-in-number-of-role
[SIM] - Bug-for-keyerror-in-userdetail

## Released
1.4.18 16-07-2025

### Added

### Changed
[SIM-3212] - update password validation added for generating access token
### Removed

### Fixed
[SIM-3192] - Update-scripts-26.2.4

## Released
1.4.17 10-07-2025


### Added
[CP-66] - Metrics Endpoint for Prometheus
[CP-373] - Organizations endpoint path validation
[CP-383] - org-id-in-read-users-API
[CP-393] - get-single-user-details-api-v2

### Changed
[CP-368] - Descendant API 
### Removed

### Fixed
[CP-419] - ca-delete-user
[CP-225] - security-scan-issue
[CP-265] - ordering-read-user

## Released
1.4.16 20-06-2025

### Added

### Changed
[CP-410] - handle-user-bulk-creation

### Removed

### Fixed

### Released
1.4.15 05-06-2025



### Added
[CP-253] - Audit Service Added
[CP-242] - read-users-and-read-organization-v2-api
[CP-140] - last-login-for-users
[CP-265] - enhance-create-user-api-v2
[CP-352] - ordering-in-read-users-v2-apis
[CP-304] Upload bulk user csv
[CP-244] - update-user-API-v2
[CP-288] - Get audit
[CP-305] - Create bulk user
[CP-364] - fix-impersonation

### Changed

### Removed

### Fixed

## Released
1.4.14 02-06-2025

### Added
Revoke token by user id
### Changed

### Removed

### Fixed

## Released
1.4.13 05-05-2025

### Released


### Added 
Create organization with PMN and domain
[CP-289] - Migrate-data-from-kc-to-redis
### Changed
[SIM-2935] - trivy_fix poetry packages updated for certifi, gunicorn, opentelemetry-instrumentation, protobuf, python-multipart, setuptools, virtualenv

### Removed

### Fixed
1.4.12 23-04-2025

### Added
[CP-205] - create-client-api-adds-pmns-and-domain-field
[CP] - CP-get_object_refactor
[CP-206] - update-client-api-adds-pmns-and-domain-field
[CP-261] - refactor-delete-client-api

### Changed

### Removed

### Fixed

## Released
1.4.11 22-04-2025

### Added

### Changed

### Removed

### Fixed
[SIM-2999] - Keycloak version issue

## Released
1.4.10 22-04-2025

### Added

### Changed
[SIM-2992] - Custom reset password email template, logging added

### Removed

### Fixed
[CP-146] - pipeline-failure-due-to-poetry-version-mismatch-in-nv-2-core-organization-api-service-on-cp-qa-env
## Released
1.4.9 08-10-2024

### Added

### Changed

### Removed

### Fixed
[CP-86] - Update client organization

## Released
1.4.8 03-10-2024

### Added
[CP-67] - Keycloak version upgradation
### Changed

### Removed

### Fixed
[CP-41] - CP PROD Organization Page - 500 Internal Server Error (Descendants issues resolved)
## Released
1.4.5 13-06-2024

### Added
[SPOG-1776] - Create BT user with reset password
[SPOG-2085] - Add confluence document link and email template change
[SPOG-2255] - Add new template for send email V2/createuser create new API Endpoint
[SPOG-2387] - Added access token API. Improvements create user v2 API

### Changed
[SPOG-1930] - Change clientId to client_id in auth.py file
[SPOG-1998] - Change response of get all users
[SPOG-2306] - Create user with 2 roles

### Removed

### Fixed
