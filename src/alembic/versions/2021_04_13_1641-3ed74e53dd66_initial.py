"""initial

Revision ID: 3ed74e53dd66
Revises:
Create Date: 2021-04-13 16:41:50.624453

"""
import sqlalchemy as sa
from alembic import op

import app.db.types

# revision identifiers, used by Alembic.
revision = "3ed74e53dd66"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "organization",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("name", sa.String(length=256), nullable=False),
        sa.Column("parent_id", sa.Integer(), nullable=True),
        sa.Column("external_id", app.db.types.GUID(), nullable=True),
        sa.Column("group_id", app.db.types.GUID(), nullable=True),
        sa.ForeignKeyConstraint(
            ["parent_id"],
            ["organization.id"],
            name=op.f("fk_organization_parent_id_organization"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_organization")),
        sa.UniqueConstraint("name", name=op.f("uq_organization_name")),
    )
    op.create_index(
        op.f("ix_organization_external_id"),
        "organization",
        ["external_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_organization_group_id"), "organization", ["group_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_organization_group_id"), table_name="organization")
    op.drop_index(op.f("ix_organization_external_id"), table_name="organization")
    op.drop_table("organization")
    # ### end Alembic commands ###
