"""create_domain_table

Revision ID: c438dec1cf37
Revises: abd631aff220
Create Date: 2025-04-03 12:18:53.948254

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "c438dec1cf37"
down_revision = "abd631aff220"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "domain",
        sa.Column(
            "id",
            sa.INTEGER(),
            sa.Identity(
                always=False,
                start=1,
                increment=1,
                minvalue=1,
                maxvalue=2147483647,
                cycle=False,
                cache=1,
            ),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("org_id", sa.Integer(), nullable=False),
        sa.Column(
            "domain_name",
            sa.String(length=256),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", name="pk_domain"),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organization.id"],
            name="fk_org_id_organization_and_domain",
            ondelete="CASCADE",
        ),
        sa.UniqueConstraint("domain_name", "org_id", name="uq_domain_name_org_id"),
    )
    op.create_index(
        op.f("ix_org_id_domain"),
        "domain",
        ["org_id"],
        unique=False,
    )


def downgrade():
    op.drop_table("domain")
