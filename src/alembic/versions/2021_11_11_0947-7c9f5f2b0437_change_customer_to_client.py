"""Change Customer to Client

Revision ID: 7c9f5f2b0437
Revises: cac367f23471
Create Date: 2021-11-11 09:47:35.478255

"""
from enum import Enum

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "7c9f5f2b0437"
down_revision = "cac367f23471"
branch_labels = None
depends_on = None


class NewOrganizationType(str, Enum):
    DISTRIBUTOR = "DISTRIBUTOR"
    CLIENT = "CLIENT"


class OldOrganizationType(str, Enum):
    DISTRIBUTOR = "DISTRIBUTOR"
    CUSTOMER = "CUSTOMER"


def upgrade():
    op.drop_constraint(
        constraint_name=op.f("ck_organization_type"),
        table_name="organization",
        type_="check",
    )
    op.drop_constraint(
        constraint_name=op.f("ck_organization_customer_has_parent"),
        table_name="organization",
        type_="check",
    )
    op.execute(
        """
        UPDATE organization
        SET type='CLIENT'
        WHERE type='CUSTOMER'
        """
    )
    op.execute(
        """
        UPDATE organization
        SET parent_type='CLIENT'
        WHERE parent_type='CUSTOMER'
        """
    )
    op.alter_column(
        "organization",
        "type",
        type_=sa.Enum(
            NewOrganizationType,
            native_enum=False,
            create_constraint=True,
            name="type",
        ),
    )
    op.alter_column(
        "organization",
        "parent_type",
        type_=sa.Enum(
            NewOrganizationType,
            native_enum=False,
            name="parent_type",
        ),
    )
    op.create_check_constraint(
        op.f("ck_organization_client_has_parent"),
        "organization",
        "NOT (parent_id IS NULL AND type = 'CLIENT')",
    )


def downgrade():
    op.drop_constraint(
        constraint_name=op.f("ck_organization_type"),
        table_name="organization",
        type_="check",
    )
    op.drop_constraint(
        constraint_name=op.f("ck_organization_client_has_parent"),
        table_name="organization",
        type_="check",
    )
    op.execute(
        """
        UPDATE organization
        SET type='CUSTOMER'
        WHERE type='CLIENT'
        """
    )
    op.execute(
        """
        UPDATE organization
        SET parent_type='CUSTOMER'
        WHERE parent_type='CLIENT'
        """
    )
    op.alter_column(
        "organization",
        "type",
        type_=sa.Enum(
            OldOrganizationType,
            native_enum=False,
            create_constraint=True,
            name="type",
        ),
    )
    op.alter_column(
        "organization",
        "parent_type",
        type_=sa.Enum(
            OldOrganizationType,
            native_enum=False,
            name="parent_type",
        ),
    )
    op.create_check_constraint(
        op.f("ck_organization_customer_has_parent"),
        "organization",
        "NOT (parent_id IS NULL AND type = 'CUSTOMER')",
    )
