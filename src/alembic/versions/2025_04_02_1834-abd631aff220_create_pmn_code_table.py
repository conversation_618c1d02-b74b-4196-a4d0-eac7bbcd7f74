"""create_pmn_code_table

Revision ID: abd631aff220
Revises: b2018cdf3448
Create Date: 2025-04-02 18:34:13.629504

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "abd631aff220"
down_revision = "b2018cdf3448"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "pmn_code",
        sa.Column(
            "id",
            sa.INTEGER(),
            sa.Identity(
                always=False,
                start=1,
                increment=1,
                minvalue=1,
                maxvalue=2147483647,
                cycle=False,
                cache=1,
            ),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("org_id", sa.Integer(), nullable=False),
        sa.Column(
            "pmn_code",
            sa.String(length=5),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", name="pk_pmn_code"),
        sa.UniqueConstraint("pmn_code", name="uq_pmn_code"),
        sa.ForeignKeyConstraint(
            ["org_id"],
            ["organization.id"],
            name="fk_org_id_organization_and_pmn_code",
            ondelete="CASCADE",
        ),
    )
    op.create_index(
        op.f("ix_org_id_pmn_code"),
        "pmn_code",
        ["org_id"],
        unique=False,
    )


def downgrade():
    op.drop_table("pmn_code")
