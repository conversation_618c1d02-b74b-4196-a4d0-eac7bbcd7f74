"""Add PARTNER organization type

Revision ID: d30e3288ac7f
Revises: ba1a07b17c5b
Create Date: 2022-01-31 17:03:19.048389

"""
from enum import Enum

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "d30e3288ac7f"
down_revision = "ba1a07b17c5b"
branch_labels = None
depends_on = None


class NewOrganizationType(str, Enum):
    DISTRIBUTOR = "DISTRIBUTOR"
    CLIENT = "CLIENT"
    PARTNER = "PARTNER"


class OldOrganizationType(str, Enum):
    DISTRIBUTOR = "DISTRIBUTOR"
    CLIENT = "CLIENT"


def upgrade():
    op.drop_constraint(
        constraint_name=op.f("ck_organization_type"),
        table_name="organization",
        type_="check",
    )
    op.drop_constraint(
        constraint_name=op.f("ck_organization_client_has_parent"),
        table_name="organization",
        type_="check",
    )
    op.alter_column(
        "organization",
        "type",
        type_=sa.Enum(
            NewOrganizationType,
            native_enum=False,
            create_constraint=True,
            name="type",
        ),
    )
    op.alter_column(
        "organization",
        "parent_type",
        type_=sa.Enum(
            NewOrganizationType,
            native_enum=False,
            name="parent_type",
        ),
    )
    op.create_check_constraint(
        op.f("ck_organization_non_distributor_has_parent"),
        "organization",
        "NOT (parent_id IS NULL AND type != 'DISTRIBUTOR')",
    )


def downgrade():
    op.drop_constraint(
        constraint_name=op.f("ck_organization_non_distributor_has_parent"),
        table_name="organization",
        type_="check",
    )
    op.drop_constraint(
        constraint_name=op.f("ck_organization_type"),
        table_name="organization",
        type_="check",
    )
    op.alter_column(
        "organization",
        "type",
        type_=sa.Enum(
            OldOrganizationType,
            native_enum=False,
            create_constraint=True,
            name="type",
        ),
    )
    op.alter_column(
        "organization",
        "parent_type",
        type_=sa.Enum(
            OldOrganizationType,
            native_enum=False,
            name="parent_type",
        ),
    )
    op.create_check_constraint(
        op.f("ck_organization_client_has_parent"),
        "organization",
        "NOT (parent_id IS NULL AND type = 'CLIENT')",
    )
