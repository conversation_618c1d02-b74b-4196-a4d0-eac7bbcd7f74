"""Add Organization.created_at

Revision ID: a461d78262a4
Revises: 7c9f5f2b0437
Create Date: 2021-11-24 17:33:32.864940

"""
import sqlalchemy as sa
from alembic import op

import app.db.types

# revision identifiers, used by Alembic.
revision = "a461d78262a4"
down_revision = "7c9f5f2b0437"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "organization",
        sa.Column(
            "created_at",
            app.db.types.TZTimestamp(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("organization", "created_at")
    # ### end Alembic commands ###
