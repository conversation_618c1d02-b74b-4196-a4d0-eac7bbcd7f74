"""create_bulk_user_tables

Revision ID: e5f8a2d71c9b
Revises: c438dec1cf37
Create Date: 2025-04-04 15:00:13.629504

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

revision = "e5f8a2d71c9b"
down_revision = "c438dec1cf37"
branch_labels = None
depends_on = None


def upgrade():
    file_status_enum = postgresql.ENUM(
        "completed",
        "in_progress",
        "failed",
        "completed_with_errors",
        name="file_status_enum",
    )
    file_status_enum.create(op.get_bind())

    # Create user_file table with UUID column
    op.create_table(
        "user_file",
        sa.Column(
            "id",
            sa.INTEGER(),
            sa.Identity(
                always=False,
                start=1,
            ),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column(
            "uuid",
            postgresql.UUID(),
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
            unique=True,
        ),
        sa.Column("file_name", sa.String(length=50), nullable=False),
        sa.Column("path", sa.String(length=1024), nullable=False),
        sa.Column("organization_id", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id", name="pk_user_file"),
        sa.ForeignKeyConstraint(
            ["organization_id"],
            ["organization.id"],
            name="fk_organization_id_user_file",
            ondelete="CASCADE",
        ),
    )
    op.create_index(
        op.f("ix_organization_id_user_file"),
        "user_file",
        ["organization_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_uuid_user_file"),
        "user_file",
        ["uuid"],
        unique=True,
    )

    op.create_table(
        "file_report",
        sa.Column(
            "id",
            sa.INTEGER(),
            sa.Identity(always=False, start=1),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("user_file_id", sa.Integer(), nullable=False),
        sa.Column(
            "status",
            sa.Enum(
                "completed",
                "in_progress",
                "failed",
                "completed_with_errors",
                name="file_status_enum",
                native_enum=False,
            ),
            nullable=False,
            default="in_progress",
        ),
        sa.Column("total_rows", sa.Integer(), nullable=True),
        sa.Column("valid_rows", sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint("id", name="pk_file_report"),
        sa.ForeignKeyConstraint(
            ["user_file_id"],
            ["user_file.id"],
            name="fk_user_file_id_file_report",
            ondelete="CASCADE",
        ),
    )
    op.create_index(
        op.f("ix_user_file_id_file_report"),
        "file_report",
        ["user_file_id"],
        unique=False,
    )

    op.create_table(
        "user_report",
        sa.Column(
            "id",
            sa.INTEGER(),
            sa.Identity(always=False, start=1),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("user_file_id", sa.Integer(), nullable=False),
        sa.Column("first_name", sa.String(length=50), nullable=False),
        sa.Column("last_name", sa.String(length=50), nullable=False),
        sa.Column("email", sa.String(length=254), nullable=False),
        sa.Column("reason", sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint("id", name="pk_user_report"),
        sa.ForeignKeyConstraint(
            ["user_file_id"],
            ["user_file.id"],
            name="fk_user_file_id_user_report",
            ondelete="CASCADE",
        ),
    )
    op.create_index(
        op.f("ix_user_file_id_user_report"),
        "user_report",
        ["user_file_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_email_user_report"),
        "user_report",
        ["email"],
        unique=False,
    )


def downgrade():
    op.drop_table("user_report")
    op.drop_table("file_report")
    op.drop_table("user_file")
    postgresql.ENUM(name="file_status_enum").drop(op.get_bind())
