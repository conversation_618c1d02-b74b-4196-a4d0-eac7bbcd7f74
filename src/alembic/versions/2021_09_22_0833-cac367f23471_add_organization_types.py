"""Add Organization Types

Revision ID: cac367f23471
Revises: 3ed74e53dd66
Create Date: 2021-09-22 08:33:27.300825

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "cac367f23471"
down_revision = "3ed74e53dd66"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "organization",
        sa.Column(
            "type",
            sa.Enum(
                "DISTRIBUTOR",
                "CUSTOMER",
                name="type",
                native_enum=False,
                create_constraint=True,
            ),
            nullable=True,
        ),
    )
    op.add_column(
        "organization",
        sa.Column(
            "parent_type",
            sa.Enum(
                "DISTRIBUTOR",
                "CUSTOMER",
                name="parent_type",
                native_enum=False,
            ),
            nullable=True,
        ),
    )
    op.execute(
        """
        UPDATE organization
        SET type='CUSTOMER', parent_type='DISTRIBUTOR'
        WHERE parent_id IS NOT NULL
        """
    )
    op.execute(
        """
        UPDATE organization
        SET type='DISTRIBUTOR'
        WHERE parent_id IS NULL
        """
    )
    op.alter_column("organization", "type", nullable=False)
    op.create_unique_constraint(
        op.f("uq_organization_id"), "organization", ["id", "type"]
    )
    op.drop_constraint(
        "fk_organization_parent_id_organization", "organization", type_="foreignkey"
    )
    op.create_foreign_key(
        op.f("fk_organization_parent_id_organization"),
        "organization",
        "organization",
        ["parent_id", "parent_type"],
        ["id", "type"],
    )
    op.create_check_constraint(
        op.f("ck_organization_customer_has_parent"),
        "organization",
        "NOT (parent_id IS NULL AND type = 'CUSTOMER')",
    )
    op.create_check_constraint(
        op.f("ck_organization_parent_is_distributor"),
        "organization",
        "parent_type = 'DISTRIBUTOR'",
    ),
    op.create_check_constraint(
        op.f("ck_organization_parent_type_if_parent_id"),
        "organization",
        "NOT (parent_id IS NOT NULL AND parent_type IS NULL)",
    ),
    op.create_check_constraint(
        op.f("ck_organization_parent_id_if_parent_type"),
        "organization",
        "NOT (parent_type IS NOT NULL AND parent_id IS NULL)",
    )


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_organization_parent_id_organization"),
        "organization",
        type_="foreignkey",
    )
    op.create_foreign_key(
        "fk_organization_parent_id_organization",
        "organization",
        "organization",
        ["parent_id"],
        ["id"],
    )
    op.drop_constraint(op.f("uq_organization_id"), "organization", type_="unique")
    op.drop_constraint(
        op.f("ck_organization_customer_has_parent"),
        "organization",
        type_="check",
    )
    op.drop_constraint(
        op.f("ck_organization_parent_is_distributor"),
        "organization",
        type_="check",
    ),
    op.drop_constraint(
        op.f("ck_organization_parent_type_if_parent_id"),
        "organization",
        type_="check",
    ),
    op.drop_constraint(
        op.f("ck_organization_parent_id_if_parent_type"),
        "organization",
        type_="check",
    )
    op.drop_column("organization", "type")
    op.drop_column("organization", "parent_type")
    # ### end Alembic commands ###
