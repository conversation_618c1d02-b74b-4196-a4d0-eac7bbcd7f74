"""Add invites table

Revision ID: ba1a07b17c5b
Revises: a461d78262a4
Create Date: 2022-01-28 13:02:05.094645

"""
import sqlalchemy as sa
from alembic import op

import app.db.types

# revision identifiers, used by Alembic.
revision = "ba1a07b17c5b"
down_revision = "a461d78262a4"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "invite",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("key", sa.String(length=64), nullable=False),
        sa.Column("organization_name", sa.String(length=254), nullable=True),
        sa.Column("email", sa.String(length=254), nullable=False),
        sa.Column(
            "created_at",
            app.db.types.TZTimestamp(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        sa.Column("expiration", app.db.types.TZTimestamp(), nullable=True),
        sa.Column("accepted", sa.Boolean(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_invite")),
        sa.UniqueConstraint("key", name=op.f("uq_invite_key")),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("invite")
    # ### end Alembic commands ###
