"""Remove Invite model

Revision ID: b2018cdf3448
Revises: d30e3288ac7f
Create Date: 2022-02-07 16:50:17.488680

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "b2018cdf3448"
down_revision = "d30e3288ac7f"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("invite")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "invite",
        sa.Column(
            "id",
            sa.INTEGER(),
            sa.Identity(
                always=False,
                start=1,
                increment=1,
                minvalue=1,
                maxvalue=2147483647,
                cycle=False,
                cache=1,
            ),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("key", sa.VARCHAR(length=64), autoincrement=False, nullable=False),
        sa.Column("email", sa.VARCHAR(length=254), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column(
            "expiration", postgresql.TIMESTAMP(), autoincrement=False, nullable=True
        ),
        sa.Column("accepted", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.PrimaryKeyConstraint("id", name="pk_invite"),
        sa.UniqueConstraint("key", name="uq_invite_key"),
    )
    # ### end Alembic commands ###
