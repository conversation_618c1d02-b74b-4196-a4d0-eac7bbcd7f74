import logging
import os

import httpx
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()


KEYCLOAK_URL = f"https://{os.getenv('AUTH_HOST')}"
logger.info(f"KEYCLOAK_URL: {KE<PERSON>CLOAK_URL}")
KEYCLOAK_REALM = os.getenv("AUTH_REALM")
logger.info(f"KEYCLOAK_REALM: {KEYCLOAK_REALM}")
OIDC_CLIENT_ID = os.getenv("OIDC_CLIENT_ID")
logger.info(f"OIDC_CLIENT_ID: {OIDC_CLIENT_ID}")
OIDC_CLIENT_SECRET = os.getenv("OIDC_CLIENT_SECRET")
logger.info(f"OIDC_CLIENT_SECRET: {OIDC_CLIENT_SECRET}")
KC_SVC_USER_NAME = os.getenv("KC_SVC_USER_NAME")
logger.info(f"KC_SVC_USER_NAME: {KC_SVC_USER_NAME}")
KC_SVC_USER_PASSWORD = os.getenv("KC_SVC_USER_PASSWORD")
logger.info(f"KC_SVC_USER_PASSWORD: {KC_SVC_USER_PASSWORD}")
OIDC_TOKEN_URL = os.getenv("OIDC_TOKEN_URL")
logger.info(f"OIDC_TOKEN_URL: {OIDC_TOKEN_URL}")
OIDC_TOKEN_URL = (
    f"{KEYCLOAK_URL}/auth/realms/{KEYCLOAK_REALM}/protocol/openid-connect/token"
)
logger.info(f"OIDC_TOKEN_URL: {OIDC_TOKEN_URL}")

payload_for_events_config = {
    "eventsEnabled": True,
    "eventsListeners": ["custom-event-listener", "jboss-logging"],
    "enabledEventTypes": [
        "SEND_RESET_PASSWORD",
        "UPDATE_CONSENT_ERROR",
        "GRANT_CONSENT",
        "VERIFY_PROFILE_ERROR",
        "REMOVE_TOTP",
        "REVOKE_GRANT",
        "UPDATE_TOTP",
        "LOGIN_ERROR",
        "CLIENT_LOGIN",
        "RESET_PASSWORD_ERROR",
        "IMPERSONATE_ERROR",
        "CODE_TO_TOKEN_ERROR",
        "CUSTOM_REQUIRED_ACTION",
        "OAUTH2_DEVICE_CODE_TO_TOKEN_ERROR",
        "RESTART_AUTHENTICATION",
        "IMPERSONATE",
        "UPDATE_PROFILE_ERROR",
        "LOGIN",
        "OAUTH2_DEVICE_VERIFY_USER_CODE",
        "UPDATE_PASSWORD_ERROR",
        "CLIENT_INITIATED_ACCOUNT_LINKING",
        "USER_DISABLED_BY_PERMANENT_LOCKOUT",
        "TOKEN_EXCHANGE",
        "AUTHREQID_TO_TOKEN",
        "LOGOUT",
        "REGISTER",
        "DELETE_ACCOUNT_ERROR",
        "CLIENT_REGISTER",
        "IDENTITY_PROVIDER_LINK_ACCOUNT",
        "DELETE_ACCOUNT",
        "UPDATE_PASSWORD",
        "CLIENT_DELETE",
        "FEDERATED_IDENTITY_LINK_ERROR",
        "IDENTITY_PROVIDER_FIRST_LOGIN",
        "CLIENT_DELETE_ERROR",
        "VERIFY_EMAIL",
        "CLIENT_LOGIN_ERROR",
        "RESTART_AUTHENTICATION_ERROR",
        "EXECUTE_ACTIONS",
        "REMOVE_FEDERATED_IDENTITY_ERROR",
        "TOKEN_EXCHANGE_ERROR",
        "PERMISSION_TOKEN",
        "SEND_IDENTITY_PROVIDER_LINK_ERROR",
        "EXECUTE_ACTION_TOKEN_ERROR",
        "SEND_VERIFY_EMAIL",
        "OAUTH2_DEVICE_AUTH",
        "EXECUTE_ACTIONS_ERROR",
        "REMOVE_FEDERATED_IDENTITY",
        "OAUTH2_DEVICE_CODE_TO_TOKEN",
        "IDENTITY_PROVIDER_POST_LOGIN",
        "IDENTITY_PROVIDER_LINK_ACCOUNT_ERROR",
        "OAUTH2_DEVICE_VERIFY_USER_CODE_ERROR",
        "UPDATE_EMAIL",
        "REGISTER_ERROR",
        "REVOKE_GRANT_ERROR",
        "EXECUTE_ACTION_TOKEN",
        "LOGOUT_ERROR",
        "UPDATE_EMAIL_ERROR",
        "CLIENT_UPDATE_ERROR",
        "AUTHREQID_TO_TOKEN_ERROR",
        "UPDATE_PROFILE",
        "CLIENT_REGISTER_ERROR",
        "FEDERATED_IDENTITY_LINK",
        "SEND_IDENTITY_PROVIDER_LINK",
        "SEND_VERIFY_EMAIL_ERROR",
        "RESET_PASSWORD",
        "CLIENT_INITIATED_ACCOUNT_LINKING_ERROR",
        "OAUTH2_DEVICE_AUTH_ERROR",
        "UPDATE_CONSENT",
        "REMOVE_TOTP_ERROR",
        "VERIFY_EMAIL_ERROR",
        "SEND_RESET_PASSWORD_ERROR",
        "CLIENT_UPDATE",
        "CUSTOM_REQUIRED_ACTION_ERROR",
        "IDENTITY_PROVIDER_POST_LOGIN_ERROR",
        "UPDATE_TOTP_ERROR",
        "CODE_TO_TOKEN",
        "VERIFY_PROFILE",
        "GRANT_CONSENT_ERROR",
        "IDENTITY_PROVIDER_FIRST_LOGIN_ERROR",
    ],
    "adminEventsEnabled": True,
    "adminEventsDetailsEnabled": True,
}


class EventConfigManager:
    def __init__(self):
        self.token = self._get_service_account_token()

    @staticmethod
    def _get_service_account_token() -> str:
        """Obtain an access token using the Keycloak service account credentials."""
        data = {
            "grant_type": "password",
            "username": KC_SVC_USER_NAME,
            "password": KC_SVC_USER_PASSWORD,
            "client_id": OIDC_CLIENT_ID,
            "client_secret": OIDC_CLIENT_SECRET,
        }
        try:
            with httpx.Client() as client:
                response = client.post(OIDC_TOKEN_URL, data=data)
                response.raise_for_status()
                token = response.json().get("access_token")
                if not token:
                    raise Exception("No access_token in token response")
                return token
        except Exception as e:
            logger.error(f"Failed to obtain service account token: {e}")
            raise

    def update_event_config(self):
        """Update Keycloak event config using direct HTTP request with Bearer token."""
        url = f"{KEYCLOAK_URL}/auth/admin/realms/{KEYCLOAK_REALM}/events/config"
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
        }
        try:
            with httpx.Client() as client:
                response = client.put(
                    url, json=payload_for_events_config, headers=headers
                )
                response.raise_for_status()
                logger.info("Event config updated successfully.")
        except Exception as e:
            logger.error(f"Failed to update event config: {e}")


def main():
    migrator = EventConfigManager()
    migrator.update_event_config()


if __name__ == "__main__":
    main()
