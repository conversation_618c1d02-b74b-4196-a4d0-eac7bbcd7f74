import json
import logging
import os
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

import httpx
import redis
from dotenv import load_dotenv
from keycloak_client import KeycloakAdmin

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()

KEYCLOAK_URL = f"https://{os.getenv('AUTH_HOST')}"
KEYCLOAK_REALM = os.getenv("AUTH_REALM")
OIDC_CLIENT_ID = os.getenv("OIDC_CLIENT_ID")
OIDC_CLIENT_SECRET = os.getenv("OIDC_CLIENT_SECRET")
REDIS_HOST = os.getenv("REDIS_HOST")
REDIS_PORT = os.getenv("REDIS_PORT")
KC_SVC_USER_NAME = os.getenv("KC_SVC_USER_NAME")
logger.info(f"KC_SVC_USER_NAME: {KC_SVC_USER_NAME}")
KC_SVC_USER_PASSWORD = os.getenv("KC_SVC_USER_PASSWORD")
logger.info(f"KC_SVC_USER_PASSWORD: {KC_SVC_USER_PASSWORD}")
OIDC_TOKEN_URL = os.getenv("OIDC_TOKEN_URL")
logger.info(f"OIDC_TOKEN_URL: {OIDC_TOKEN_URL}")
OIDC_TOKEN_URL = (
    f"{KEYCLOAK_URL}/auth/realms/{KEYCLOAK_REALM}/protocol/openid-connect/token"
)
logger.info(f"OIDC_TOKEN_URL: {OIDC_TOKEN_URL}")


@dataclass
class RedisConfig:
    host = REDIS_HOST
    port = REDIS_PORT
    decode_responses: bool = True


@dataclass
class UserData:
    id: str
    timestamp: int
    username: str
    enabled: bool
    emailVerified: bool
    firstName: Optional[str]
    lastName: Optional[str]
    email: str
    groups: Optional[str]
    roles: List[str]
    attributes: Dict[str, Any]
    lastLogin: int | None


class KeycloakToRedisManager:
    def __init__(self, redis_config: RedisConfig):
        self.redis = redis.Redis(
            host=redis_config.host,
            port=redis_config.port,
            decode_responses=redis_config.decode_responses,
        )
        self.keycloak = self._create_keycloak_admin()
        self.token = self._get_service_account_token()

    @staticmethod
    def _create_keycloak_admin() -> KeycloakAdmin:
        """Create and return a KeycloakAdmin client."""
        if (
            not KEYCLOAK_REALM
            or not KEYCLOAK_URL
            or not OIDC_CLIENT_ID
            or not OIDC_CLIENT_SECRET
            or not REDIS_HOST
            or not REDIS_PORT
        ):
            raise ValueError("Missing environment variables")
        return KeycloakAdmin(
            client=httpx.Client(),
            keycloak_url=KEYCLOAK_URL,
            realm=KEYCLOAK_REALM,
            client_id=OIDC_CLIENT_ID,
            client_secret=OIDC_CLIENT_SECRET,
        )

    @staticmethod
    def _get_service_account_token() -> str:
        """Obtain an access token using the Keycloak service account credentials."""
        data = {
            "grant_type": "password",
            "username": KC_SVC_USER_NAME,
            "password": KC_SVC_USER_PASSWORD,
            "client_id": OIDC_CLIENT_ID,
            "client_secret": OIDC_CLIENT_SECRET,
        }
        try:
            with httpx.Client() as client:
                response = client.post(OIDC_TOKEN_URL, data=data)
                response.raise_for_status()
                token = response.json().get("access_token")
                if not token:
                    raise Exception("No access_token in token response")
                return token
        except Exception as e:
            logger.error(f"Failed to obtain service account token: {e}")
            raise

    def _get_last_login(self, user_id: str):
        """Update Keycloak event config using direct HTTP request with Bearer token."""
        url = (
            f"{KEYCLOAK_URL}/auth/admin/realms/{KEYCLOAK_REALM}/events?"
            f"user={user_id}&type=CODE_TO_TOKEN&max=1"
        )

        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
        }
        try:
            with httpx.Client() as client:
                response = client.get(url, headers=headers)
                if isinstance(response.json(), list):
                    return response.json()[0]["time"]
        except Exception as e:
            logger.error(f"Failed to get lastlogin: {e}")
            return None

    def _get_user_group(self, user_id: str) -> Optional[str]:
        """Extract group ID from user's group information."""
        user_group = self.keycloak.users.get_groups(user_id)
        if not user_group:
            logger.info(f"User {user_id} has no group")
            return None
        logger.info("Get groups")

        return user_group[0]["id"] if user_group else None

    def _get_user_roles(self, user_id: str) -> List[str]:
        """Get user roles from Keycloak."""
        user_roles = self.keycloak.users.get_user_roles(user_id)
        role_ids = []
        for role in user_roles:
            role_ids.append(role["id"])
        logger.info("Get roles")
        return role_ids

    def _process_user(self, user_data: Dict[str, Any]) -> UserData:
        """Process raw user data into structured UserData object."""
        logger.info("Processing users")
        user_id = user_data["id"]
        if not user_data.get("email", None):
            logger.warning(f"Userdata doesnt have email key {user_data}")

        return UserData(
            id=user_id,
            timestamp=user_data["createdTimestamp"],
            username=user_data["username"],
            enabled=user_data["enabled"],
            emailVerified=user_data["emailVerified"],
            firstName=user_data.get("firstName"),
            lastName=user_data.get("lastName"),
            email=user_data["username"],
            attributes=user_data["attributes"] if "attributes" in user_data else {},
            lastLogin=self._get_last_login(user_id),
            groups=self._get_user_group(user_id),
            roles=self._get_user_roles(user_id),
        )

    def _save_to_redis(self, user: UserData) -> None:
        """Save user data to Redis."""
        # Save user details
        logger.info("Saving data to redis")
        user_dict = {
            k: str(v)
            if isinstance(v, bool)
            else json.dumps(v)
            if isinstance(v, dict)
            else v
            for k, v in user.__dict__.items()
            if v is not None
            and k
            in [
                "id",
                "timestamp",
                "username",
                "enabled",
                "firstName",
                "lastName",
                "email",
                "attributes",
                "lastLogin",
                "groups",
            ]
        }
        try:
            self.redis.hset(name=f"users:{user.id}", mapping=user_dict)

            # Save group membership
            if user.groups:
                self.redis.sadd(f"groups:{user.groups}", user.id)

            if user.roles:
                self.redis.hset(f"users:{user.id}", "roles", json.dumps(user.roles))

            # Save roles
            for role in user.roles:
                self.redis.sadd(f"roles:{role}", user.id)
        except Exception as e:
            logger.error(f"Failed to save data in redis: {e}")

    def migrate(self) -> None:
        """Execute the migration from Keycloak to Redis."""
        try:
            start = 0
            end = 100
            while True:
                logger.info(f"start: {start}")
                users = self.keycloak.users.get_many(first=start, max_=end)
                if not users:
                    break
                logger.info(f"Found {len(users)} users to migrate")

                for user_data in users:
                    try:
                        user = self._process_user(user_data)
                        self._save_to_redis(user)
                        logger.info(f"Migrated user: {user.username}")
                    except Exception as e:
                        logger.error(f"Failed to migrate user {user_data}: {e}")

                start += end
            logger.info("Migration completed successfully")
        except Exception as e:
            logger.error(f"Migration failed: {e}")
        finally:
            self.keycloak.client.close()


def main():
    redis_config = RedisConfig()
    migrator = KeycloakToRedisManager(redis_config)
    migrator.migrate()


if __name__ == "__main__":
    main()
