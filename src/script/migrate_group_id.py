import logging
import os
from typing import Union, cast

import httpx
from dotenv import load_dotenv
from keycloak_client import KeycloakAdmin
from sqlalchemy import create_engine, select, update
from sqlalchemy.engine.url import URL
from sqlalchemy.orm import sessionmaker

from app.enums import OrganizationTypeEnum
from app.models import Organization

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Load environment variables
load_dotenv()
DB_URI = (
    f"{os.getenv('DB_SCHEME')}://{os.getenv('DB_USER')}:"
    f"{os.getenv('DB_PASSWORD')}@{os.getenv('DB_HOST')}:"
    f"{os.getenv('DB_PORT')}/{os.getenv('DB_NAME')}"
)
KEYCLOAK_URL = f"https://{os.getenv('AUTH_HOST')}"
KEYCLOAK_REALM = os.getenv("AUTH_REALM")
OIDC_CLIENT_ID = os.getenv("OIDC_CLIENT_ID")
OIDC_CLIENT_SECRET = os.getenv("OIDC_CLIENT_SECRET")

engine = create_engine(cast(Union[str, URL], DB_URI))

SessionLocal = sessionmaker(bind=engine)


class KeycloakToPostgresManager:
    """
    Class for migrating group IDs from Keycloak to PostgreSQL.
    """

    def __init__(self):
        self.keycloak = self._create_keycloak_admin()

    @staticmethod
    def _create_keycloak_admin() -> KeycloakAdmin:
        """Create and return a KeycloakAdmin client."""
        if (
            not KEYCLOAK_REALM
            or not KEYCLOAK_URL
            or not OIDC_CLIENT_ID
            or not OIDC_CLIENT_SECRET
        ):
            raise ValueError("Missing environment variables")
        return KeycloakAdmin(
            client=httpx.Client(),
            keycloak_url=KEYCLOAK_URL,
            realm=KEYCLOAK_REALM,
            client_id=OIDC_CLIENT_ID,
            client_secret=OIDC_CLIENT_SECRET,
        )

    def migrate(self) -> None:
        try:
            with SessionLocal() as session:
                db_orgs = session.execute(select(Organization)).scalars().all()

            for org in db_orgs:
                group = self.keycloak.groups.get_many(search=org.name)
                if not group:
                    logging.error("Group not found in KC: ", org.name)
                    continue
                if (
                    "subGroups" in group[0]
                    and group[0]["subGroups"]
                    and org.type == OrganizationTypeEnum.CLIENT
                ):
                    group_id = group[0]["subGroups"][0]["id"]
                else:
                    group_id = group[0]["id"]
                with SessionLocal() as session:
                    query = (
                        update(Organization)
                        .values(external_id=group_id)
                        .where(Organization.name == org.name)
                    )
                    session.execute(query)
                    session.commit()
                    logging.info(f"Group migrated: {org.name}")
        except Exception as e:
            logger.error(f"Migration failed: {e}")
        finally:
            self.keycloak.client.close()


def main():
    migrator = KeycloakToPostgresManager()
    migrator.migrate()


if __name__ == "__main__":
    main()
