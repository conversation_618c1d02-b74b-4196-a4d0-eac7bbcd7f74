actor UserInfo {}
actor ServiceInfo {}
resource Organization {
    relations = { ancestor: Organization };
    roles = [
        "DistributorAdmin",
        "DistributorUser",
        "ClientAdmin",
        "ClientUser",
        "PartnerUser",
    ];
    permissions = [
        "read",
        "create-child",
        "update",
        "delete",
    ];

    "read" if "PartnerUser";
    "read" if "ClientUser";
    "read" if "DistributorUser";

    "create-child" if "DistributorUser";

    "update" if "DistributorAdmin";
    "update" if "ClientAdmin";

    "read" if "update";
    "create-child" if "update";

    "update" if "DistributorAdmin" on "ancestor";
    "update" if "DistributorUser" on "ancestor";
    "delete" if "DistributorAdmin" on "ancestor";
}

allow(actor, action, resource: Organization) if
    has_permission(actor, action.replace("org:", ""), resource);


has_permission(actor: ServiceInfo, "read", _: Organization) if
    ORG_MANAGEMENT_CLIENT_ID = actor.client_id or
    (ORG_MANAGEMENT_UI_CLIENT_ID = actor.client_id and IS_DEV);

has_permission(actor: ServiceInfo, "update", _: Organization) if
    ORG_MANAGEMENT_CLIENT_ID = actor.client_id or
    (ORG_MANAGEMENT_UI_CLIENT_ID = actor.client_id and IS_DEV);

has_relation(parent: Organization, "ancestor", child: Organization) if
    child.parent = parent or
    has_relation(parent, "ancestor", child.parent);

has_role(actor: UserInfo, role_name: String, _: Organization{id: org_id}) if
    actor.organization.id = org_id and
    role = role_name and
    role in actor.realm_access.roles;

allow_request(actor: UserInfo, request: Request) if
    request.url.path.startswith("/v1/users") and
    "DistributorAdmin" in actor.realm_access.roles;

allow_request(actor: ServiceInfo, request: Request) if
    request.url.path.startswith("/v1/users") and
    (ORG_MANAGEMENT_CLIENT_ID = actor.client_id or (IS_DEV and ORG_MANAGEMENT_UI_CLIENT_ID = actor.client_id));

allow_request(actor: UserInfo, request: Request) if
    request.url.path.startswith("/v1/partners") and
    "DistributorAdmin" in actor.realm_access.roles;

allow_request(actor: ServiceInfo, request: Request) if
    request.url.path.startswith("/v1/partners") and
    (ORG_MANAGEMENT_CLIENT_ID = actor.client_id or (IS_DEV and ORG_MANAGEMENT_UI_CLIENT_ID = actor.client_id));

allow_request(actor: UserInfo, request: Request) if
    request.url.path.startswith("/v1/identity-providers") and (
        "DistributorAdmin" in actor.realm_access.roles or
        "DistributorUser" in actor.realm_access.roles or
        "ClientAdmin" in actor.realm_access.roles
    );

allow_request(actor: ServiceInfo, request: Request) if
    request.url.path.startswith("/v1/identity-providers") and
    (ORG_MANAGEMENT_CLIENT_ID = actor.client_id or (IS_DEV and ORG_MANAGEMENT_UI_CLIENT_ID = actor.client_id));

allow_request(actor: UserInfo, request: Request) if
    request.url.path.startswith("/v2/users") and (
    "DistributorAdmin" in actor.realm_access.roles or
    "DistributorUser" in actor.realm_access.roles
    );

allow_request(actor: ServiceInfo, request: Request) if
    request.url.path.startswith("/v2/users") and
    (ORG_MANAGEMENT_CLIENT_ID = actor.client_id or (IS_DEV and ORG_MANAGEMENT_UI_CLIENT_ID = actor.client_id));
