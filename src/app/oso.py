import os

from oso import Oso  # type: ignore[attr-defined]
from sqlalchemy_oso import register_models
from starlette.requests import Request

from app import schemas
from app.constants import APP_DIR
from app.core.config import settings
from app.db.base import Base

if os.environ.get("DEBUG") == "1":
    os.environ.setdefault("POLAR_LOG", "1")

oso = Oso()
register_models(oso, Base)
oso.register_class(schemas.UserInfo)
oso.register_class(schemas.ServiceInfo)
oso.register_constant(settings.OIDC_CLIENT_ID, "ORG_MANAGEMENT_CLIENT_ID")
oso.register_constant(settings.OIDC_UI_CLIENT_ID, "ORG_MANAGEMENT_UI_CLIENT_ID")
oso.register_class(Request)

if os.getenv("DEV_MODE") == "1":
    oso.register_constant(True, "IS_DEV")
else:
    oso.register_constant(False, "IS_DEV")

oso.load_files([(APP_DIR / "policy.polar")])
