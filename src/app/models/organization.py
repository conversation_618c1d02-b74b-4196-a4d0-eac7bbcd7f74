import uuid as _uuid
from typing import Any, List

from sqlalchemy import (
    Check<PERSON>onstraint,
    Column,
    Enum,
    ForeignKey,
    ForeignKeyConstraint,
    Identity,
    Integer,
    String,
    Text,
    UniqueConstraint,
    and_,
    text,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, declared_attr, relationship
from sqlalchemy.sql import functions

from app.db.base_class import Base
from app.db.types import GUID, TZTimestamp
from app.enums import OrganizationTypeEnum


class Organization(Base):
    id: int = Column(
        Integer, Identity(), primary_key=True, autoincrement=True, nullable=False
    )
    type: OrganizationTypeEnum = Column(
        Enum(
            OrganizationTypeEnum,
            native_enum=False,
            create_constraint=True,
            name="type",
        ),
        nullable=False,
    )
    name = Column(String(length=256), unique=True, nullable=False)

    parent_id = Column(Integer)
    parent_type = Column(
        Enum(
            OrganizationTypeEnum,
            native_enum=False,
            name="parent_type",
        ),
    )

    external_id = Column(GUID, index=True, nullable=True)
    group_id = Column(GUID, index=True, nullable=True)
    created_at = Column(
        TZTimestamp(timezone=False),
        server_default=functions.current_timestamp(),
        nullable=False,
    )

    # Enforces type constraint during object creation
    parent: "Distributor" = relationship(
        "Distributor",
        foreign_keys="[Organization.parent_id, Organization.parent_type]",
        remote_side="[Distributor.id, Distributor.type]",
        back_populates="children",
        uselist=False,
    )
    # Used by Oso and for query building
    parent_view: "Distributor" = relationship(
        "Organization",
        foreign_keys=[parent_id],
        remote_side=[id],
        viewonly=True,
        uselist=False,
    )
    children: list["Organization"] = relationship(
        "Organization",
        back_populates="parent",
    )
    domain: Mapped[List["Domain"]] = relationship(
        "Domain", back_populates="organization", cascade="all, delete-orphan"
    )
    pmn_code: Mapped[List["PMNCode"]] = relationship(
        "PMNCode", back_populates="organization", cascade="all, delete-orphan"
    )
    user_files: Mapped[List["UserFile"]] = relationship(
        "UserFile", back_populates="organization", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint(id, type),
        ForeignKeyConstraint(
            [parent_id, parent_type],
            [id, type],
        ),
        CheckConstraint(
            ~and_(parent_type != OrganizationTypeEnum.DISTRIBUTOR),
            name="parent_is_distributor",
        ),
        CheckConstraint(
            ~and_(parent_id.is_(None), type != OrganizationTypeEnum.DISTRIBUTOR),
            name="non_distributor_has_parent",
        ),
        CheckConstraint(
            ~and_(parent_id.is_not(None), parent_type.is_(None)),
            name="parent_type_if_parent_id",
        ),
        CheckConstraint(
            ~and_(parent_type.is_not(None), parent_id.is_(None)),
            name="parent_id_if_parent_type",
        ),
    )
    __mapper_args__ = {"polymorphic_on": "type"}

    @property
    def repr_fields(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type,
            "parent_id": self.parent_id,
        }

    def __repr__(self) -> str:
        fields = ",".join(f"{key}={value!r}" for key, value in self.repr_fields.items())
        return f"<{self.__class__.__name__}({fields})>"

    def get_path(self) -> str:
        prefix = ""
        if self.parent:
            prefix = self.parent.get_path()
        return f"{prefix}/{self.name}"

    @property
    def path(self) -> str:
        return self.get_path()


class Distributor(Organization):
    __tablename__ = None  # type: ignore
    __mapper_args__ = {"polymorphic_identity": OrganizationTypeEnum.DISTRIBUTOR}
    sub_distributors: list["Distributor"] = relationship(
        "Distributor",
        viewonly=True,
    )
    clients: list["Client"] = relationship(
        "Client",
        viewonly=True,
    )

    @property
    def repr_fields(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "parent_id": self.parent_id,
        }


class Client(Organization):
    __tablename__ = None  # type: ignore
    __mapper_args__ = {"polymorphic_identity": OrganizationTypeEnum.CLIENT}

    @property
    def repr_fields(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "parent_id": self.parent_id,
        }


class Partner(Organization):
    __tablename__ = None  # type: ignore
    __mapper_args__ = {"polymorphic_identity": OrganizationTypeEnum.PARTNER}

    @property
    def repr_fields(self) -> dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "parent_id": self.parent_id,
        }


class PMNCode(Base):
    @declared_attr
    def __tablename__(cls) -> Mapped[str]:
        return "pmn_code"

    id = Column(Integer, Identity(), primary_key=True, autoincrement=True)
    pmn_code = Column(String(length=5), nullable=False, unique=True)
    org_id = Column(Integer, ForeignKey("organization.id", ondelete="CASCADE"))
    organization: Mapped["Organization"] = relationship(
        "Organization", back_populates="pmn_code"
    )


class Domain(Base):
    id = Column(Integer, Identity(), primary_key=True, autoincrement=True)
    domain_name = Column(String(length=256), nullable=False)
    org_id = Column(Integer, ForeignKey("organization.id", ondelete="CASCADE"))
    organization: Mapped["Organization"] = relationship(
        "Organization", back_populates="domain"
    )


class UserFile(Base):
    @declared_attr
    def __tablename__(cls) -> Mapped[str]:
        return "user_file"

    id: int = Column(Integer, Identity(), primary_key=True)
    uuid: _uuid.UUID = Column(
        UUID(as_uuid=True),
        default=_uuid.uuid4,
        server_default=text("gen_random_uuid()"),
        nullable=False,
        unique=True,
        index=True,
    )
    file_name = Column(String(50), nullable=False)
    path = Column(String(1024), nullable=False)
    organization_id = Column(
        Integer,
        ForeignKey("organization.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    organization: Mapped["Organization"] = relationship(
        "Organization", back_populates="user_files"
    )
    file_reports: Mapped["FileReport"] = relationship(
        "FileReport", back_populates="user_file", cascade="all, delete-orphan"
    )
    user_reports: Mapped["UserReport"] = relationship(
        "UserReport", back_populates="user_file", cascade="all, delete-orphan"
    )


class FileReport(Base):
    @declared_attr
    def __tablename__(cls) -> Mapped[str]:
        return "file_report"

    id = Column(Integer, Identity(), primary_key=True)
    user_file_id = Column(
        Integer,
        ForeignKey("user_file.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    status = Column(String(20), nullable=False, server_default="InProgress")
    total_rows = Column(Integer, nullable=True)
    valid_rows = Column(Integer, nullable=True)

    # Relationships
    user_file: Mapped["UserFile"] = relationship(
        "UserFile", back_populates="file_reports"
    )


class UserReport(Base):
    @declared_attr
    def __tablename__(cls) -> Mapped[str]:
        return "user_report"

    id = Column(Integer, Identity(), primary_key=True)
    user_file_id = Column(
        Integer,
        ForeignKey("user_file.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    email = Column(String(254), nullable=False, index=True)
    reason = Column(Text, nullable=True)

    # Relationships
    user_file: Mapped["UserFile"] = relationship(
        "UserFile", back_populates="user_reports"
    )
