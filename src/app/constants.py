import pathlib

APP_DIR = pathlib.Path(__file__).parent
APPLICATION_NAME = "Organizations API"
OTEL_SERVICE_NAME = "organizations-api"

ORGANIZATION = "organization"
USER = "user"

PUT = "PUT"
DELETE = "DELETE"
POST = "POST"
PATCH = "PATCH"

CSV_HEADERS = [
    "First Name",
    "Last Name",
    "Email",
    "Organization",
    "User Type",
    "Last Login(UTC)",
    "Created Date(UTC)",
    "Created By",
    "Identity Provider",
    "Status",
]

NEVER_LOGGED_IN = "Never logged in"

EXPECTED_HEADERS_IN_CSV = ["first name", "last name", "email"]

OFFLINE_ACCESS = "offline_access"
DEFAULT_ROLES = "default-roles"
MAX_NAME_LENGTH = 50
MIN_NAME_LENGTH = 1
MAX_LENGTH_FOR_DB = 10
