import logging

import uvicorn
from fastapi import Fast<PERSON><PERSON>, Response
from fastapi.exception_handlers import http_exception_handler
from keycloak_client import KeycloakException
from prometheus_client import CONTENT_TYPE_LATEST, generate_latest
from prometheus_fastapi_instrumentator import PrometheusFastApiInstrumentator
from starlette.requests import Request
from starlette.responses import JSONResponse

from app import constants
from app.api.api_v1.api import api_router
from app.api.api_v2.api import api_v2_router
from app.core.config import settings
from app.core.security import http_exc_from_keycloak

app = FastAPI(
    title=constants.APPLICATION_NAME,
    openapi_url="/v1/organizations/openapi.json",
    # https://github.com/tiangolo/fastapi/issues/12
    swagger_ui_init_oauth={
        "usePkceWithAuthorizationCodeGrant": True,
        "clientId": settings.OIDC_UI_CLIENT_ID,
        "scopes": ["openid", "profile", "email"],
        # "additionalQueryStringParams": {
        #     'kc_idp_hint': 'oidc',
        # }
    },
    root_path=settings.ROOT_PATH,
    version="0.5.0",
)


@app.exception_handler(KeycloakException)
async def handle_keycloak_exc(request: Request, exc: KeycloakException) -> JSONResponse:
    return await http_exception_handler(request, http_exc_from_keycloak(exc))


@app.middleware("http")
async def log_requests(request: Request, call_next):
    logging.info(f"Request hit: {request.url}")
    logging.info(f"Middleware OIDC_AUTHORIZATION_URL:{settings.OIDC_AUTHORIZATION_URL}")
    logging.info(f"Middleware OIDC_TOKEN_URL:{settings.OIDC_TOKEN_URL}")
    auth_header = request.headers.get("authorization")
    if auth_header and auth_header.lower().startswith("bearer "):
        token = auth_header[7:]  # Remove 'Bearer ' prefix
        logging.info(f"Bearer Token in Middleware: {token}")
    else:
        logging.info("No Bearer token found")
    return await call_next(request)


app.include_router(api_router, prefix="/v1")
app.include_router(api_v2_router, prefix="/v2")


@app.get("/metrics")
def metrics():
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


PrometheusFastApiInstrumentator(should_group_status_codes=False).instrument(app)

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.APP_HOST,
        port=settings.APP_PORT,
        reload=True,
        log_config=None,  # we have our own logging initialization
    )  # pragma: no cover
