from typing import Any

from pydantic import UUID4, BaseModel, EmailStr, Field, SecretStr, validator

from app.typedefs import Username


class KeycloakIdentityProvider(BaseModel):
    alias: str
    displayName: str | None = None


class KeycloakUser(BaseModel):
    id: UUID4
    enabled: bool
    emailVerified: bool
    createdTimestamp: int
    email: str | None
    username: Username
    firstName: str | None
    lastName: str | None
    roles: list[str] = []
    identityProvider: KeycloakIdentityProvider | None = None


class KeycloakUserCreate(BaseModel):
    email: EmailStr
    username: None | Username = None
    password: SecretStr | None = SecretStr("")
    enabled: bool | None = True
    # review this
    emailVerified: bool = False
    firstName: str | None = None
    lastName: str | None = None
    attributes: dict[str, list[str]] | None = Field(default_factory=dict)

    @validator("username", pre=True, always=True)
    def assemble_username_from_email(cls, v: str | None, values: dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return values.get("email")


class UserWithCredentials(KeycloakUserCreate):
    id: UUID4
    username: Username


class KeyCloakUserDetails(KeycloakUser):
    lastLoginTimestamp: int | None
    organization: dict | None = None
    createdBy: str | None
