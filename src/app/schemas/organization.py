import datetime
import re
from typing import Optional
from uuid import UUID

from pydantic import (
    AnyHttpUrl,
    BaseModel,
    ConstrainedStr,
    Field,
    NonNegativeInt,
    validator,
)

from app.enums import FileReportStatusEnum, OrganizationRoleEnum, OrganizationTypeEnum


class OrganizationNameStr(ConstrainedStr):
    min_length = 3
    max_length = 256
    strip_whitespace = True


class OrganizationBase(BaseModel):
    name: OrganizationNameStr


class PMNDomainDetail(BaseModel):
    allowed_domains: list[str] = []
    pmn_codes: list[str] = []

    @validator("allowed_domains", pre=True)
    def validate_allowed_domains(cls, v):
        lowercase_domains = []
        if not v:
            return v
        domain_pattern = r"^[a-zA-Z0-9](?:[a-zA-Z0-9-.]*[a-zA-Z0-9])?\.[a-zA-Z]{2,}$"
        if not all(re.match(domain_pattern, domain) for domain in v):
            raise ValueError("Invalid pattern for domain")
        for domain in v:
            lowercase_domains.append(domain.lower())
        if not (len(set(lowercase_domains)) == len(lowercase_domains)):
            raise ValueError("Duplicate domains not allowed")
        return lowercase_domains

    @validator("pmn_codes", pre=True)
    def validate_pmn_codes(cls, v):
        if not v:
            return v
        pmn_code_pattern = r"^[A-Z0-9]{5}$"
        if not (len(set(v)) == len(v)):
            raise ValueError("Duplicate PMNs not allowed")
        if not all(re.match(pmn_code_pattern, pmn_code) for pmn_code in v):
            raise ValueError("PMN Codes with only 5 uppercase alphabets allowed")
        if not (len(set(v)) == len(v)):
            raise ValueError("Duplicate PMNs not allowed")
        return v


class OrganizationCreate(OrganizationBase, PMNDomainDetail):
    ...


class OrganizationUpdate(OrganizationBase, PMNDomainDetail):
    ...


class OrganizationInDbBase(OrganizationBase):
    id: int

    class Config:
        orm_mode = True


class OrganizationDetail(OrganizationInDbBase, PMNDomainDetail):
    ...


class OrganizationSimple(OrganizationInDbBase):
    # TODO: remove this fields after Distributor/OrgManagement functionality
    # TODO: check existing clients for their usage
    parent_id: NonNegativeInt | None
    type: OrganizationTypeEnum


class OrganizationFull(OrganizationInDbBase, PMNDomainDetail):
    type: OrganizationTypeEnum
    parent: OrganizationSimple | None
    created_at: datetime.datetime
    created_by: str | None = None


class OrganizationMembership(BaseModel):
    role: OrganizationRoleEnum


class OrganizationRole(BaseModel):
    display_name: str
    name: str


class DescendantOrganization(OrganizationFull):
    number_of_users: NonNegativeInt
    impersonation_url: AnyHttpUrl | None
    created_by: str | None


class ResponseSchema(BaseModel):
    last_page: int
    total_count: int
    results: list[DescendantOrganization]
    page: NonNegativeInt | None
    page_size: NonNegativeInt | None


class BulkUserCreateRequest(BaseModel):
    roles: list[str] = Field(..., min_items=1)
    fileName: str
    identityProvider: Optional[str] = Field(...)

    @validator("identityProvider")
    def validate_empty(cls, v):
        if isinstance(v, str) and not v:
            raise ValueError("Identity provider cannot be empty string.")
        return v

    @validator("roles")
    def validate_roles(cls, v):
        if not v:
            raise ValueError("At least one role must be provided")
        if len(set(v)) != len(v):
            raise ValueError("Duplicate roles not allowed")
        valid_roles = [
            OrganizationRoleEnum.CLIENT_ADMIN.value,
            OrganizationRoleEnum.CLIENT_USER.value,
        ]
        invalid_roles = set(v) - set(valid_roles)
        if invalid_roles:
            raise ValueError(f"Invalid roles: {invalid_roles}")
        return v


class BulkUserCreateResponse(BaseModel):
    userFileId: UUID


class CSVUploadResponse(BaseModel):
    key: str
    fileName: str
    url: str


class BulkUserNotificationStatus(BaseModel):
    fileName: str
    id: UUID
    createdBy: str | None = None
    createdAt: datetime.datetime | None = None
    status: FileReportStatusEnum


class InvalidUserRow(BaseModel):
    email: str
    firstName: str
    lastName: str
    reason: str


class BulkUserNotificationDetail(BaseModel):
    totalRows: int
    validRows: int
    invalidRows: list[InvalidUserRow]
