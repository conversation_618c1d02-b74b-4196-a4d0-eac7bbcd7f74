from pydantic import BaseModel, ConstrainedStr, EmailStr


class NameStr(ConstrainedStr):
    min_length = 1
    max_length = 64
    strip_whitespace = True


class Subject(ConstrainedStr):
    min_length = 1
    strip_whitespace = True


class Address(BaseModel):
    name: NameStr | None
    email: EmailStr


class EmailMessage(BaseModel):
    subject: Subject | None
    name_from: Subject | None
    recipients: list[Address]
    reply_to: Address | None = None
    html: str
