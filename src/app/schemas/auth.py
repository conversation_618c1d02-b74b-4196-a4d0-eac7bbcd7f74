from typing import Literal
from uuid import UUID

from pydantic import AnyHttpUrl, BaseModel, EmailStr, Field

from app.enums import OrganizationTypeEnum
from app.typedefs import Username


class RealmAccess(BaseModel):
    roles: list[str]


class UserOrganization(BaseModel):
    id: int
    type: OrganizationTypeEnum


class UserInfo(BaseModel):
    id: UUID = Field(alias="sub")
    email: EmailStr
    username: Username
    email_verified: bool
    realm_access: RealmAccess
    organization: UserOrganization
    typ: Literal["Bearer"]
    given_name: str | None = None
    family_name: str | None = None
    name: str | None = None


class ServiceInfo(BaseModel):
    client_id: str = Field(alias="client_id")
    allowed_origins: tuple[AnyHttpUrl, ...] = Field(
        default_factory=tuple, alias="allowed-origins"
    )


class ServiceAccountConfig(BaseModel):
    scopes: list[str] = []
    roles: list[str] = []
    attributes: dict[str, list[str]] = {}


class ServiceAccountCreate(BaseModel):
    name: str = Field(min_length=1, max_length=255)
    permissions: list[str] = Field(min_items=1)


class ServiceAccountCreated(BaseModel):
    client_id: str
    client_secret: str
    name: str
    permissions: list[str]


class ResetPasswordOptions(BaseModel):
    temporary: bool = True


class PreAuthContext(BaseModel):
    email: EmailStr | None = None
    oauth_query: dict[str, str] = Field(
        default_factory=dict,
        description="Query parameters of initiated OAuth2 code grant flow",
    )


class PreAuthResult(BaseModel):
    login_url: AnyHttpUrl


class PasswordPolicy(BaseModel):
    id: str = Field(..., example="length")
    value: int | str | None = Field(..., example=8)
    display_name: str = Field(..., example="Minimum Length")
    description: str | None = Field(..., example="minimum length 8")


class APIPasswordOptions(BaseModel):
    temporary: bool = False
