from typing import Any, <PERSON>, Union
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, SecretStr, validator

from app.constants import MAX_NAME_LENGTH, MIN_NAME_LENGTH
from app.enums import OrganizationRoleEnum
from app.schemas import OrganizationSimple
from app.schemas.organization import OrganizationNameStr
from app.typedefs import Username


class UserBase(BaseModel):
    email: EmailStr
    firstName: str | None = None
    lastName: str | None = None


class UserCreate(UserBase):
    role: OrganizationRoleEnum
    identityProvider: str | None = None
    enabled: bool = True
    emailVerified: bool = True
    username: Username = None  # type: ignore[assignment]

    @validator("username", pre=True, always=True)
    def assemble_username_from_email(cls, v: Any, values: dict[str, Any]) -> Any:
        if v is None and "email" in values:
            return values["email"]
        return v

    @validator("username")
    def validate_username(cls, v):
        if not v or not str(v).strip():
            raise ValueError("username must not be empty")
        return v


class UserBaseV2(BaseModel):
    email: EmailStr
    firstName: str
    lastName: str


class UserCreateV2(UserBaseV2):
    roles: List[Union[OrganizationRoleEnum, str]] = Field(min_items=1)
    identityProvider: str | None = None
    enabled: bool = True
    emailVerified: bool = True
    username: Username = None  # type: ignore[assignment]

    @validator("username", pre=True, always=True)
    def assemble_username_from_email(cls, v: Any, values: dict[str, Any]) -> Any:
        if v is None and "email" in values:
            return values["email"]
        return v

    @validator("roles", check_fields=False)
    def validate_role(cls, v):
        enum_count = sum(1 for item in v if isinstance(item, OrganizationRoleEnum))
        if enum_count != 1:
            raise ValueError("Exactly one DefaultRole must be provided in 'roles'")
        return v

    @validator("firstName", "lastName", check_fields=False)
    def validate_name(cls, v, field):
        if not v:
            raise ValueError(f"{field.name} required!")
        if not (MIN_NAME_LENGTH <= len(v.strip()) <= MAX_NAME_LENGTH):
            raise ValueError(
                f"{field.name} must contain at least {MIN_NAME_LENGTH} character"
                f" and at most {MAX_NAME_LENGTH} characters"
            )
        return v.strip()


class UserUpdate(BaseModel):
    identityProvider: str | None
    username: Username | None


class User(UserBase):
    id: UUID
    username: Username
    organization: OrganizationSimple


class UserV2Base(BaseModel):
    email: EmailStr
    first_name: str | None = None
    last_name: str | None = None
    mobile_phone: str | None = None


class UserV2(UserV2Base):
    id: UUID
    username: Username
    organization: OrganizationSimple


class CreatePartnerUser(UserV2Base):
    organization_name: OrganizationNameStr


class EnablePartnerUser(BaseModel):
    password: SecretStr


class UserUpdatev2(BaseModel):
    identityProvider: str | None
    firstName: str
    lastName: str
    enabled: bool
    roles: List[Union[OrganizationRoleEnum, str]] = Field(min_items=1)

    @validator("roles")
    def validate_role(cls, v):
        enum_count = sum(1 for item in v if isinstance(item, OrganizationRoleEnum))
        if enum_count != 1 or enum_count > 1:
            raise ValueError("Exactly one DefaultRole must be provided in 'roles'")
        return v

    @validator("firstName", "lastName")
    def validate_name(cls, v, field):
        if not v:
            raise ValueError(f"{field.name} required!")
        if not (MIN_NAME_LENGTH <= len(v.strip()) <= MAX_NAME_LENGTH):
            raise ValueError(
                f"{field.name} must contain at least {MIN_NAME_LENGTH} character"
                f" and at most {MAX_NAME_LENGTH} characters"
            )
        return v.strip()
