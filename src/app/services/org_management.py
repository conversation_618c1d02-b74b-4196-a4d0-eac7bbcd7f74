import logging
from collections.abc import Iterable
from enum import Enum
from typing import Any, Dict, Literal, Optional, TypeVar, cast, overload
from urllib.parse import urlencode, urlsplit, urlunsplit
from uuid import UUID

import sqlalchemy as sa
from fastapi import BackgroundTasks
from fastapi.encoders import jsonable_encoder
from pydantic import UUID4, AnyHttpUrl, EmailStr, SecretStr, parse_obj_as
from sqlalchemy import func, select
from sqlalchemy.engine import Row
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from sqlalchemy.sql import Select, distinct
from sqlalchemy.sql.functions import array_agg

from app import schemas
from app.adapters.core_api.base import AbstractCoreFacade
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import BadPassword, GroupNotFound
from app.adapters.iam.keycloak import KeycloakUser, to_keycloak
from app.adapters.iam.schemas import Group, GroupCreate, User, UserCreate
from app.adapters.platform_api_client.audit_service import AbstractAuditAPI
from app.adapters.platform_api_client.schemas import AuditLogModel
from app.common.exceptions import NotFound
from app.common.pagination import Pagination
from app.core.config import settings
from app.core.utils import get_object, get_object_or_404
from app.db.redis_service import RedisService
from app.enums import FileReportStatusEnum, OrganizationRoleEnum, OrganizationTypeEnum
from app.models import Domain, FileReport, Organization, PMNCode
from app.models.organization import Client, Distributor, Partner, UserFile, UserReport
from app.schemas.organization import OrganizationFull, OrganizationNameStr
from app.schemas.user import UserCreateV2
from app.services import mappers, users
from app.services.mappers import alias_to_identity_provider
from app.services.validation import validate_csv_content
from app.typedefs import UserDict


class OrganizationExistsInDb(Exception):
    ...


class OperationNotAllowed(Exception):
    ...


class UserAlreadyExists(Exception):
    ...


class PMNCodeAlreadyExists(Exception):
    def __init__(self, pmn_codes: list):
        self.pmn_codes = pmn_codes


@overload
def create_organization(
    db: Session,
    iam: AbstractIAM,
    *,
    organization_in: schemas.OrganizationCreate,
    org_type: Literal[OrganizationTypeEnum.CLIENT],
    parent: Distributor,
) -> OrganizationFull:
    ...


@overload
def create_organization(
    db: Session,
    iam: AbstractIAM,
    *,
    organization_in: schemas.OrganizationCreate,
    org_type: Literal[OrganizationTypeEnum.PARTNER],
    parent: Distributor,
) -> OrganizationFull:
    ...


@overload
def create_organization(
    db: Session,
    iam: AbstractIAM,
    *,
    organization_in: schemas.OrganizationCreate,
    org_type: Literal[OrganizationTypeEnum.DISTRIBUTOR],
    parent: Distributor | None = None,
) -> OrganizationFull:
    ...


def create_organization(
    db: Session,
    iam: AbstractIAM,
    *,
    organization_in: schemas.OrganizationCreate,
    org_type: OrganizationTypeEnum,
    parent: Distributor | None = None,
) -> OrganizationFull:
    if organization_name_exist(db, org_name=organization_in.name):
        raise OrganizationExistsInDb(organization_in.name)
    org_in_data = jsonable_encoder(
        organization_in.dict(exclude={"allowed_domains", "pmn_codes"})
    )

    type_model_map = {
        OrganizationTypeEnum.CLIENT: Client,
        OrganizationTypeEnum.DISTRIBUTOR: Distributor,
        OrganizationTypeEnum.PARTNER: Partner,
    }
    model = type_model_map[org_type]
    if parent is not None:
        org_in_data.update(parent=parent)
    org: Organization = model(**org_in_data)  # noqa
    db.add(org)
    db.flush()

    if organization_in.allowed_domains:
        domains_data = list(
            map(
                lambda x: Domain(org_id=org.id, domain_name=x),
                organization_in.allowed_domains,
            )
        )
        db.add_all(domains_data)

    if organization_in.pmn_codes:
        criteria = [PMNCode.pmn_code.in_(organization_in.pmn_codes)]
        exisitng_pmns = cast(
            list[str], get_object(db, PMNCode.pmn_code, True, *criteria)
        )
        if exisitng_pmns:
            raise PMNCodeAlreadyExists(exisitng_pmns)
        pmns_data = list(
            map(lambda x: PMNCode(org_id=org.id, pmn_code=x), organization_in.pmn_codes)
        )
        db.add_all(pmns_data)

    try:
        group = create_organization_group(iam, org)
    except Exception:
        db.rollback()
        raise
    try:
        org.external_id = group.id
        db.commit()
        db.refresh(org)
    except SQLAlchemyError:
        iam.groups.remove(group.path)
        db.rollback()
        raise

    return OrganizationFull(
        id=org.id,
        name=org.name,
        type=org.type,
        created_at=org.created_at,
        parent=org.parent,
        allowed_domains=organization_in.allowed_domains,
        pmn_codes=organization_in.pmn_codes,
    )


OrgT = TypeVar("OrgT", bound=Organization)


def update_organization(
    db: Session,
    identity_api: AbstractIAM,
    organization: OrgT,
    organization_in: schemas.OrganizationUpdate,
) -> OrganizationFull:
    if organization_name_exist(
        db, org_name=organization_in.name, exclude_org_id=organization.id
    ):
        raise OrganizationExistsInDb(organization_in.name)
    path = organization.path
    organization.name = organization_in.name
    db.add(organization)

    db.query(Domain).filter(Domain.org_id == organization.id).delete(
        synchronize_session=False
    )
    db.query(PMNCode).filter(PMNCode.org_id == organization.id).delete(
        synchronize_session=False
    )

    if organization_in.allowed_domains:
        domains_data = list(
            map(
                lambda x: Domain(org_id=organization.id, domain_name=x),
                organization_in.allowed_domains,
            )
        )
        db.add_all(domains_data)

    if organization_in.pmn_codes:
        criteria = [PMNCode.pmn_code.in_(organization_in.pmn_codes)]
        exisitng_pmns = cast(
            list[str], get_object(db, PMNCode.pmn_code, True, *criteria)
        )
        if exisitng_pmns:
            raise PMNCodeAlreadyExists(exisitng_pmns)
        pmns_data = list(
            map(
                lambda x: PMNCode(org_id=organization.id, pmn_code=x),
                organization_in.pmn_codes,
            )
        )
        db.add_all(pmns_data)

    group = mappers.organization_to_group(organization)
    identity_api.groups.update(path, group)

    db.commit()
    db.refresh(organization)

    return OrganizationFull(
        id=organization.id,
        created_at=organization.created_at,
        type=organization.type,
        name=organization.name,
        parent=organization.parent,
        allowed_domains=organization_in.allowed_domains,
        pmn_codes=organization_in.pmn_codes,
    )


def delete_organization(
    db: Session,
    iam: AbstractIAM,
    *,
    organization: Organization,
    delete_users: bool = False,
) -> None:
    try:
        users = iam.groups.get_members(organization.path)
        for user in users:
            if delete_users:
                iam.users.remove(user.id)
            else:
                remove_user_roles(iam, user_id=user.id)
        iam.groups.remove(organization.path)
    except GroupNotFound as e:
        logging.warning(str(e))
    db.delete(organization)
    db.commit()


def create_organization_group(
    iam: AbstractIAM,
    organization: Organization,
) -> GroupCreate:
    group = iam.groups.add(
        group=mappers.organization_to_group(organization),
        parent_path=organization.parent.path if organization.parent else None,
    )
    return group


def organization_name_exist(
    db: Session,
    *,
    org_name: str,
    exclude_org_id: int | None = None,
) -> bool:
    criterion = Organization.name.ilike(org_name)  # noqa
    if exclude_org_id is not None:
        criterion &= Organization.id != exclude_org_id
    name_exists_q = db.query(Organization).filter(criterion).exists()
    name_exists = db.query(sa.literal(True)).filter(name_exists_q).scalar()
    return name_exists is True


def add_user_to_organization(
    iam: AbstractIAM,
    *,
    organization: Organization,
    user_id: UUID4,
    organization_role: OrganizationRoleEnum,
) -> None:
    iam.groups.add_member(organization.path, user_id)
    iam.users.add_roles(user_id, [organization_role.value])


def add_user_to_organization_v2(
    iam: AbstractIAM,
    *,
    organization: Organization,
    user_id: UUID4,
    organization_role: list[str],
) -> None:
    iam.groups.add_member(organization.path, user_id)
    iam.users.add_roles(user_id, organization_role)


def remove_user_from_organization(
    iam: AbstractIAM,
    *,
    organization: Organization,
    user_id: UUID4,
) -> None:
    iam.groups.remove_member(organization.path, user_id)
    remove_user_roles(iam, user_id)


def remove_user_roles(iam: AbstractIAM, user_id: UUID4) -> None:
    roles = iam.users.get_roles(user_id)
    to_remove = extract_organization_roles(roles)
    iam.users.remove_roles(user_id, [r.value for r in to_remove])


def fetch_user_org_roles(
    iam: AbstractIAM,
    user_id: UUID4,
) -> list[OrganizationRoleEnum]:
    roles = iam.users.get_roles(user_id)
    return extract_organization_roles(roles)


def create_root_organization(
    db: Session,
    iam: AbstractIAM,
    *,
    organization_name: str,
    admin_email: str,
    admin_password: str,
) -> Distributor:
    organization = create_organization(
        db,
        iam,
        organization_in=schemas.OrganizationCreate(
            name=OrganizationNameStr(organization_name),
        ),
        org_type=OrganizationTypeEnum.DISTRIBUTOR,
    )
    organization_model = get_object_or_404(db, Distributor, False, id=organization.id)
    user = iam.users.get_by_email(EmailStr(admin_email))
    if not user:
        user = iam.users.add(
            UserCreate(
                username=admin_email,
                email=EmailStr(admin_email),
                enabled=True,
                email_verified=True,
            )
        )
        iam.users.set_password(user.id, SecretStr(admin_password), temporary=False)
    add_user_to_organization(
        iam,
        organization=organization_model,
        user_id=user.id,
        organization_role=OrganizationRoleEnum.DISTRIBUTOR_ADMIN,
    )
    return organization_model


def remove_root_organization(
    db: Session,
    iam: AbstractIAM,
    *,
    organization_name: str,
    delete_users: bool = False,
) -> None:
    org = (
        db.query(Organization)
        .filter(
            Organization.name == organization_name,
        )
        .first()
    )
    if not org:
        raise NotFound()
    _delete_organization_recursively(
        db,
        iam,
        org=org,
        delete_users=delete_users,
    )


def _delete_organization_recursively(
    db: Session,
    iam: AbstractIAM,
    *,
    org: Organization,
    delete_users: bool = False,
) -> None:
    child: Organization
    for child in org.children:
        _delete_organization_recursively(
            db,
            iam,
            org=child,
            delete_users=delete_users,
        )

    delete_organization(
        db,
        iam,
        organization=org,
        delete_users=delete_users,
    )


def is_organization_group(group: Group) -> bool:
    return "organization" in group.attributes.get("type", [])


def get_user_org(
    db: Session, iam: AbstractIAM, *, user_id: UUID4
) -> Organization | None:
    groups = iam.users.get_groups(user_id)
    organization_groups = filter(is_organization_group, groups)
    organization_group = next(organization_groups, None)
    if not organization_group:
        return None

    org_id_attr = organization_group.attributes.get("organization_id")
    if not (org_id_attr and len(org_id_attr) == 1):
        raise ValueError("Organization group must contain `organization_id` attribute")
    organization_id = org_id_attr[0]
    organization: Organization | None = (
        db.query(Organization).filter(Organization.id == organization_id).first()
    )
    return organization


def get_allowed_roles(organization: Organization) -> list[schemas.OrganizationRole]:
    roles = []
    prefix = organization.type.value.lower().capitalize()
    for role in organization.type.roles:
        role_representation = schemas.OrganizationRole(
            name=role.value, display_name=role.value.replace(prefix, "")
        )
        roles.append(role_representation)
    return roles


@overload
def get_descendants(
    db: Session,
    root_org: Organization,
    page: int | None,
    page_size: int | None,
    count: Literal[False],
    *criteria: Any,
    **kwargs: Any,
) -> list[Row]:
    ...


@overload
def get_descendants(
    db: Session,
    root_org: Organization,
    page: int | None,
    page_size: int | None,
    count: Literal[True],
    *criteria: Any,
    **kwargs: Any,
) -> int:
    ...


def get_descendants(
    db: Session,
    root_org: Organization,
    page: int | None,
    page_size: int | None,
    count: bool,
    sort_by: sa.Column | None = None,
    *criteria: Any,
    **kwargs: Any,  # can only filter by keys or columns of last joined table
    # As we have multiplej oins, query is not able to filter by columns of all tables
) -> list[Row] | int:
    base_query = descendants_query(root_org.id).filter(*criteria).filter_by(**kwargs)
    if count:
        count_query = select(func.count()).select_from(base_query.subquery())
        return db.execute(count_query).scalar_one()
    if sort_by is not None:
        base_query = base_query.order_by(sort_by.desc())

    if page and page_size:
        base_query = base_query.limit(page_size).offset((page - 1) * page_size)

    ret = db.execute(base_query).all()
    return ret


def descendants_query(org_id: int) -> Select:
    root = select(
        Organization.id,
        Organization.parent_id,
    ).filter_by(id=org_id)
    cte = root.cte(recursive=True, name="descendants")
    descendant = cte.union_all(
        select(
            Organization.id,
            Organization.parent_id,
        ).join(cte, cte.c.id == Organization.parent_id)
    )
    return (
        select(
            Organization,
            array_agg(distinct(PMNCode.pmn_code)).filter(PMNCode.pmn_code != []),
            array_agg(distinct(Domain.domain_name)).filter(Domain.domain_name != []),
        )
        .outerjoin(PMNCode, Organization.id == PMNCode.org_id)
        .outerjoin(Domain, Organization.id == Domain.org_id)
        .filter(
            Organization.id.in_(select(descendant.c.id)),  # noqa
        )
        .group_by(Organization.id, Organization.parent_id)
    )


def get_members(
    iam: AbstractIAM,
    organization: Organization,
    fetch_roles: bool = False,
    active_only: bool = False,
) -> list[UserDict]:
    users: Iterable[User] = iam.groups.get_members(organization.path)
    if active_only:
        users = (u for u in users if u.enabled and u.email_verified)
    keycloak_users = (to_keycloak(u, KeycloakUser) for u in users)
    result = []  # type: ignore
    for user in keycloak_users:
        user_dict = user.dict(by_alias=True)
        if fetch_roles:
            user_dict["roles"] = fetch_user_org_roles(iam, user.id)

        user_dict["identityProvider"] = alias_to_identity_provider(
            iam,
            user.identity_provider,
        )
        result.append(user_dict)
    return cast(list[UserDict], result)


class UserState(str, Enum):
    DRAFT = "DRAFT"
    RESERVED = "RESERVED"
    REGISTERED = "REGISTERED"

    # Private fields prior to python 3.11
    # are considered enum values
    # so we use properties for constants
    @property
    def attribute_namespace(self) -> str:
        return "organizations-api"

    @property
    def attribute_name(self) -> str:
        return "user-state"

    def as_attributes(self) -> dict[str, list[str]]:
        return {f"{self.attribute_namespace}:{self.attribute_name}": [self.value]}

    def in_attributes(self, attributes: dict[str, list[str]]) -> bool:
        return self.as_attributes().items() <= attributes.items()


def add_partner_user(
    db: Session,
    iam: AbstractIAM,
    core_api: AbstractCoreFacade,
    user_in: schemas.CreatePartnerUser,
) -> schemas.UserV2:
    iam_user = iam.users.get_by_email(user_in.email)
    created = False
    if not iam_user:
        iam_user = iam.users.add(
            UserCreate(
                username=user_in.email,
                email=user_in.email,
                enabled=False,
                email_verified=False,
                first_name=user_in.first_name,
                last_name=user_in.last_name,
                attributes=UserState.DRAFT.as_attributes(),
            )
        )
        created = True
    if not UserState.DRAFT.in_attributes(iam_user.attributes):
        raise UserAlreadyExists(f'User with email "{user_in.email}" already exists.')

    organization = get_or_create_organization_for_partner(
        db, iam, user_in.organization_name
    )
    if not organization.type != OrganizationTypeEnum.DISTRIBUTOR:
        raise ValueError("get_or_create should not return distributor organization")
    if organization.type is OrganizationTypeEnum.PARTNER:
        role = OrganizationRoleEnum.PARTNER_USER
    elif organization.type is OrganizationTypeEnum.CLIENT:
        role = OrganizationRoleEnum.CLIENT_USER
    else:
        raise ValueError(f'Uncovered organization type "{organization.type}"')

    if not created:
        user_org = get_user_org(db, iam, user_id=iam_user.id)
        if user_org:
            remove_user_from_organization(
                iam, organization=user_org, user_id=iam_user.id
            )
    add_user_to_organization(
        iam,
        organization=organization,
        user_id=iam_user.id,
        organization_role=role,
    )

    user = schemas.UserV2(
        id=iam_user.id,
        email=user_in.email,
        username=user_in.email,
        first_name=user_in.first_name,
        last_name=user_in.last_name,
        organization=schemas.OrganizationSimple.from_orm(organization),
        mobile_phone=user_in.mobile_phone,
    )

    core_api.notify_user_registered(user)
    try:
        core_api.add_contact(user)
    except Exception as e:
        logging.error(str(e))

    if user.mobile_phone:
        iam_user.attributes["mobilePhone"] = [user.mobile_phone]

    iam_user.attributes.update(UserState.RESERVED.as_attributes())
    iam_user.first_name = user_in.first_name
    iam_user.last_name = user_in.last_name
    iam.users.update(iam_user.to_update())
    return user


def get_or_create_organization_for_partner(
    db: Session, iam: AbstractIAM, organization_name: str
) -> Organization:
    organization = get_object(
        db, Organization, False, Organization.name.ilike(organization_name)  # noqa
    )
    if organization and organization.type is OrganizationTypeEnum.DISTRIBUTOR:
        raise OperationNotAllowed(
            f'Cannot use organization "{organization.name}" for a partner.'
        )
    elif organization:
        return organization

    distributor = get_object(db, Distributor, False, parent_id=None)
    if not distributor:
        logging.error("Distributor not found")
        raise NotFound()
    organization_model = create_organization(
        db,
        iam,
        organization_in=schemas.OrganizationCreate(
            name=OrganizationNameStr(organization_name)
        ),
        org_type=OrganizationTypeEnum.PARTNER,
        parent=distributor,
    )
    return get_object_or_404(db, Organization, False, id=organization_model.id)


def enable_partner_user(
    db: Session, iam: AbstractIAM, user_id: UUID4, cmd: schemas.EnablePartnerUser
) -> None:
    iam_user = iam.users.get_by_id(user_id)
    if iam_user is None:
        raise OperationNotAllowed(f"User not found {user_id}")
    if not UserState.RESERVED.in_attributes(iam_user.attributes):
        raise OperationNotAllowed("Not a partner user or already registered.")

    organization = get_user_org(db, iam, user_id=iam_user.id)
    if not organization:
        raise OperationNotAllowed("The user is not a member of any organization.")

    # When a password doesn't match the password policy,
    # all user attributes except the password are updated.
    # So the password is updated before attributes
    try:
        iam.users.set_password(iam_user.id, cmd.password, temporary=False)
    except BadPassword as e:
        raise OperationNotAllowed(str(e))

    logging.info(f"type:{organization.type}{OrganizationTypeEnum.PARTNER}")
    iam_user.enabled = organization.type is OrganizationTypeEnum.PARTNER
    iam_user.email_verified = True
    iam_user.attributes.update(UserState.REGISTERED.as_attributes())
    iam.users.update(iam_user.to_update())


def get_impersonation_url(
    iam: AbstractIAM, organization_users: list[UserDict]
) -> AnyHttpUrl | None:
    admin_id = None
    for user in organization_users:
        roles = fetch_user_org_roles(iam, user["id"])
        if set(roles).intersection(OrganizationTypeEnum.DISTRIBUTOR.roles):
            # only clients
            break
        if OrganizationRoleEnum.CLIENT_ADMIN in roles:
            admin_id = user["id"]
            break

    if admin_id is None:
        return None
    else:
        return build_impersonation_url(admin_id)


def build_impersonation_url(user_id: UUID4) -> AnyHttpUrl:
    query_params = dict(
        user_id=str(user_id), redirect_url=make_sign_out_url(settings.LANDING_PAGE_URL)
    )
    query = urlencode(query_params)
    components = urlsplit(settings.KEYCLOAK_URL)
    replace = components._replace(  # noqa
        path="/auth-extension/impersonate", query=query
    )
    url = urlunsplit(replace)
    return parse_obj_as(AnyHttpUrl, url)


def make_sign_out_url(rd: AnyHttpUrl) -> AnyHttpUrl:
    query_params = dict(rd=rd)
    query = urlencode(query_params)
    components = urlsplit(str(settings.SIGN_OUT_URL))
    replace = components._replace(query=query)
    return parse_obj_as(AnyHttpUrl, urlunsplit(replace))


def extract_organization_roles(user_roles: Iterable[str]) -> list[OrganizationRoleEnum]:
    roles: list[OrganizationRoleEnum] = []
    for role in user_roles:
        try:
            roles.append(OrganizationRoleEnum(role))
        except ValueError:
            # skip unrelated roles
            pass
    return roles


def extract_org_created_by(audit_service: AbstractAuditAPI, organization: Organization):
    if organization.id:
        audit = audit_service.get_audit(id=organization.id, event="POST")
        return audit.user if audit else None


def extract_org_members(organization: Organization) -> list[UserDict]:
    redis_server = RedisService()
    member: Dict[str, Dict[str, Any]] = {}
    if organization.external_id:
        member = redis_server.get_group_users(organization.external_id)
    return cast(list[UserDict], [{"id": UUID(uid)} for uid in member]) if member else []


def create_user_file_record(
    db: Session, organization_id: int, filename: str, s3_path: str
) -> UserFile:
    """Create a record in the user_file table."""
    user_file = UserFile(
        organization_id=organization_id, file_name=filename, path=s3_path
    )
    db.add(user_file)
    db.commit()
    db.refresh(user_file)
    return user_file


def create_file_report_record(
    db: Session,
    user_file_id: int,
    total_rows: int,
    valid_rows: int,
    status: FileReportStatusEnum,
) -> FileReport:
    """Create a record in the file_report table."""
    file_report = FileReport(
        user_file_id=user_file_id,
        status=status,
        total_rows=total_rows,
        valid_rows=valid_rows,
    )
    db.add(file_report)
    db.commit()
    db.refresh(file_report)
    return file_report


def create_user_report_records(
    db: Session,
    user_file_id: int,
    invalid_rows: list[dict[str, Any]],
) -> list[UserReport]:
    """Create records in the user_report table for invalid rows."""
    user_reports = []

    for row in invalid_rows:
        user_report = UserReport(
            user_file_id=user_file_id,
            first_name=row.get("first_name", ""),
            last_name=row.get("last_name", ""),
            email=row.get("email", ""),
            reason=row.get("error", ""),
        )
        db.add(user_report)
        user_reports.append(user_report)

    db.commit()
    return user_reports


def process_bulk_user_creation(
    db: Session,
    iam: AbstractIAM,
    response: bytes,
    organization: Organization,
    audit_service: AbstractAuditAPI,
    data: schemas.BulkUserCreateRequest,
    message: AuditLogModel,
    user_info,
    background_tasks: BackgroundTasks = None,
) -> UUID:
    file_basename = data.fileName.split("/")[-1]

    valid_rows, validation_errors = validate_csv_content(file_content=response)

    total_rows = len(valid_rows) + len(validation_errors)

    status = FileReportStatusEnum.IN_PROGRESS
    if (validation_errors and not valid_rows) or total_rows == 0:
        status = FileReportStatusEnum.FAILED

    user_file = create_user_file_record(
        db=db,
        organization_id=organization.id,
        filename=file_basename,
        s3_path=data.fileName,
    )

    create_file_report_record(
        db=db,
        user_file_id=user_file.id,
        total_rows=total_rows,
        valid_rows=0,
        status=status,
    )

    if validation_errors:
        create_user_report_records(
            db=db,
            user_file_id=user_file.id,
            invalid_rows=validation_errors,
        )

    if background_tasks and valid_rows:
        background_tasks.add_task(
            _process_csv_file_background,
            user_file_id=user_file.id,
            organization=organization,
            roles=data.roles,
            db=db,
            iam=iam,
            identity_provider=data.identityProvider,
            valid_rows=valid_rows,
            is_error=False if not validation_errors else True,
            audit_service=audit_service,
            message=message,
            user_info=user_info,
        )

    return user_file.uuid


def _process_csv_file_background(
    user_file_id: int,
    valid_rows: list[dict[str, Any]],
    roles: list[str],
    iam: AbstractIAM,
    db: Session,
    organization: Organization,
    is_error: bool,
    identity_provider: str | None,
    audit_service: AbstractAuditAPI,
    message: AuditLogModel,
    user_info,
) -> None:
    """
    Background task to process the CSV file.
    This runs after the API has already responded to the client.
    """

    try:
        if valid_rows:
            _process_valid_rows(
                db,
                iam,
                organization,
                user_file_id,
                valid_rows,
                roles,
                identity_provider,
                is_error,
                audit_service,
                message,
                user_info,
            )

    except Exception as e:
        logging.error(f"Error in background processing: {str(e)}")
        _update_file_report(db, user_file_id, FileReportStatusEnum.FAILED)

    finally:
        db.close()


def _process_valid_rows(
    db: Session,
    iam: AbstractIAM,
    organization: Organization,
    user_file_id: int,
    valid_rows: list[dict[str, Any]],
    roles: list[str],
    identity_provider: str | None,
    is_error: bool,
    audit_service: AbstractAuditAPI,
    message: AuditLogModel,
    user_info,
) -> None:
    """Process valid rows by creating users and adding them to the organization."""

    successful_users = 0
    failed_rows = []
    for row in valid_rows:
        email = row.get("email", "")
        user_already_exists = iam.users.get_by_email(email)
        if user_already_exists:
            if iam.users.get_groups(user_already_exists.id):
                failed_rows.append(
                    {**row, "error": f"User with email {email} already exists"}
                )
                continue

        try:
            user_in = UserCreateV2(
                email=email,
                username=email,
                firstName=row.get("first_name", ""),
                lastName=row.get("last_name", ""),
                enabled=True,
                emailVerified=True,
                roles=roles,
                identityProvider=identity_provider,
            )
            created_user, _ = users.add_user(db, iam, user_info, user_in, organization)

            message.payload = {"id": created_user.id}
            audit_service.add_audit(message)

            successful_users += 1
            logging.info(f"Successfully created user: {created_user.email}")

        except Exception as e:
            error_message = f"Failed to create user {row.get('email')}: {str(e)}"
            logging.error(error_message)
            failed_rows.append(
                {
                    "first_name": row.get("first_name", ""),
                    "last_name": row.get("last_name", ""),
                    "email": row.get("email", ""),
                    "error": error_message,
                }
            )

    if failed_rows:
        create_user_report_records(
            db=db,
            user_file_id=user_file_id,
            invalid_rows=failed_rows,
        )

    status = (
        FileReportStatusEnum.COMPLETED
        if not is_error and not failed_rows
        else FileReportStatusEnum.COMPLETED_WITH_ERRORS
    )
    _update_file_report(db, user_file_id, status, successful_users)

    logging.info(
        f"Bulk creation completed: {successful_users} created,"
        f" {len(failed_rows)} failed"
    )


def _update_file_report(
    db: Session,
    user_file_id: int,
    status: FileReportStatusEnum = None,
    valid_rows: int = None,
) -> None:
    try:
        query = select(FileReport).where(FileReport.user_file_id == user_file_id)
        file_report = db.execute(query).scalar_one_or_none()
        if not file_report:
            logging.error(f"No file report found for user_file_id {user_file_id}")
            return

        if status is not None:
            file_report.status = status
        if valid_rows is not None:
            file_report.valid_rows = valid_rows

        db.commit()
        db.refresh(file_report)
    except Exception as e:
        logging.error(f"Error updating file report: {str(e)}")
        db.rollback()


def get_user_file_status(
    db: Session,
    organization_id: int,
    pagination: Pagination,
    audit_service: AbstractAuditAPI,
) -> tuple[list[schemas.BulkUserNotificationStatus], int]:
    total_count = (
        db.query(UserFile).filter(UserFile.organization_id == organization_id).count()
    )

    base_query = (
        select(UserFile.file_name, UserFile.uuid, FileReport.status)
        .join(FileReport, UserFile.id == FileReport.user_file_id)
        .filter(UserFile.organization_id == organization_id)
    )

    if pagination:
        base_query = base_query.offset(
            (pagination.page - 1) * pagination.page_size
        ).limit(pagination.page_size)

    user_files = db.execute(base_query).all()
    if not user_files:
        raise NotFound("User file not found.")
    logging.info(f"user_files {user_files}")
    results = []
    for user_file in user_files:
        logging.info(f"user_file {user_file}")
        userfile_audit = audit_service.get_audit(id=user_file.uuid, event="POST")
        results.append(
            schemas.BulkUserNotificationStatus(
                fileName=user_file.file_name[7:],
                id=user_file.uuid,
                status=user_file.status,
                createdBy=userfile_audit.user if userfile_audit else None,
                createdAt=userfile_audit.created_date if userfile_audit else None,
            )
        )
    return results, total_count


def get_user_file_report(
    db: Session, organization_id: int, user_file_id: UUID
) -> Optional[dict[str, Any]]:
    user_file = (
        db.query(UserFile)
        .filter(
            UserFile.uuid == user_file_id,
            UserFile.organization_id == organization_id,
        )
        .first()
    )

    if not user_file:
        return None

    file_report = (
        db.query(FileReport).filter(FileReport.user_file_id == user_file.id).first()
    )

    if not file_report:
        return {"totalRows": 0, "validRows": 0, "invalidRows": []}

    user_reports = (
        db.query(UserReport).filter(UserReport.user_file_id == user_file.id).all()
    )

    invalid_rows = [
        {
            "email": report.email,
            "firstName": report.first_name,
            "lastName": report.last_name,
            "reason": report.reason,
        }
        for report in user_reports
    ]

    return {
        "totalRows": file_report.total_rows or 0,
        "validRows": file_report.valid_rows or 0,
        "invalidRows": invalid_rows,
    }
