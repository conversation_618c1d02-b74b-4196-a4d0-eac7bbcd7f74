import uuid

from app import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import ClientAlreadyExists, ClientScopeNotFound, RoleNotFound
from app.adapters.iam.schemas import ClientBase
from app.models import Organization
from app.services.authz import Base<PERSON><PERSON><PERSON><PERSON>x<PERSON>, ResourceNotFound


class BadClient(BaseAuthZException):
    ...


class ClientNotFound(ResourceNotFound):
    ...


class ServiceAccountAlreadyExists(BaseAuthZException):
    ...


def setup_service_account(
    iam: AbstractIAM,
    client_id: str,
    *,
    config: schemas.ServiceAccountConfig,
) -> uuid.UUID:
    """
    Enables service-to-service calls for given client and configure attributes
    """
    client = iam.clients.get(client_id)
    if not client:
        raise ClientNotFound(f'Client "{client_id} not found')
    if client.public_client:
        raise BadClient("Service account not allowed for public client")
    if client.bearer_only:
        raise BadClient("Service account not allowed for bearer only client")

    if not client.service_accounts_enabled:
        update = ClientBase.parse_obj(client.dict())
        update.service_accounts_enabled = True
        client = iam.clients.update(update)

    service_account = iam.clients.get_service_account_user(client.client_id)
    service_account.email_verified = True
    service_account.attributes.update(config.attributes)
    iam.clients.update_service_account_user(service_account)

    try:
        iam.users.add_roles(service_account.id, config.roles)
        iam.clients.add_default_client_scopes(client_id, config.scopes)
    except RoleNotFound as e:
        raise ResourceNotFound(f'Role "{e}" not found.')
    except ClientScopeNotFound as e:
        raise ResourceNotFound(f'Scope "{e}" not found.')

    return service_account.id


def create_service_account(
    iam: AbstractIAM,
    service_account_in: schemas.ServiceAccountCreate,
    organization: Organization,
) -> schemas.ServiceAccountCreated:
    client_id = str(uuid.uuid4())
    try:
        client_in = ClientBase(
            client_id=client_id,
            name=service_account_in.name,
            enabled=True,
            service_accounts_enabled=True,
            public_client=False,
        )
        iam.clients.add(client_in)
        iam.clients.add_optional_client_scopes(
            client_id,
            scopes=service_account_in.permissions,
        )
        secret = iam.clients.get_secret(client_id)
    except ClientAlreadyExists as e:
        raise ServiceAccountAlreadyExists(str(e))
    except ClientScopeNotFound as e:
        raise ResourceNotFound(f"Client scope {e} not found")

    service_account_id = setup_service_account(
        iam,
        client_id=client_id,
        config=schemas.ServiceAccountConfig(),
    )
    iam.groups.add_member(organization.path, service_account_id)
    return schemas.ServiceAccountCreated(
        client_id=client_id,
        client_secret=secret,
        name=service_account_in.name,
        permissions=service_account_in.permissions,
    )
