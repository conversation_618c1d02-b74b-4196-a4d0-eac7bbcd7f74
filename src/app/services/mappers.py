from functools import partial
from typing import Callable

from pydantic import AnyHttpUrl

from app import models, schemas
from app.adapters.iam import schemas as iam_schemas
from app.adapters.iam.base import AbstractIAM
from app.schemas.organization import OrganizationNameStr
from app.services.authz_password import AbstractPasswordPolicy
from app.typedefs import UserDict


def organization_to_group(organization: models.Organization) -> iam_schemas.GroupBase:
    return iam_schemas.GroupBase(
        name=organization.name,
        attributes={
            "type": ["organization"],
            "organization_type": [organization.type.value],
            "organization_id": [str(organization.id)],
        },
    )


def organization_to_descendant(
    organization: models.Organization,
    pmns: list[str] | None,
    domains: list[str] | None,
    *,
    get_users: Callable[[models.Organization], list[UserDict]],
    get_impersonation_url: Callable[[list[UserDict]], AnyHttpUrl | None],
    get_created_by: Callable[[models.Organization], str | None],
) -> schemas.DescendantOrganization:
    users = get_users(organization)
    impersonation_url = get_impersonation_url(users)
    created_by = get_created_by(organization)
    return schemas.DescendantOrganization(
        id=organization.id,
        name=OrganizationNameStr(organization.name),
        parent=schemas.OrganizationSimple.from_orm(organization.parent)
        if organization.parent
        else None,
        type=organization.type,
        number_of_users=len(users),
        created_at=organization.created_at,
        impersonation_url=impersonation_url,
        pmn_codes=pmns if pmns else [],
        allowed_domains=domains if domains else [],
        created_by=created_by,
    )


def create_descendant_mapper(
    **kwargs,
) -> Callable[
    [models.Organization, list[str], list[str]], schemas.DescendantOrganization
]:
    return partial(organization_to_descendant, **kwargs)


def user_info_to_user(
    user_info: schemas.UserInfo, organization: models.Organization
) -> schemas.User:
    return schemas.User(
        id=user_info.id,
        email=user_info.email,
        username=user_info.username,
        organization=schemas.OrganizationSimple.from_orm(organization),
        firstName=user_info.given_name,
        lastName=user_info.family_name,
    )


def alias_to_identity_provider(
    iam: AbstractIAM,
    alias: str | None,
) -> schemas.KeycloakIdentityProvider | None:
    idp = iam.identity_providers.get_by_alias(alias) if alias else None
    if idp is not None:
        return schemas.KeycloakIdentityProvider(
            alias=idp.alias,
            displayName=idp.display_name,
        )
    return None


def password_policy_to_schema(policy: AbstractPasswordPolicy) -> schemas.PasswordPolicy:
    return schemas.PasswordPolicy(
        id=policy.id,
        value=policy.value,
        display_name=policy.display_name,
        description=policy.description,
    )
