from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.schemas import (
    ClientScope,
    ProtocolMapper,
    ProtocolMapperType,
    Role,
)

APP_MANAGEMENT_SCOPE_NAME = "application-management"
APP_ROLE_NAME = "Application"


def get_app_management_scope(iam: AbstractIAM) -> ClientScope | None:
    return iam.client_scopes.get_by_name(APP_MANAGEMENT_SCOPE_NAME)


def create_application_role(iam: AbstractIAM) -> None:
    iam.roles.add(Role(name=APP_ROLE_NAME))


def get_application_role(iam: AbstractIAM) -> Role | None:
    return iam.roles.get(APP_ROLE_NAME)


def create_app_management_scope(iam: AbstractIAM) -> None:
    application_id_mapper = ProtocolMapper(
        name="Application ID",
        type=str(ProtocolMapperType.USER_ATTRIBUTE.value),
        consent_required=False,
        config={
            "userinfo.token.claim": "true",
            "user.attribute": "application_id",
            "id.token.claim": "false",
            "access.token.claim": "true",
            "claim.name": "application.id",
            "jsonType.label": "int",
        },
    )
    app_management_aud_mapper = ProtocolMapper(
        name="application-management audience",
        type=str(ProtocolMapperType.AUDIENCE.value),
        consent_required=False,
        config={
            "id.token.claim": "false",
            "access.token.claim": "true",
            "included.custom.audience": "application-management",
        },
    )
    scope = ClientScope(
        name=APP_MANAGEMENT_SCOPE_NAME,
        attributes={
            "include.in.token.scope": "true",
            "display.on.consent.screen": "false",
        },
        protocol_mappers=[
            application_id_mapper,
            app_management_aud_mapper,
        ],
    )
    iam.client_scopes.add(scope)
    iam.client_scopes.add_roles(scope.name, [APP_ROLE_NAME])
