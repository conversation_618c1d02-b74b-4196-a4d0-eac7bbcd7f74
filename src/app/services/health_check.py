from contextlib import AbstractContextManager
from types import TracebackType
from typing import cast

from fastapi import Request

HEALTH_CHECK_HEADER = "Healthy-Header"


class HealthCheckError(RuntimeError):
    def __init__(self, detail: dict[str, str]):
        self.detail = detail

    def __str__(self) -> str:
        return f"{self.__class__.__qualname__}: \n" + "\n".join(
            f'"{service}": {error}' for service, error in self.detail.items()
        )


class ErrorHandler:
    """
    Aggregate errors with context manager.

    Usage::

        handler = ErrorHandler()
        with handler(service_name='database'):
            raise Exception('foo')
        with handler(service_name='redis'):
            raise Exception('bar')
        handler.raise_for_errors()

    Will raise
    HealthCheckError({'database': 'foo', 'redis': 'bar'})

    """

    def __init__(self) -> None:
        self._errors: dict[str, str] = {}

    def __call__(self, service_name: str) -> "ErrorHandlerContextManager":
        return ErrorHandlerContextManager(self, service_name)

    def add_error(self, service_name: str, exc_val: BaseException) -> None:
        self._errors[service_name] = str(exc_val)

    def raise_for_errors(self) -> None:
        if self._errors:
            raise HealthCheckError(self._errors)


class ErrorHandlerContextManager(AbstractContextManager):  # type: ignore
    def __init__(self, handler: ErrorHandler, service_name: str):
        self._handler = handler
        self._service_name = service_name

    def __enter__(self) -> ErrorHandler:
        return self._handler

    def __exit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> bool | None:
        if exc_val:
            self._handler.add_error(self._service_name, exc_val)
        return True


def get_healthy_secret(request: Request) -> str:
    secret: str = request.headers.get(cast(str, HEALTH_CHECK_HEADER), "")
    return secret
