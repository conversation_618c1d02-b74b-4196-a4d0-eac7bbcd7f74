import logging
import re
import secrets
import string
from abc import ABC, abstractmethod
from collections.abc import Sequence
from typing import Any, Generic, Protocol, TypeVar

from pydantic import SecretStr

from app.adapters.iam.schemas import User
from app.core.config import settings
from app.core.utils import quantify
from app.schemas.mail import Address, EmailMessage, Subject


class PasswordGenerator:
    MIN_LENGTH = 1
    SPECIAL_SYMBOLS = "!@#$%^&*"
    MAX_ITERATIONS = 10000

    def __init__(
        self,
        default_length: int = 18,
        policies: Sequence["AbstractPasswordPolicy"] = (),
    ):
        min_length, max_length = self.min_max_length(policies)
        self.length = self.get_length(default_length, min_length, max_length)
        self.policies = policies

    @classmethod
    def get_length(
        cls,
        default_length: int,
        min_length: int | None,
        max_length: int | None,
    ) -> int:
        length = default_length
        if min_length and max_length and min_length > max_length:
            raise ValueError(
                f"Min length({min_length}) is greater " f"than max length({max_length})"
            )
        if min_length and length < min_length:
            length = min_length
        elif max_length and max_length < length:
            length = max_length
        if length < cls.MIN_LENGTH:
            raise ValueError(f"Length must be at least {cls.MIN_LENGTH}")
        return length

    @classmethod
    def min_max_length(
        cls, policies: Sequence["AbstractPasswordPolicy"]
    ) -> tuple[int | None, int | None]:
        min_length = None
        max_length = None
        for policy in policies:
            if isinstance(policy, LengthPasswordPolicy):
                min_length = policy.value
            elif isinstance(policy, MaxLengthPasswordPolicy):
                max_length = policy.value
        return min_length, max_length

    def __call__(self) -> SecretStr:
        symbols = string.hexdigits + self.SPECIAL_SYMBOLS
        for i in range(self.MAX_ITERATIONS):
            password = "".join(secrets.choice(symbols) for _ in range(self.length))
            if all([p.verify(password) for p in self.policies]):
                return SecretStr(password)
        else:
            policies = " and ".join(map(str, self.policies))
            raise RuntimeError(
                "Too many iterations for password generation." f"Policies:{policies}"
            )


class AbstractPasswordPolicy(Protocol):
    id: str
    value: None | str | int

    @property
    @abstractmethod
    def display_name(self) -> str:
        ...

    @property
    @abstractmethod
    def description_fmt(self) -> str | None:
        ...

    @property
    def description(self) -> str | None:
        if self.description_fmt is None:
            return None
        description = self.description_fmt.format(self.value)
        return description

    @abstractmethod
    def verify(self, password: str) -> bool:
        ...


ValueT = TypeVar("ValueT", bound=str | int | None)


class BasePasswordPolicy(AbstractPasswordPolicy, ABC, Generic[ValueT]):
    _registry: dict[str, type["BasePasswordPolicy"]] = {}

    def __init__(self, value: ValueT, **context: Any):
        self.value: ValueT = value
        self.context = context

    def __init_subclass__(cls, id_: str, **kwargs):  # noqa
        super().__init_subclass__(**kwargs)
        cls.id = id_
        cls._registry[id_] = cls

    def __repr__(self):
        return f"<{self.__class__.__name__}({self.value}, {self.context})>"

    def __eq__(self, other: Any) -> bool:
        if not type(other) is type(self):
            return False
        return self.value == other.value and self.context == other.context

    @classmethod
    def create(
        cls, id_: str, value: None | str | int, **context
    ) -> "BasePasswordPolicy":
        try:
            klass = cls._registry[id_]
        except ValueError:
            raise ValueError(f"Unknown password policy {id_}")
        return klass(value, **context)


class DigitsPasswordPolicy(BasePasswordPolicy[int], id_="digits"):
    display_name = "Digits"
    description_fmt = "at least {0} numerical digits"

    def verify(self, password: str) -> bool:
        count = 0
        for c in password:
            if c.isdigit():
                count += 1
        return self.value <= count


class ForceExpiredPasswordChange(
    BasePasswordPolicy[int], id_="forceExpiredPasswordChange"
):
    display_name = "Expire Password"
    description_fmt = None

    def verify(self, password: str) -> bool:
        return True


class HashAlgorithmPasswordPolicy(BasePasswordPolicy[str], id_="hashAlgorithm"):
    display_name = "Hashing Algorithm"
    description_fmt = None

    def verify(self, password: str) -> bool:
        return True


class HashIterationsPasswordPolicy(BasePasswordPolicy[int], id_="hashIterations"):
    display_name = "Hashing Iterations"
    description_fmt = None

    def verify(self, password: str) -> bool:
        return True


class LengthPasswordPolicy(BasePasswordPolicy[int], id_="length"):
    display_name = "Minimum Length"
    description_fmt = "minimum length {0}"

    def verify(self, password: str) -> bool:
        return len(password) >= self.value


class MaxLengthPasswordPolicy(BasePasswordPolicy[int], id_="maxLength"):
    display_name = "Maximum Length"
    description_fmt = "maximum length {0}"

    def verify(self, password: str) -> bool:
        return len(password) <= self.value


class RegexPatternPasswordPolicy(BasePasswordPolicy[str], id_="regexPattern"):
    display_name = "Regular Expression"
    description_fmt = None

    def verify(self, password: str) -> bool:
        return re.fullmatch(self.value, password) is not None


class SpecialCharsPasswordPolicy(BasePasswordPolicy[int], id_="specialChars"):
    display_name = "Special Characters"
    description_fmt = "at least {0} special characters"

    def verify(self, password: str) -> bool:
        count = quantify(password, lambda c: not c.isalnum())
        return count >= self.value


class LowerCasePasswordPolicy(BasePasswordPolicy[int], id_="lowerCase"):
    display_name = "Lowercase Characters"
    description_fmt = "at least {0} lower case characters"

    def verify(self, password: str) -> bool:
        count = quantify(password, str.islower)
        return count >= self.value


class UpperCasePasswordPolicy(BasePasswordPolicy[int], id_="upperCase"):
    display_name = "Uppercase Characters"
    description_fmt = "at least {0} upper case characters"

    def verify(self, password: str) -> bool:
        count = quantify(password, str.isupper)
        return count >= self.value


class NotEmailPasswordPolicy(BasePasswordPolicy[None], id_="notEmail"):
    display_name = "Not Email"
    description_fmt = "not be equal to the email"

    def verify(self, password: str) -> bool:
        email = self.context.get("email", None)
        if email is None:
            return True
        return email != password


class NotUsernamePasswordPolicy(BasePasswordPolicy[None], id_="notUsername"):
    display_name = "Not Username"
    description_fmt = "not be equal to the username"

    def verify(self, password: str) -> bool:
        username = self.context.get("username", None)
        if username is None:
            return True
        return username != password


class BlacklistPasswordPolicy(BasePasswordPolicy[str], id_="passwordBlacklist"):
    display_name = "Password Blacklist"
    description_fmt = None

    def verify(self, password: str) -> bool:
        # on keycloak side
        return True


class HistoryPasswordPolicy(BasePasswordPolicy[int], id_="passwordHistory"):
    display_name = "Not Recently Used"
    description_fmt = "must not be equal to any of last {0} passwords"

    def verify(self, password: str) -> bool:
        # on keycloak side
        return True


def render_password_email(user: User, password: SecretStr) -> EmailMessage:
    logging.info(f"User_detail_information {user}")
    name = " ".join(filter(None, [user.first_name, user.last_name])) or None
    msg = EmailMessage(
        subject=Subject(f"Login to {settings.PORTAL_NAME}"),
        recipients=[Address(email=user.email, name=name)],
        html=_PARTNER_PASSWORD_TEMPLATE.format(
            name=name or user.email,
            password=password.get_secret_value(),
            portal_url=settings.PORTAL_URL,
            portal_name=settings.PORTAL_NAME,
        ),
    )
    return msg


_PARTNER_PASSWORD_TEMPLATE = """\
Dear {name}, use the password "{password}" to log in to the {portal_name}.<br><br>
{portal_name} URL: <a href="{portal_url}" target="_blank">{portal_url}</a>
"""  # nosec B105
# false positive — we're not hardcoding a password
