import logging
from uuid import <PERSON><PERSON><PERSON>

from pydantic import EmailStr, SecretStr

from app import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.schemas import TokenRequest, TokenResponse, UserCreate
from app.core.config import settings
from app.services import org_management


def create_validate_user(
    iam: AbstractIAM,
    *,
    user_info: schemas.UserInfo,
    target_org,
) -> tuple[str, UUID]:
    copy_email = EmailStr(f"copy_{user_info.organization.id}_{user_info.email}")
    user_info_by_email = iam.users.get_by_email(copy_email)
    logging.info(f"copy_email: {copy_email}, user_info: {user_info_by_email}")
    if not user_info_by_email:
        logging.info("creating copy user.....")
        roles = iam.users.get_roles(user_info.id)
        user_request = UserCreate(
            email=copy_email,
            username=copy_email,
            first_name=user_info.given_name,
            last_name=user_info.family_name,
            enabled=True,
            email_verified=True,
        )
        user = iam.users.add(user_request)
        org_management.add_user_to_organization_v2(
            iam,
            organization=target_org,
            user_id=user.id,
            organization_role=roles,
        )
        password = SecretStr(settings.SERVICE_ACCOUNT_PASSWORD)
        iam.users.set_password(user.id, password=password, temporary=False)
    elif (
        user_info_by_email.required_actions
        and "UPDATE_PASSWORD" in user_info_by_email.required_actions
    ):
        logging.info("updating password.....")
        get_user_credentials = iam.users.get_user_credentials(user_info_by_email.id)
        logging.info(f"get_user_credentials:{get_user_credentials}")
        for user_credential in get_user_credentials:
            iam.users.remove_user_credential(user_info_by_email.id, user_credential.id)
        password = SecretStr(settings.SERVICE_ACCOUNT_PASSWORD)
        logging.info(f"Password:{password}")
        iam.users.set_password(
            user_info_by_email.id, password=password, temporary=False
        )
    logging.info("validated user details.....")
    copy_user_id = user.id if not user_info_by_email else user_info_by_email.id
    return copy_email, copy_user_id


def generate_access_token(
    iam: AbstractIAM,
    *,
    user_info: schemas.UserInfo,
    target_org,
) -> TokenResponse:
    email, copy_user_id = create_validate_user(
        iam,
        user_info=user_info,
        target_org=target_org,
    )
    password = settings.SERVICE_ACCOUNT_PASSWORD
    iam.users.revoke_token(copy_user_id)
    token_request = TokenRequest(username=email, password=password)
    token = iam.users.get_token(token_request)
    return token
