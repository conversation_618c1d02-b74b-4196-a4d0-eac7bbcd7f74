import csv
import io
from pathlib import Path
from typing import Any, Dict, List, Set, Tuple, cast

from email_validator import EmailNotValidError  # type: ignore
from email_validator import validate_email as default_email_validator
from fastapi import HTTPException, UploadFile
from starlette import status

from app.constants import EXPECTED_HEADERS_IN_CSV, MAX_LENGTH_FOR_DB, MAX_NAME_LENGTH


class CSV:
    """
    CSV file validator that performs various checks on uploaded CSV files.

    Validations:
    - File extension (.csv)
    - File size limit
    - Empty file check
    - Header validation (presence, order, names)
    - Column count
    """

    def __init__(
        self,
        file: UploadFile,
        max_size_mb: float = 5.0,
    ):
        """
        Initialize CSV validator with file and size limit.

        Args:
            file: The uploaded file
            max_size_mb: Maximum allowed file size in MB (default: 5MB)
            expected_headers: List of expected headers in correct order
        """
        self.file = file
        self.max_size_mb = max_size_mb
        self.content = self.file.file.read()
        self.headers: List[str] | None = None
        self.rows: List[List[str]] | None = None
        self.row_count = 0
        self.expected_headers = EXPECTED_HEADERS_IN_CSV

        self._validate_extension()

    def validate(self) -> Dict[str, Any]:
        self._validate_size()
        self._validate_not_empty()

        try:
            try:
                text_content = self.content.decode("utf-8-sig")
            except UnicodeDecodeError:
                text_content = self.content.decode("utf-8")

            csv_file = io.StringIO(text_content)
            csv_reader = csv.reader(csv_file)

            self.headers = cast(List[str], next(csv_reader, []))
            self._validate_headers_exist()

            if self.expected_headers:
                self._validate_column_count(len(self.expected_headers))
                self._validate_headers_match(self.expected_headers)

            csv_file.seek(0)

            self.file.file.seek(0)

            return {"content": text_content}

        except UnicodeDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File is not a valid CSV file (encoding error)",
            )

    def _validate_extension(self) -> None:
        """Validate file has .csv extension"""
        file_extension = Path(self.file.filename or "").suffix.lower()
        if file_extension != ".csv":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file format. Please upload a .csv file.",
            )

    def _validate_size(self) -> None:
        """Validate file size is within limit"""
        file_size_mb = len(self.content) / (1024 * 1024)
        if file_size_mb > self.max_size_mb:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=(f"File size exceeds {self.max_size_mb}MB limit."),
            )

    def _validate_not_empty(self) -> None:
        """Validate file is not empty"""
        if not self.content.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="The uploaded CSV file is empty.",
            )

    def _validate_headers_exist(self) -> None:
        """Validate CSV has headers"""
        if not self.headers:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="CSV file has no headers",
            )

    def _validate_column_count(self, expected_count: int) -> None:
        """Validate CSV has correct number of columns"""
        if not self.headers:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="CSV file has no headers",
            )

        if len(self.headers) != expected_count:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=("CSV column should have exactly 3 headers."),
            )

    def _validate_headers_match(self, expected_headers: List[str]) -> None:
        """
        Validate CSV headers match expected headers.

        Args:
            expected_headers: List of expected headers in correct order
        """
        if not self.headers:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="CSV file has no headers",
            )

        normalized_headers = [header.strip().lower() for header in self.headers]

        if normalized_headers != expected_headers:
            if set(normalized_headers) == set(expected_headers):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=(
                        "Headers must be in the order: First Name, Last Name, Email."
                    ),
                )

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=(
                    "Invalid headers. Headers should be First Name, Last Name, Email."
                ),
            )


def validate_name(name: str, field_name: str) -> str | None:
    if not name:
        return f"Missing value in '{field_name.title()}' column"
    if len(name) > MAX_NAME_LENGTH:
        return f"{field_name} exceeds {MAX_NAME_LENGTH} characters."
    return None


def is_valid_email(email: str) -> bool:
    try:
        default_email_validator(email, check_deliverability=False)
        return True
    except EmailNotValidError:
        return False


def validate_email(email: str, emails_set: Set[str]) -> str | None:
    if not email:
        return "Missing value in 'Email' column."
    else:
        if not is_valid_email(email):
            return f"Invalid email format {email}"

        if email.lower() in emails_set:
            return "Duplicate email in file"
        else:
            emails_set.add(email.lower())
            return None


def format_name_for_db(name: str) -> str:
    return name.strip()[:MAX_LENGTH_FOR_DB] + "..."


def validate_csv_content(
    file_content: bytes,
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Validate CSV content and return valid rows and validation errors.

    Args:
        file_content: The CSV file content as a string
    Returns:
        Tuple containing:
        - List of valid rows as dictionaries
        - List of validation error messages
    """

    csv_reader = csv.reader(io.StringIO(file_content.decode("utf-8")))
    rows = list(csv_reader)
    emails_set: set[str] = set()
    valid_rows = []
    validation_errors = []
    for row in rows[1:]:
        first_name = row[0].strip() if len(row) > 0 else "-"
        last_name = row[1].strip() if len(row) > 1 else "-"
        email = row[2].strip() if len(row) > 2 else "-"

        if len(row) < 3:
            validation_errors.append(
                {
                    "first_name": first_name,
                    "last_name": last_name,
                    "email": email,
                    "error": "One or more fields are empty",
                }
            )
            continue

        row_dict = {
            "first_name": format_name_for_db(first_name)
            if len(first_name) > MAX_NAME_LENGTH
            else first_name,
            "last_name": format_name_for_db(last_name)
            if len(last_name) > MAX_NAME_LENGTH
            else last_name,
            "email": format_name_for_db(email)
            if len(email) > MAX_NAME_LENGTH
            else email.lower(),
        }

        first_name_errors = validate_name(first_name, "First name")
        if first_name_errors:
            row_dict["error"] = first_name_errors
            validation_errors.append(row_dict)
            continue

        last_name_errors = validate_name(last_name, "Last name")
        if last_name_errors:
            row_dict["error"] = last_name_errors
            validation_errors.append(row_dict)
            continue

        email_errors = validate_email(email.lower(), emails_set)
        if email_errors:
            row_dict["error"] = email_errors
            validation_errors.append(row_dict)
            continue

        valid_rows.append(row_dict)

    return valid_rows, validation_errors
