import logging
from typing import Optional, overload
from uuid import UUID

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pydantic import <PERSON><PERSON><PERSON><PERSON>
from starlette import status

from app import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import GroupAlreadyExists, UserAlreadyExist
from app.adapters.iam.schemas import <PERSON><PERSON><PERSON><PERSON>, User, UserCreate, UserUpdate
from app.adapters.platform_api_client.audit_service import Abstract<PERSON><PERSON>t<PERSON><PERSON>
from app.adapters.platform_api_client.schemas import AuditResponse
from app.common.pagination import InvalidPage, PaginatedResponse, Pagination
from app.constants import DEFAULT_ROLES, OFFLINE_ACCESS
from app.core.utils import get_object, get_object_or_404
from app.db.redis_service import RedisService
from app.enums import OrganizationRoleEnum, OrganizationScopeEnum
from app.models import Organization
from app.oso import oso
from app.schemas.mail import EmailMessage
from app.services import authz, org_management
from app.services.authz_password import render_password_email
from app.services.mappers import alias_to_identity_provider

ERR_BAD_ROLE_FOR_ORG_TYPE = (
    'Bad role for organization of type "{org_type}": "{role_name}"'
)


logger = logging.getLogger(__name__)

ERR_USER_IS_A_MEMBER_WITH_EMAIL = (
    "The user with the email address {email}"
    " is already a member of another organization."
)
ERR_USER_IS_A_MEMBER_WITH_EMAIL_AND_ORG_NAME = (
    "The user with the email address {email}"
    " is already a member of the {org_name}"
    " organization."
)

ERR_FOR_MORE_THAN_ONE_DEFAULT_ROLE = "There should be exactly one default role."
MAXIMUM_NUMBER_OF_DEFAULT_ROLE = 1


def get_by_email(iam, user_in) -> User | None:
    try:
        return iam.users.get_by_email(user_in.email)
    except Exception as e:
        logger.error("error_in_get_by_email", str(e))
        raise


def get_user_org(db, iam, user_in, user, actor):
    try:
        existing_org = org_management.get_user_org(db, iam, user_id=user.id)
        if not existing_org:
            logger.warning(msg="User exists without Organization, adding user details")
            return
        elif oso.is_allowed(actor, OrganizationScopeEnum.READ, existing_org):
            error_msg = ERR_USER_IS_A_MEMBER_WITH_EMAIL_AND_ORG_NAME.format(
                org_name=existing_org.name, email=user_in.email
            )
            raise HTTPException(status.HTTP_409_CONFLICT, error_msg)
        else:
            error_msg = ERR_USER_IS_A_MEMBER_WITH_EMAIL.format(email=user_in.email)
            raise HTTPException(status.HTTP_409_CONFLICT, error_msg)
    except Exception as e:
        logger.error(f"Error in get_user_org {e}", exc_info=True)
        raise


def add_user_in_to_organization(iam, target_org, user, user_in):
    try:
        if user:
            org_management.add_user_to_organization_v2(
                iam,
                organization=target_org,
                user_id=user.id,
                organization_role=user_in.roles,
            )
    except GroupAlreadyExists as e:
        logger.error(f"add_user_error {e}", exc_info=True)
        raise HTTPException(status.HTTP_404_NOT_FOUND, "error in add user into org")


def get_user_organization_roles(iam, user) -> list[OrganizationRoleEnum]:
    try:
        return org_management.fetch_user_org_roles(iam, user.id)
    except Exception as e:
        logger.error(str(e))
        raise


def get_user_v2(user, target_org) -> schemas.UserV2:
    try:
        user_v2 = schemas.UserV2(
            **user.dict(),
            organization=schemas.OrganizationSimple.from_orm(target_org),
        )
        return user_v2
    except Exception as e:
        logger.error(str(e), exc_info=True)
        raise HTTPException(status.HTTP_404_NOT_FOUND, "error in get user v2")


def add_contact(core_api, user_v2) -> None:
    try:
        core_api.add_contact(user_v2)
    except Exception as e:
        logger.error("add_contact_error", str(e))


def get_keycloak_user_response(user, roles, user_idp) -> schemas.KeycloakUser:
    try:
        return schemas.KeycloakUser(
            id=user.id,
            email=user.email,
            username=user.username,
            firstName=user.first_name,
            lastName=user.last_name,
            enabled=user.enabled,
            emailVerified=user.email_verified,
            createdTimestamp=user.created_timestamp,
            roles=roles,
            identityProvider=user_idp,
        )
    except Exception as e:
        logger.error(f"error_get_response {e}", exc_info=True)
        raise HTTPException(status.HTTP_404_NOT_FOUND, "error in get user response")


def _render_password_email(user, password) -> EmailMessage:
    try:
        message = render_password_email(user, password)
        return message
    except Exception as e:
        logger.error(f"Unable to send an email: {e}")
        raise


def send_email(iam, mail_api, user):
    try:
        password = authz.generate_password(iam, email=user.email)
        iam.users.set_password(user.id, password=password, temporary=True)
        message = _render_password_email(user, password)
        mail_api.send(message)

    except Exception as e:
        logger.error(f"error_send_email {e}", exc_info=True)
        raise HTTPException(status.HTTP_409_CONFLICT, "error_send_email")


def alias_to_user_identity_provider(
    iam, user
) -> schemas.KeycloakIdentityProvider | None:
    return alias_to_identity_provider(iam, user.identity_provider)


def validate_user_in(db, actor, org_id, user_in):
    target_org = get_object_or_404(db, Organization, False, id=org_id)
    try:
        oso.authorize(actor, OrganizationScopeEnum.UPDATE, target_org)
        default_roles = [role.value for role in OrganizationRoleEnum]
        default_user_role = set(user_in.roles) & set(default_roles)
        if len(default_user_role) != MAXIMUM_NUMBER_OF_DEFAULT_ROLE:
            raise HTTPException(
                status.HTTP_400_BAD_REQUEST, ERR_FOR_MORE_THAN_ONE_DEFAULT_ROLE
            )
        check_org_role(target_org, list(default_user_role)[0])
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    return target_org


def check_org_role(
    organization: Organization,
    role: OrganizationRoleEnum,
) -> None:
    if role not in organization.type.roles:
        error_msg = ERR_BAD_ROLE_FOR_ORG_TYPE.format(
            role_name=role.value,
            org_type=organization.type.value,
        )
        raise HTTPException(status.HTTP_400_BAD_REQUEST, error_msg)


def validate_user_identity_provider(iam, user_in, user) -> None:
    logging.info(f"Validating user identity provider: {user_in}:{user}")
    identity_provider = validate_identity_provider(
        iam,
        user_in.identityProvider,
    )
    user.set_identity_provider(identity_provider.alias)
    logging.info(f"Updating user: {user.to_update()}")
    iam.users.update(user.to_update())


def validate_identity_provider(
    iam: AbstractIAM,
    idp_alias: str,
) -> IdentityProvider:
    """
    Checks whether provided idp exists in IAM service
    """
    idp = iam.identity_providers.get_by_alias(idp_alias)
    if idp is None:
        raise HTTPException(
            detail=f"Bad identity provider: {idp_alias}",
            status_code=status.HTTP_400_BAD_REQUEST,
        )
    return idp


def add_user(
    db,
    iam: AbstractIAM,
    actor,
    user_in: schemas.UserCreateV2,
    target_org: Organization,
):
    logging.info("In add_user")
    existing_user = get_by_email(iam, user_in)
    if existing_user:
        get_user_org(db, iam, user_in, existing_user, actor)
    else:
        user_schema = UserCreate(
            username=user_in.username,
            email=user_in.email,
            email_verified=user_in.emailVerified,
            enabled=user_in.enabled,
            first_name=user_in.firstName,
            last_name=user_in.lastName,
        )
        created_user = iam.users.add(user_schema)
    user = existing_user if existing_user else created_user
    try:
        if user_in.identityProvider:
            validate_user_identity_provider(iam, user_in, user)
        add_user_in_to_organization(iam, target_org, user, user_in)

        user_idp = alias_to_user_identity_provider(iam, user)
        return user, user_idp
    except UserAlreadyExist as e:
        raise e
    except Exception as e:
        raise e


def fetch_user_roles(iam, user_id, user_data):
    user_roles = iam.users.get_user_role_details(user_id)
    roles = [
        user_roles[role]
        for role in user_data.get("roles", [])
        if user_roles[role] != OFFLINE_ACCESS
        and not user_roles[role].startswith(DEFAULT_ROLES)
    ]
    return roles


def map_user_details(
    user_id: UUID4,
    user_data: dict,
    user_organization: Organization | None,
    idp: schemas.KeycloakIdentityProvider | None,
    roles: list[str] | None,
    user_log: Optional[AuditResponse],
) -> schemas.KeyCloakUserDetails:
    organization = {}
    if user_organization:
        logging.info(f"user_id:{user_id}")
        logging.info(f"user_organization:{user_organization}")
        organization = {
            "id": user_organization.id,
            "name": user_organization.name,
        }
    return schemas.KeyCloakUserDetails(
        id=user_id,
        email=user_data.get("email"),
        username=user_data.get("username", " "),
        emailVerified=user_data.get("emailVerified", False),
        lastLoginTimestamp=user_data.get("lastLogin"),
        firstName=user_data.get("firstName"),
        lastName=user_data.get("lastName"),
        enabled=user_data.get("enabled", False),
        createdTimestamp=user_data.get("timestamp", 0),
        organization=organization if organization else None,
        identityProvider=idp,
        roles=roles,
        createdBy=user_log.user if user_log else None,
    )


@overload
def fetch_all_users(
    db,
    iam: AbstractIAM,
    pagination: Pagination,
    search,
    ordering,
    audit_service: AbstractAuditAPI,
) -> PaginatedResponse:
    ...


@overload
def fetch_all_users(
    db,
    iam: AbstractIAM,
    pagination: None,
    search,
    ordering,
    audit_service: AbstractAuditAPI,
) -> list[schemas.KeyCloakUserDetails]:
    ...


def fetch_all_users(
    db,
    iam: AbstractIAM,
    pagination: Pagination | None,
    search,
    ordering,
    audit_service: AbstractAuditAPI,
) -> PaginatedResponse | list[schemas.KeyCloakUserDetails]:
    users_data = []
    redis_service = RedisService()
    users, total_users = redis_service.get_all_users(
        search_value=search,
        ordering=ordering,
        pagination=pagination,
    )
    if users:
        for user_id, data in users.items():
            user_organization = None
            user_organization_id = data.get("groups", None)
            if user_organization_id:
                logger.info(f"users: {data} {user_organization_id}")
                user_organization = get_object(
                    db, Organization, False, external_id=UUID(user_organization_id)
                )
            idp_alias = (
                data.get("attributes").get("identityProvider")
                if data.get("attributes")
                else None
            )
            idp = alias_to_identity_provider(iam, idp_alias[0]) if idp_alias else None
            roles = fetch_user_roles(iam, user_id, data)
            user_log = audit_service.get_audit(user_id, event="POST")
            users_data.append(
                map_user_details(
                    UUID4(user_id), data, user_organization, idp, roles, user_log
                )
            )
    if not pagination:
        return users_data
    try:
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=users_data,
            totalCount=total_users,
        )
    except InvalidPage as e:
        raise e


def fetch_organization_users(
    iam: AbstractIAM,
    organization: Organization,
    pagination: Pagination | None,
    search,
    ordering,
    audit_service: AbstractAuditAPI,
):
    redis_service = RedisService()
    group_users = None
    if organization.external_id:
        group_users = redis_service.get_group_users(org_id=organization.external_id)
    if not group_users:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Users not found for {organization.name}",
        )
    users_data = []
    users, total_users = redis_service.get_all_users(
        user_ids=list(group_users),
        pagination=pagination,
        search_value=search,
        ordering=ordering,
    )
    for user_id, data in users.items():
        idp_alias = (
            data.get("attributes").get("identityProvider")
            if data.get("attributes")
            else None
        )
        roles = fetch_user_roles(iam, user_id, data)
        idp = alias_to_identity_provider(iam, idp_alias[0]) if idp_alias else None
        user_log = audit_service.get_audit(user_id, event="POST")
        users_data.append(
            map_user_details(UUID4(user_id), data, organization, idp, roles, user_log)
        )

    if pagination:
        try:
            return PaginatedResponse.from_iterable(
                pagination=pagination,
                results=users_data,
                totalCount=total_users,
            )
        except InvalidPage as e:
            raise e
            return PaginatedResponse.from_iterable(
                pagination=pagination,
                results=users_data,
                totalCount=total_users,
            )
    return users_data


def remove_user_roles(iam, user_id: UUID4, user_data):
    user_roles = fetch_user_roles(iam, user_id, user_data)
    iam.users.remove_roles(user_id, user_roles)


def update_user(iam, redis_service, user_in, user_id, redis_user):
    user = iam.users.get_by_id(user_id)
    remove_user_roles(iam, user_id, redis_user)
    iam.users.add_roles(user_id, user_in.roles)

    user_update_details = UserUpdate(
        id=user.id,
        last_name=user_in.lastName,
        first_name=user_in.firstName,
        email=user.email,
        email_verified=user.email_verified,
        enabled=user_in.enabled,
        username=user.username,
    )
    iam.users.update(user_update_details)
    updated_user = iam.users.get_by_id(user_id)
    if user_in.identityProvider is None:
        # `identityProvider` was passed with `null` value
        user.remove_identity_provider()
    else:
        idp = validate_identity_provider(iam, user_in.identityProvider)
        updated_user.set_identity_provider(idp.alias)
        iam.users.update(updated_user.to_update())

    # Fetch updated user and roles for response
    user_idp = alias_to_user_identity_provider(iam, updated_user)
    updated_user_data = redis_service.get_user(user_id=user_id)
    roles = fetch_user_roles(iam, user_id, updated_user_data)
    response = get_keycloak_user_response(updated_user, roles, user_idp)
    return response


def fetch_organization_user(
    iam: AbstractIAM,
    organization: Organization,
    audit_service: AbstractAuditAPI,
    user_id: UUID,
    user_data: dict,
) -> schemas.KeyCloakUserDetails:
    attrs = user_data.get("attributes")
    if attrs is None:
        idp_alias = None
    else:
        idp_alias = attrs.get("identityProvider")
    roles = fetch_user_roles(iam, user_id, user_data)
    idp = alias_to_identity_provider(iam, idp_alias[0]) if idp_alias else None
    user_log = audit_service.get_audit(str(user_id), event="POST")
    user_details = map_user_details(
        user_id, user_data, organization, idp, roles, user_log
    )
    return user_details
