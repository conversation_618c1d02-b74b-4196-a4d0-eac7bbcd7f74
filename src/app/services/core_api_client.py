import logging

import httpx
from fastapi import HTTPException
from httpx import Headers
from starlette import status

from app.core.config import settings

BAD_RESPONSE_FMT = (
    'URL:"{response.url}", '
    'Status:"{response.status_code}", '
    'Response:"{response.content}"'
)
BAD_AUTH_STATUS_CODES = [
    status.HTTP_401_UNAUTHORIZED,
    status.HTTP_403_FORBIDDEN,
]


class ExternalUserAPIClient(httpx.Client):
    """HTTP client for making requests on behalf of the user/service"""

    @classmethod
    def from_token(cls, token: str):
        headers = {"Authorization": f"Bearer {token}"}
        return cls(headers=headers)


class InternalAPIClient(httpx.Client):
    """HTTP client for making requests using own credentials"""

    @classmethod
    def create(cls) -> "InternalAPIClient":
        client = cls()
        data = dict(
            grant_type="client_credentials",
            client_id=settings.OIDC_CLIENT_ID,
            client_secret=settings.OIDC_CLIENT_SECRET,
        )
        try:
            response = client.post(str(settings.OIDC_TOKEN_URL), data=data)
        except Exception as e:
            logging.warning(f"Cannot retrieve access token for internal client: {e}")
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR)

        if response.is_error:
            msg = "Cannot retrieve access token."
            logging.warning(msg + BAD_RESPONSE_FMT.format(response=response))
            client.close()
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR)
        payload = response.json()
        token = payload["access_token"]
        client.headers = Headers({"Authorization": f"Bearer {token}"})
        return client
