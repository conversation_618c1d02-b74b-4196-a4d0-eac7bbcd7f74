import base64
import logging
from typing import Any, Final
from uuid import UUID

import jwt
from pydantic import AnyHttpUrl, EmailStr, SecretStr, parse_obj_as
from sqlalchemy.orm import Session
from yarl import URL

from app import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.schemas import User
from app.core.config import settings
from app.services import org_management
from app.services.authz_password import BasePasswordPolicy, PasswordGenerator

logger = logging.getLogger(__name__)


class BaseAuthZException(Exception):
    ...


class ResourceNotFound(BaseAuthZException):
    ...


def pre_authorize_url(
    iam: AbstractIAM,
    db: Session,
    ctx: schemas.PreAuthContext,
) -> AnyHttpUrl:
    """
    Generates a redirect URL based on the email provided and the user's status.
    """
    try:
        action_token_url = get_action_token_url(ctx.oauth_query)
    except ValueError as e:
        if settings.DEBUG:
            logger.debug(f"Detailed exception: {e}")
        logger.warning("Pre-auth failed, error getting action token URL:")
        return settings.LANDING_PAGE_URL

    action_token = URL(action_token_url).query["key"]
    try:
        payload = PreAuthTokenCodec(settings.PRE_AUTH_TOKEN_SECRET).decode(
            action_token, verify_signature=False
        )
    except ValueError as e:
        if settings.DEBUG:
            logger.debug(f"Detailed exception: {e}")

        logger.warning("Pre-auth failed, cannot decode action token URL:")
        return settings.LANDING_PAGE_URL

    email: str | None = payload.get("eml", None)
    if not email:
        logger.warning("Pre-auth: user email not provided in action token")
        return settings.LANDING_PAGE_URL

    user = get_active_user(iam, db, email)
    if user:
        idp_hint = resolve_user_identity_provider(iam, user)
        login_hint = transform_login_hint(email, idp_hint)
        return build_auth_url_for_user(
            user_id=user.id,
            idp_hint=idp_hint,
            login_hint=login_hint,
            action_token_url=action_token_url,
        )
    else:
        return build_external_auth_url(
            login_hint=transform_login_hint(email, settings.EXTERNAL_IDP),
            idp_hint=settings.EXTERNAL_IDP,
            redirect_url=settings.EXTERNAL_LANDING_PAGE,
        )


def transform_login_hint(email: str, idp_hint: str | None) -> str:
    login_hint = email
    if idp_hint == settings.EXTERNAL_IDP:
        suffix = f"@{settings.EXTERNAL_IDP_DOMAIN_TO_REMOVE}".lower()
        login_hint = email.lower().removesuffix(suffix)
    return login_hint


def get_active_user(iam: AbstractIAM, db: Session, email: str) -> User | None:
    user = iam.users.get_by_email(EmailStr(email))
    if not (user and user.enabled and user.email_verified):
        return None

    organization = org_management.get_user_org(db, iam, user_id=user.id)
    if not organization:
        return None

    return user


def resolve_user_identity_provider(iam: AbstractIAM, user: User) -> str | None:
    if user.identity_provider:
        # Use identity provider from the user's attribute
        return user.identity_provider
    elif user.federated_identities:
        # Fallback to the first configured IdP in Keycloak
        provider = user.federated_identities[0].identity_provider

        # Update attribute for the future
        user.set_identity_provider(provider)
        iam.users.update(user.to_update())
        return provider

    return None


def build_auth_url_for_user(
    user_id: UUID,
    login_hint: str | None,
    idp_hint: str | None,
    action_token_url: AnyHttpUrl,
) -> AnyHttpUrl:
    # An app token to pass to the action token endpoint in Keycloak
    payload = dict(sub=str(user_id))
    if idp_hint:
        payload.update(kc_idp_hint=idp_hint)
    if login_hint:
        payload.update(login_hint=login_hint)

    app_token = PreAuthTokenCodec.from_b64_secret(
        settings.PRE_AUTH_TOKEN_SECRET
    ).encode(payload)

    login_url = URL(action_token_url).update_query({"app-token": app_token})
    return parse_obj_as(AnyHttpUrl, str(login_url))


def build_external_auth_url(
    login_hint: str,
    idp_hint: str = settings.EXTERNAL_IDP,
    redirect_url: AnyHttpUrl = settings.EXTERNAL_LANDING_PAGE,
) -> AnyHttpUrl:
    url = URL(settings.KEYCLOAK_URL).origin() / "auth-extension/login"
    url = url.update_query(
        login_hint=login_hint, kc_idp_hint=idp_hint, redirect_url=redirect_url
    )
    return parse_obj_as(AnyHttpUrl, str(url))


def get_action_token_url(params: dict[str, str]) -> AnyHttpUrl:
    """
    Extracts the action token URL from URL parameters, passed
    by the custom Keycloak authenticator to redict to after pre-authorization.
    See for details:
     https://www.keycloak.org/docs/15.0/server_development/#_action_token_spi
     https://gitlabnv2.flyaps.com/nv2/infra/keycloak/-/merge_requests/6
    """
    action_token_url = params.get("token")
    if not action_token_url:
        raise ValueError('Missing "token" parameter.')
    url = parse_obj_as(AnyHttpUrl, action_token_url)
    key = URL(url).query.get("key")
    if not key:
        raise ValueError('Bad action token URL, missing "key" parameter.')
    return url


class PreAuthTokenCodec:
    ALGORITHM: Final[str] = "HS256"

    def __init__(self, secret: str | None = None):
        if not secret:
            raise ValueError("Secret must be provided.")
        self.secret: str = secret

    def encode(self, payload: dict[str, Any]) -> str:
        token = jwt.encode(payload, self.secret, algorithm=self.ALGORITHM)  # nosemgrep
        # we are adding nosemgrep since it doesnt leak sensitive data in token
        return token

    def decode(self, token: str, verify_signature: bool = True) -> dict[str, Any]:
        try:
            payload = jwt.decode(
                token,
                self.secret,
                algorithms=[self.ALGORITHM],
                options=dict(verify_signature=verify_signature),
            )
        except jwt.DecodeError as e:
            raise ValueError(str(e))
        return payload

    @classmethod
    def from_b64_secret(cls, secret_b64: str) -> "PreAuthTokenCodec":
        secret = base64.b64decode(secret_b64).decode()
        return cls(secret)


def get_password_policies(iam: AbstractIAM, **context: Any) -> list[BasePasswordPolicy]:
    policies = []
    for iam_policy in iam.password_policies.get_current():
        policy = BasePasswordPolicy.create(iam_policy.id, iam_policy.value, **context)
        policies.append(policy)
    return policies


def generate_password(iam: AbstractIAM, **context: Any) -> SecretStr:
    policies = get_password_policies(iam, **context)
    generate = PasswordGenerator(policies=policies)
    password = generate()
    return password
