import json
import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import redis
from pydantic import UUID4
from redis.exceptions import RedisError

from app.common.ordering import OrderDirection, Ordering
from app.common.pagination import Pagination
from app.core.config import settings


class RedisService:
    """Service for interacting with Redis hash maps"""

    def __init__(self):
        """
        Initialize Redis connection
        """

        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            # db=settings.REDIS_DB,
            decode_responses=False,
            socket_connect_timeout=5,
            socket_timeout=5,
        )

    def _try_json_loads(self, value: bytes) -> Any:
        """Safely attempt to JSON decode a value"""
        try:
            return json.loads(value.decode("utf-8"))
        except (json.JSONDecodeError, UnicodeDecodeError):
            return value.decode("utf-8")

    def _deserialize(self, data: Union[bytes, None, Dict[bytes, bytes]]) -> Any:
        """deserialization for Redis"""
        if data is None:
            return None

        if isinstance(data, set):  # List of values case
            return {item.decode("utf-8") for item in data}

        if isinstance(data, dict):  # hgetall case
            return {k.decode("utf-8"): self._try_json_loads(v) for k, v in data.items()}

        return self._try_json_loads(data)

    def get_user(self, user_id: UUID4) -> Optional[Dict[str, Any]]:
        """
        Retrieve user data from the 'users' hash
        """
        try:
            user_data = self.redis_client.hgetall(f"users:{user_id}")
            return self._deserialize(user_data) if user_data else None
        except RedisError as e:
            logging.error(f"Error getting user: {e}")
            return None
        except RedisError:
            return None

    def get_sort_key(self, item, redis_field, reverse: bool):
        value = item[1].get(redis_field)
        if value is None:
            return (2, "")

        if redis_field in {"createdTimestamp", "lastLogin", "timestamp"}:
            try:
                num_value = float(value)
                return (0, -num_value if reverse else num_value)
            except Exception:
                return (2, "")

        try:
            normalized_value = str(value).lower()
            if reverse:
                normalized_value = "".join(chr(255 - ord(c)) for c in normalized_value)
            return (1, normalized_value)
        except Exception:
            return (2, "")

    def get_all_users(
        self,
        ordering: Ordering,
        pagination: Pagination | None = None,
        user_ids: Optional[List[str]] = None,
        search_value: Optional[str] = None,
    ) -> Tuple[Dict[str, Any], int]:
        """
        Get users with pagination, searching, and ordering capabilities.
        """
        try:
            # Get all relevant user keys
            if user_ids is None:
                user_keys = self.redis_client.keys("users:*")
                if not user_keys:
                    return {}, 0
            else:
                user_keys = [f"users:{user_id}" for user_id in user_ids]

            # Fetch all user data in a pipeline
            with self.redis_client.pipeline() as pipe:
                for key in user_keys:
                    pipe.hgetall(key)
                all_raw_users = pipe.execute()

            # Deserialize and process users
            users = {}
            sorted_users: Dict[str, Any] = {}

            for key, user_data in zip(user_keys, all_raw_users):
                user_id = (
                    key.decode("utf-8").split(":")[1]
                    if isinstance(key, bytes)
                    else key.split(":")[1]
                )
                users[user_id] = self._deserialize_hash(user_data)
            # Searching
            if search_value:
                search_lower = search_value.lower()
                users = {
                    uid: user
                    for uid, user in users.items()
                    if any(
                        isinstance(val, str)
                        and search_lower in val.lower()
                        or isinstance(val, (int, float))
                        and str(val) == search_value
                        for val in user.values()
                    )
                }
            if ordering:
                field_mapping = {
                    "email": "email",
                    "firstName": "firstName",
                    "createdTimestamp": "timestamp",
                    "lastName": "lastName",
                    "lastLoginTimestamp": "lastLogin",
                }
                redis_field = field_mapping.get(ordering.field, ordering.field)
                reverse = True if (ordering.order == OrderDirection.DESC) else False
                logging.info(f"users list before sorting {users}")
                sorted_items = sorted(
                    users.items(),
                    key=lambda item: self.get_sort_key(item, redis_field, reverse),
                    reverse=False,
                )
                sorted_users = dict(sorted_items)
            total_users = len(sorted_users)
            if pagination:
                start_idx = (pagination.page - 1) * pagination.page_size
                end_idx = start_idx + pagination.page_size
                paginated_users = dict(
                    list(dict(sorted_users).items())[start_idx:end_idx]
                )
                logging.info(
                    f"Fetched {len(paginated_users)}/{total_users} "
                    f"users (page {pagination.page})"
                )
                return paginated_users, total_users
            return dict(list(dict(users).items())), total_users

        except RedisError as e:
            logging.error(f"Error fetching users: {e}")
            return {}, 0

    def get_group_users(self, org_id: UUID4) -> Dict[str, Dict[str, Any]]:
        """
        Get list of users for a group
        """
        try:
            group_users = self.redis_client.smembers(f"groups:{org_id}")
            return self._deserialize(group_users) if group_users else None
        except RedisError as e:
            logging.error(f"Error fetching group members: {e}")
            return {}

    def _deserialize_hash(self, hash_data: Dict[bytes, bytes]) -> Dict[str, Any]:
        """Deserialize a single user hash"""
        result = {}
        for field, value in hash_data.items():
            field_name = field.decode("utf-8")
            try:
                result[field_name] = json.loads(value.decode("utf-8"))
            except json.JSONDecodeError:
                result[field_name] = value.decode("utf-8")
        return result
