import os

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

if os.environ.get("DEBUG") == "1":
    import logging

    logging.basicConfig(format="%(message)s\n")
    logger = logging.getLogger("sqlalchemy.engine")
    logger.setLevel(logging.INFO)


engine = create_engine(str(settings.DATABASE_URI), pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
