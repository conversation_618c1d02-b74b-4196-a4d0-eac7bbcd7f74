import datetime
import uuid
from typing import Any

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.engine import Dialect
from sqlalchemy.types import CHAR, TypeDecorator


# Copy/pasted from
# https://docs.sqlalchemy.org/en/14/core/custom_types.html?highlight=guid#backend-agnostic-guid-type
class GUID(TypeDecorator):  # type: ignore[type-arg]
    """Platform-independent GUID type.

    Uses PostgreSQL's UUID type, otherwise uses
    CHAR(32), storing as stringified hex values.

    """

    impl = CHAR
    cache_ok = True

    def load_dialect_impl(self, dialect: Dialect) -> Any:
        if dialect.name == "postgresql":
            return dialect.type_descriptor(UUID())  # type: ignore[arg-type]
        else:
            return dialect.type_descriptor(CHAR(32))  # type: ignore[arg-type]

    def process_bind_param(
        self, value: str | uuid.UUID | None, dialect: Dialect
    ) -> str | None:
        if value is None:
            return value
        elif dialect.name == "postgresql":
            return str(value)
        else:
            if not isinstance(value, uuid.UUID):
                return "%.32x" % uuid.UUID(value).int
            else:
                # hexstring
                return "%.32x" % value.int

    def process_result_value(
        self, value: str | uuid.UUID | None, dialect: Dialect
    ) -> uuid.UUID | None:
        if value is None:
            return value
        else:
            if not isinstance(value, uuid.UUID):
                value = uuid.UUID(value)
            return value


# https://docs.sqlalchemy.org/en/14/core/custom_types.html?highlight=utc#store-timezone-aware-timestamps-as-timezone-naive-utc
class TZTimestamp(TypeDecorator):  # type: ignore[type-arg]
    """Store Timezone Aware Timestamps as Timezone Naive UTC"""

    impl = TIMESTAMP
    cache_ok = True

    def process_bind_param(
        self, value: datetime.datetime | None, dialect: Dialect
    ) -> datetime.datetime | None:
        if value is not None:
            if not value.tzinfo:
                raise TypeError("tzinfo is required")
            value = value.astimezone(datetime.timezone.utc).replace(tzinfo=None)
        return value

    def process_result_value(
        self, value: datetime.datetime | None, dialect: Dialect
    ) -> datetime.datetime | None:
        if value is not None:
            value = value.replace(tzinfo=datetime.timezone.utc)
        return value
