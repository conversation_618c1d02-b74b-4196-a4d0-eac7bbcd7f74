import json
import logging
import uuid
from abc import ABC, abstractmethod

from platform_api_client import PlatformAPIClient, PlatformAPIError
from pydantic import parse_obj_as

from app.adapters.platform_api_client.schemas import (
    AuditLogModel,
    AuditLogResponseModel,
    AuditResponse,
)

logger = logging.getLogger(__name__)


class AbstractAuditAPI(ABC):
    @abstractmethod
    def get_audit(self, id: str | int, event: str) -> AuditResponse | None:
        ...

    @abstractmethod
    def add_audit(self, message) -> AuditLogResponseModel | None:
        ...


class AuditAPI(AbstractAuditAPI):
    """Implements audt management API using HTTP PlatformAPIClient"""

    ADD_AUDIT = "/v1/auditlog/log"
    GET_AUDIT = "/v1/auditlog/log"

    def __init__(
        self,
        client: PlatformAPIClient,
        base_url: str,
    ) -> None:
        self._client = client
        self._client.set_base_url(base_url)

    def get_audit(self, id: str | int, event: str) -> AuditResponse | None:
        try:
            headers = {"X-trace-id": str(uuid.uuid4())}
            params = {"payloadField": "id", "payloadValue": id, "event": event}
            response = self._client.get(
                url=self.GET_AUDIT, headers=headers, params=params
            )
            logging.info(f"Audit fetched successfully {response.json()}")
            audit = parse_obj_as(AuditResponse, response.json()["results"][0])
            return audit
        except PlatformAPIError as e:
            logger.error(f"Error fetching the audit logs: {e}")
            return None
        except Exception as e:
            logger.error(f"Error fetching the audit logs: {e}")
            return None

    def add_audit(self, message: AuditLogModel) -> AuditLogResponseModel | None:
        try:
            headers = {"X-trace-id": str(uuid.uuid4())}
            response = self._client.post(
                url=self.ADD_AUDIT, json=json.loads(message.json()), headers=headers
            )
            result = self._client.parse_payload(response, AuditLogResponseModel)
            logging.info(f"Audit added successfully {result}")
            return result
        except PlatformAPIError as e:
            logger.error(f"Error adding the audit logs: {e}")
            return None
        except Exception as e:
            logger.error(f"Error fetching the audit logs: {e}")
            return None
