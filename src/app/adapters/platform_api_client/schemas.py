from datetime import datetime
from typing import Dict, List, Optional, Union

from pydantic import BaseModel


class AuditLogModel(BaseModel):
    user: str
    ipAddress: str
    event: str
    request: str
    source: str
    payload: Union[List, Dict, None]


class AuditLogResponseModel(BaseModel):
    id: Optional[str]
    status: Optional[str] = None
    error: Optional[str] = None
    message: Optional[str] = None


class AuditResponse(BaseModel):
    user: str
    ipAddress: str
    event: str
    request: str
    source: str
    payload: Union[List, Dict, None]
    created_date: datetime
