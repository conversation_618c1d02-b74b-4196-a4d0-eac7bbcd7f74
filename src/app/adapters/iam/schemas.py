from collections.abc import Sequence
from dataclasses import dataclass
from enum import Enum
from typing import Any
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field

from app.typedefs import Username

# USER


class UserBase(BaseModel):
    email: EmailStr
    username: Username
    first_name: str | None = None
    last_name: str | None = None
    enabled: bool
    email_verified: bool


class UserCreate(UserBase):
    attributes: dict[str, list[str]] = {}


class UserUpdate(UserBase):
    id: UUID
    attributes: dict[str, list[str]] = {}


class UserBrief(UserBase):
    id: UUID
    created_timestamp: int | None = None


class FederatedIdentity(BaseModel):
    identity_provider: str
    user_id: str
    user_name: str


class User(UserBase):
    id: UUID
    attributes: dict[str, list[str]] = {}
    federated_identities: Sequence[FederatedIdentity] = []
    created_timestamp: int
    required_actions: list[str] | None = []

    @property
    def identity_provider(self) -> str | None:
        match self.attributes:
            case {"identityProvider": [x]}:
                return x
            case _:
                return None

    def set_identity_provider(self, alias: str) -> None:
        self.attributes["identityProvider"] = [alias]

    def remove_identity_provider(self) -> None:
        self.attributes.pop("identityProvider", None)

    def to_update(self) -> UserUpdate:
        return UserUpdate.parse_obj(
            self.dict(include=set(UserUpdate.__fields__.keys()))
        )


@dataclass
class TokenRequest:
    username: str
    password: str
    client_id: str | None = Field(readOnly=True)
    client_secret: str | None = Field(readOnly=True)


@dataclass
class TokenResponse:
    access_token: str
    expires_in: int


class UserCredentials(BaseModel):
    id: UUID
    type: str


# PASSWORD POLICY


class PasswordPolicy(BaseModel):
    id: str
    value: None | int | str


# SERVICE ACCOUNT USER


class ServiceAccountUser(BaseModel):
    id: UUID
    enabled: bool
    email_verified: bool
    attributes: dict[str, list[str]] = {}


# GROUP


class GroupBase(BaseModel):
    name: str
    attributes: dict[str, list[str]] = {}


class Group(BaseModel):
    name: str
    path: str
    attributes: dict[str, list[str]] = {}


class GroupCreate(Group):
    id: UUID


# ROLE


class Role(BaseModel):
    name: str
    description: str | None = None
    attributes: dict[str, list[str]] = {}


# IDENTITY PROVIDER


class IdentityProvider(BaseModel):
    alias: str
    display_name: str | None = None


class ProtocolMapperType(str, Enum):
    USER_ATTRIBUTE = "oidc-usermodel-attribute-mapper"
    AUDIENCE = "oidc-audience-mapper"
    REALM_ROLE = "oidc-usermodel-realm-role-mapper"


# CLIENT SCOPE


class ProtocolMapper(BaseModel):
    name: str
    consent_required: bool = False
    type: str
    config: dict[str, str]


class ClientScope(BaseModel):
    name: str
    description: str | None = None
    attributes: dict[str, Any] = {}
    protocol_mappers: list[ProtocolMapper] = []


# CLIENT


class ClientBase(BaseModel):
    client_id: str
    name: str | None = None
    enabled: bool
    service_accounts_enabled: bool = True
    public_client: bool = False
    bearer_only: bool = False


class Client(ClientBase):
    optional_client_scopes: list[str] = []
    default_client_scopes: list[str] = []
