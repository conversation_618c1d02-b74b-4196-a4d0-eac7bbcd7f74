from abc import ABC, abstractmethod
from typing import Protocol
from uuid import UUID

from pydantic import UUID4, EmailStr, SecretStr

from app.adapters.iam.exc import (
    ClientScopeNotFound,
    GroupAlreadyExists,
    UserAlreadyExist,
)
from app.adapters.iam.schemas import (
    Client,
    ClientBase,
    ClientScope,
    FederatedIdentity,
    Group,
    GroupBase,
    GroupCreate,
    IdentityProvider,
    PasswordPolicy,
    Role,
    ServiceAccountUser,
    TokenRequest,
    TokenResponse,
    User,
    UserBrief,
    UserCreate,
    UserCredentials,
    UserUpdate,
)


class AbstractUserAPI(ABC):
    def add(self, user: UserCreate) -> User:
        existing_user = self.get_by_email(user.email)
        if existing_user:
            raise UserAlreadyExist(existing_user.id)
        return self._add(user)

    @abstractmethod
    def _add(self, user: UserCreate) -> User:
        ...

    @abstractmethod
    def get_by_id(self, user_id: UUID) -> User | None:
        ...

    @abstractmethod
    def get_by_email(self, email: EmailStr) -> User | None:
        ...

    @abstractmethod
    def update(self, user_in: UserUpdate) -> User:
        ...

    @abstractmethod
    def remove(self, user_id: UUID) -> None:
        ...

    @abstractmethod
    def get_many(
        self,
        email: str | None = None,
        createdTimestamp: int | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        exact: bool | None = None,
        search: str | None = None,
        email_verified: bool | None = None,
        enabled: bool | None = None,
        offset: int | None = None,
        limit: int | None = None,
    ) -> list[UserBrief]:
        ...

    @abstractmethod
    def set_password(self, user_id: UUID, password: SecretStr, temporary: bool) -> None:
        ...

    @abstractmethod
    def get_groups(self, user_id: UUID) -> list[Group]:
        ...

    @abstractmethod
    def add_roles(self, user_id: UUID, roles: list[str]) -> None:
        ...

    @abstractmethod
    def get_roles(self, user_id: UUID) -> list[str]:
        ...

    @abstractmethod
    def get_user_role_details(self, user_id: UUID) -> dict:
        ...

    @abstractmethod
    def remove_roles(self, user_id: UUID, roles: list[str]) -> None:
        ...

    @abstractmethod
    def add_federated_identity(
        self, user_id: UUID, federated_identity: FederatedIdentity
    ) -> None:
        """Add link to external IdP for the user."""

    @abstractmethod
    def revoke_token(self, user_id: UUID4) -> bool:
        ...

    @abstractmethod
    def get_token(self, token_request: TokenRequest) -> TokenResponse:
        ...

    @abstractmethod
    def get_user_credentials(self, user_id: UUID) -> list[UserCredentials]:
        ...

    @abstractmethod
    def remove_user_credential(self, user_id: UUID, credential_id: UUID) -> None:
        ...


class AbstractGroupAPI(ABC):
    def add(self, group: GroupBase, parent_path: str | None) -> GroupCreate:
        path_prefix = parent_path or ""

        path = f"{path_prefix}/{group.name}"
        existing_group = self.get(path)
        if existing_group:
            raise GroupAlreadyExists(existing_group.path)

        return self._add(group, parent_path)

    @abstractmethod
    def _add(self, group: GroupBase, parent_path: str | None) -> GroupCreate:
        ...

    @abstractmethod
    def get(self, path: str) -> Group | None:
        ...

    @abstractmethod
    def update(self, path: str, group: GroupBase) -> Group:
        ...

    @abstractmethod
    def remove(self, path: str) -> None:
        ...

    @abstractmethod
    def add_member(self, path: str, user_id: UUID) -> None:
        ...

    @abstractmethod
    def remove_member(self, path: str, user_id: UUID) -> None:
        ...

    @abstractmethod
    def get_members(self, path: str) -> list[User]:
        ...

    @abstractmethod
    def get_many(self) -> list[Group]:
        ...


class AbstractRoleAPI(ABC):
    @abstractmethod
    def add(self, role: Role) -> Role:
        ...

    @abstractmethod
    def get(self, name: str) -> Role | None:
        ...

    @abstractmethod
    def update(self, name: str, role_in: Role) -> Role:
        ...

    @abstractmethod
    def remove(self, name: str) -> None:
        ...

    @abstractmethod
    def get_many(
        self,
        search: str | None = None,
        offset: int | None = None,
        limit: int | None = None,
    ) -> list[Role]:
        ...


class AbstractIdentityProviderAPI(ABC):
    @abstractmethod
    def get_many(self) -> list[IdentityProvider]:
        ...

    @abstractmethod
    def get_by_alias(self, alias: str) -> IdentityProvider | None:
        ...


class AbstractClientScopeAPI(ABC):
    """Interface to manage scopes for authorization clients"""

    @abstractmethod
    def add(self, client_scope: ClientScope) -> ClientScope:
        """Add new scope"""

    @abstractmethod
    def _get_many(self) -> list[ClientScope]:
        """Get all scopes"""

    def get_many(self, names: list[str] | None = None) -> list[ClientScope]:
        """Get all scopes or filter by provided names"""

        scopes = self._get_many()
        if names is None:
            return scopes
        scopes_mapping = {scope.name: scope for scope in scopes}
        try:
            filtered_scopes = [scopes_mapping[name] for name in names]
        except KeyError as e:
            raise ClientScopeNotFound(e.args[0])
        return filtered_scopes

    def get_by_name(self, name: str) -> ClientScope | None:
        """Get the scope by its name"""

        try:
            scopes = self.get_many([name])
            return scopes[0]
        except ClientScopeNotFound:
            return None

    @abstractmethod
    def remove(self, name: str) -> None:
        """Remove the scope by name"""

    @abstractmethod
    def add_roles(self, name: str, role_names: list[str]) -> None:
        """
        Add a set of roles to the scope. Only this set of roles
        will be populated in the access token for the scope. And only
        this set of roles allows you to get the scope in a token.
        """

    @abstractmethod
    def get_roles(self, name: str) -> list[str]:
        """Get the set of roles associated with the scope"""


class AbstractClientAPI(ABC):
    """Interface to manage authorization clients"""

    @abstractmethod
    def add(self, client: ClientBase) -> Client:
        """Add new client"""

    @abstractmethod
    def get(self, client_id: str) -> Client | None:
        """Get the client by client_id"""

    @abstractmethod
    def update(self, client: ClientBase) -> Client:
        """Update the client"""

    @abstractmethod
    def remove(self, client_id: str) -> None:
        """Remove the client by its client_id"""

    @abstractmethod
    def get_service_account_user(self, client_id: str) -> ServiceAccountUser:
        """Get the service account user of the client"""

    @abstractmethod
    def update_service_account_user(
        self, user: ServiceAccountUser
    ) -> ServiceAccountUser:
        """Update service account user"""

    @abstractmethod
    def add_default_client_scopes(self, client_id: str, scopes: list[str]) -> None:
        """Add a set of default scopes for the client by client_id"""

    @abstractmethod
    def add_optional_client_scopes(self, client_id: str, scopes: list[str]) -> None:
        """Add a set of optional scopes for the client by client_id"""

    @abstractmethod
    def get_secret(self, client_id: str) -> str:
        """Get the client secret by client_id"""


class AbstractPasswordPolicyAPI(ABC):
    """Interface to manage password policies for users"""

    @abstractmethod
    def configure(self, policies: list[PasswordPolicy]) -> None:
        """Replace password policies with provided"""

    @abstractmethod
    def get_current(self) -> list[PasswordPolicy]:
        """Return all configured password policies"""


class AbstractIAM(Protocol):
    users: AbstractUserAPI
    groups: AbstractGroupAPI
    roles: AbstractRoleAPI
    identity_providers: AbstractIdentityProviderAPI
    client_scopes: AbstractClientScopeAPI
    clients: AbstractClientAPI
    password_policies: AbstractPasswordPolicyAPI
