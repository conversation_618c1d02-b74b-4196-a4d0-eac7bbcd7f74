class IAMException(Exception):
    ...


class UserNotFound(IAMException):
    ...


class UserAlreadyExist(IAMException):
    ...


class BadPassword(IAMException):
    ...


class GroupAlreadyExists(IAMException):
    ...


class GroupNotFound(IAMException):
    ...


class RoleAlreadyExists(IAMException):
    ...


class RoleNotFound(IAMException):
    ...


class ClientScopeAlreadyExists(IAMException):
    pass


class ClientScopeNotFound(IAMException):
    ...


class ClientAlreadyExists(Exception):
    ...


class ClientNotFound(Exception):
    ...


class IdentityProviderNotFound(Exception):
    ...


class UsersNotFound(IAMException):
    ...


class NotFound(Exception):
    ...


class OpenApiError(Exception):
    ...
