import json
import logging
from collections.abc import Generator, Iterator, Sequence
from contextlib import contextmanager
from typing import Any, TypeVar, cast
from uuid import UUID

from fastapi import HTT<PERSON>Exception
from fastapi.encoders import jsonable_encoder
from keycloak_client import KeycloakAdmin, KeycloakConflict, KeycloakException
from keycloak_client.api.clients import URL_CLIENT
from pydantic import UUID4, BaseModel, EmailStr, SecretStr, parse_obj_as
from starlette import status

from app.adapters.iam.base import (
    AbstractClientAPI,
    AbstractClientScopeAPI,
    AbstractGroupAPI,
    AbstractIAM,
    AbstractIdentityProviderAPI,
    AbstractPasswordPolicyAPI,
    AbstractRoleAPI,
    AbstractUserAPI,
)
from app.adapters.iam.exc import (
    BadPassword,
    ClientAlreadyExists,
    ClientNotFound,
    ClientScopeAlreadyExists,
    ClientScopeNotFound,
    GroupNotFound,
    IdentityProviderNotFound,
    NotFound,
    OpenApiError,
    RoleAlreadyExists,
    RoleNotFound,
    UserNotFound,
    UsersNotFound,
)
from app.adapters.iam.schemas import (
    Client,
    ClientBase,
    ClientScope,
    FederatedIdentity,
    Group,
    GroupBase,
    GroupCreate,
    IdentityProvider,
    PasswordPolicy,
    ProtocolMapper,
    Role,
    ServiceAccountUser,
    TokenRequest,
    TokenResponse,
    User,
    UserBrief,
    UserCreate,
    UserCredentials,
    UserUpdate,
)
from app.core.config import settings
from app.typedefs import Username


def to_lower_camel(v: str) -> str:
    first, *others = v.split("_")
    return "".join([first.lower(), *map(str.title, others)])


class KeycloakBaseModel(BaseModel):
    class Config:
        alias_generator = to_lower_camel
        allow_population_by_field_name = True


class KeycloakUserCreate(UserCreate, KeycloakBaseModel):
    username: Username


class KeycloakUserBrief(UserBrief, KeycloakBaseModel):
    ...


class KeycloakServiceAccountUser(ServiceAccountUser, KeycloakBaseModel):
    ...


class KeycloakFederatedIdentity(FederatedIdentity, KeycloakBaseModel):
    ...


class KeycloakUser(User, KeycloakBaseModel):
    federated_identities: Sequence[KeycloakFederatedIdentity] = []


class KeycloakUserUpdate(UserUpdate, KeycloakBaseModel):
    federated_identities: Sequence[KeycloakFederatedIdentity] = []


class KeycloakIdentityProvider(IdentityProvider, KeycloakBaseModel):
    ...


class KeycloakClientBase(ClientBase, KeycloakBaseModel):
    ...


class KeycloakClient(Client, KeycloakBaseModel):
    ...


class BaseKeycloakAPI:
    def __init__(self, ka: KeycloakAdmin):
        self.ka = ka


class KeycloakUserAPI(BaseKeycloakAPI, AbstractUserAPI):
    def _add(self, user: UserCreate) -> User:
        keycloak_user = to_keycloak(user, KeycloakUserCreate)
        payload = jsonable_encoder(keycloak_user)
        user_id = self.ka.users.create(payload)

        created_user = self.get_by_id(UUID(user_id))
        if not created_user:
            logging.error("Keycloak must return created user")
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        return created_user

    def get_by_id(self, user_id: UUID) -> User | None:
        try:
            with wrap_not_found_exception(UserNotFound(str(user_id))):
                user_data = self.ka.users.get(str(user_id))
        except UserNotFound:
            return None
        keycloak_user = parse_obj_as(KeycloakUser, user_data)
        return from_keycloak(keycloak_user, User)

    def get_by_email(self, email: EmailStr) -> User | None:
        result = self.ka.users.get_many(
            email=email, exact=True, brief_representation=True
        )
        if not result:
            return None
        user = self.get_by_id(result[0]["id"])
        if not user:
            logging.error("Keycloak must return the user from the list")
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        return user

    def update(self, user_in: UserUpdate) -> User:
        keycloak_user = to_keycloak(user_in, KeycloakUserUpdate)
        payload = jsonable_encoder(keycloak_user)
        with wrap_not_found_exception(UserNotFound(str(user_in.id))):
            self.ka.users.update(str(user_in.id), payload=payload)
        return cast(User, self.get_by_id(user_in.id))

    def remove(self, user_id: UUID) -> None:
        with wrap_not_found_exception(UserNotFound(str(user_id))):
            self.ka.users.delete(str(user_id))

    def get_many(
        self,
        email: str | None = None,
        createdTimestamp: int | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        exact: bool | None = None,
        search: str | None = None,
        email_verified: bool | None = None,
        enabled: bool | None = None,
        offset: int | None = None,
        limit: int | None = None,
    ) -> list[UserBrief]:
        user_list = self.ka.users.get_many(
            email=email,
            first_name=first_name,
            last_name=last_name,
            exact=exact,
            search=search,
            email_verified=email_verified,
            enabled=enabled,
            first=offset,
            max_=limit,
            brief_representation=True,
        )
        users_with_email = (u for u in user_list if u.get("email"))
        keycloak_users = parse_obj_as(list[KeycloakUserBrief], users_with_email)
        return [from_keycloak(user, UserBrief) for user in keycloak_users]

    def set_password(self, user_id: UUID, password: SecretStr, temporary: bool) -> None:
        try:
            logging.info(f"set_password user_id: {user_id}")
            logging.info(f"set_password temporary: {temporary}")
            with wrap_not_found_exception(UserNotFound(str(user_id))):
                self.ka.users.reset_password(
                    user_id=str(user_id),
                    password=password.get_secret_value(),
                    temporary=temporary,
                )
            logging.info("Passowrd triggered for user.....")
        except KeycloakException as e:
            response = e.response
            if not response.status_code == status.HTTP_400_BAD_REQUEST:
                raise e
            try:
                data = response.json()
            except json.JSONDecodeError:
                raise e
            if not isinstance(data, dict) or "error_description" not in data:
                raise e

            raise BadPassword(data["error_description"])

    def get_groups(self, user_id: UUID) -> list[Group]:
        with wrap_not_found_exception(UserNotFound(str(user_id))):
            groups = self.ka.users.get_groups(
                str(user_id), brief_representation=False, max_=-1
            )
        return parse_obj_as(list[Group], groups)

    def add_roles(self, user_id: UUID, roles: list[str]) -> None:
        logging.info(f"Adding roles {roles} to user {user_id}")
        roles_data = _get_roles_by_names(self.ka, roles)
        logging.info(f"Roles data: {roles_data}")
        with wrap_not_found_exception(UserNotFound(str(user_id))):
            self.ka.users.add_realm_roles(str(user_id), roles=roles_data)

    def get_roles(self, user_id: UUID) -> list[str]:
        with wrap_not_found_exception(UserNotFound(str(user_id))):
            roles = self.ka.users.get_realm_roles(str(user_id))
            return [r["name"] for r in roles]

    def get_user_role_details(self, user_id: UUID) -> dict:
        with wrap_not_found_exception(UserNotFound(str(user_id))):
            roles = self.ka.users.get_realm_roles(str(user_id))
            return {r["id"]: r["name"] for r in roles}

    def remove_roles(self, user_id: UUID, roles: list[str]) -> None:
        roles_data = _get_roles_by_names(self.ka, roles)
        with wrap_not_found_exception(UserNotFound(str(user_id))):
            self.ka.users.remove_realm_roles(str(user_id), roles=roles_data)

    def add_federated_identity(
        self, user_id: UUID, federated_identity: FederatedIdentity
    ) -> None:
        with wrap_not_found_exception(UserNotFound(str(user_id))):
            self.get_by_id(user_id)

        idp_alias = federated_identity.identity_provider
        url = self.ka.users.build_url(
            "/users/{user_id}/federated-identity/{idp}",
            user_id=user_id,
            idp=idp_alias,
        )
        keycloak_identity = to_keycloak(federated_identity, KeycloakFederatedIdentity)
        with wrap_not_found_exception(IdentityProviderNotFound(idp_alias)):
            response = self.ka.users._post(
                url=url, json=jsonable_encoder(keycloak_identity)
            )
            response.raise_for_status()

    def _get_token_request_data(self, request_data: TokenRequest) -> dict:
        request = {
            "grant_type": "password",
            "username": request_data.username,
            "password": request_data.password,
            "client_id": settings.OIDC_CLIENT_ID,
            "client_secret": settings.OIDC_CLIENT_SECRET,
        }
        return request

    def revoke_token(self, user_id: UUID4) -> bool:
        with wrap_not_found_exception(UsersNotFound("User not found.")):
            self.ka.users.revoke_token(str(user_id), settings.KEYCLOAK_REALM)
        return True

    def get_token(self, token_request: TokenRequest) -> TokenResponse:
        request = self._get_token_request_data(token_request)
        response = self.ka.users.get_token(request, f"{settings.OIDC_TOKEN_URL}")
        if response:
            _result = response
            if not _result["access_token"]:
                raise NotFound("Response is received but no token data found")
            access_token_data = TokenResponse(
                access_token=_result["access_token"],
                expires_in=_result["expires_in"],
            )
            return access_token_data
        else:
            raise OpenApiError("Open Api Error!!!!!")

    def get_user_credentials(self, user_id: UUID) -> list[UserCredentials]:
        logging.info(f"get_user_credentials user_id: {user_id}")
        with wrap_not_found_exception(UserNotFound(str(user_id))):
            user_data = self.ka.users.get_user_credential(str(user_id))
        logging.info(f"get_user_credentials user_data: {user_data}")
        return parse_obj_as(list[UserCredentials], user_data)

    def remove_user_credential(self, user_id: UUID, credential_id: UUID) -> None:
        logging.info(
            f"remove_user_credential user_id: {user_id},"
            f" credential_id: {credential_id} "
        )
        with wrap_not_found_exception(UserNotFound(str(user_id))):
            self.ka.users.remove_user_credential(str(user_id), str(credential_id))


class KeycloakGroupAPI(BaseKeycloakAPI, AbstractGroupAPI):
    def _add(self, group: GroupBase, parent_path: str | None) -> GroupCreate:
        version = self._get_version()
        payload = jsonable_encoder(group)
        if parent_path:
            with wrap_not_found_exception(GroupNotFound(parent_path)):
                parent_data = self.ka.groups.get_by_path(
                    parent_path, version
                )  # type:ignore

            group_id = self.ka.groups.create_child(parent_data["id"], payload=payload)
        else:
            group_id = self.ka.groups.create(payload)

        group_data = self.ka.groups.get(group_id)
        return parse_obj_as(GroupCreate, group_data)

    def get(self, path: str) -> Group | None:
        try:
            version = self._get_version()
            with wrap_not_found_exception(GroupNotFound(path)):
                group_data = self.ka.groups.get_by_path(path, version)  # type:ignore
                if not group_data or len(group_data) == 0:
                    raise GroupNotFound(path)
        except GroupNotFound:
            return None
        return parse_obj_as(Group, group_data)

    def update(self, path: str, group_in: GroupBase) -> Group:
        version = self._get_version()
        group_data = self.ka.groups.get_by_path(path, version)  # type:ignore
        if not group_data:
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        payload = jsonable_encoder(group_in)

        if version > "15.0.3" and "subGroups" in group_data and group_data["subGroups"]:
            group_id = group_data["subGroups"][0]["id"]
        else:
            group_id = group_data["id"]

        self.ka.groups.update(group_id, payload=payload)
        group_data = self.ka.groups.get(group_id)
        if not group_data:
            logging.error("Keycloak must return updated group")
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        return parse_obj_as(Group, group_data)

    def remove(self, path: str) -> None:
        version = self._get_version()
        group_data = self.ka.groups.get_by_path(path, version)  # type:ignore
        if not group_data:
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        if version > "15.0.3" and "subGroups" in group_data and group_data["subGroups"]:
            group_id = group_data["subGroups"][0]["id"]
        else:
            group_id = group_data["id"]

        self.ka.groups.delete(group_id)

    def add_member(self, path: str, user_id: UUID) -> None:
        version = self._get_version()
        group_data = self.ka.groups.get_by_path(path, version)  # type:ignore
        if not group_data:
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        if version > "15.0.3" and "subGroups" in group_data and group_data["subGroups"]:
            group_id = group_data["subGroups"][0]["id"]
        else:
            group_id = group_data["id"]
        self.ka.users.create_group_membership(str(user_id), group_id=group_id)

    def remove_member(self, path: str, user_id: UUID) -> None:
        version = self._get_version()
        group_data = self.ka.groups.get_by_path(path, version)  # type:ignore

        if not group_data:
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        if version > "15.0.3" and "subGroups" in group_data and group_data["subGroups"]:
            group_id = group_data["subGroups"][0]["id"]
        else:
            group_id = group_data["id"]

        self.ka.users.delete_group_membership(str(user_id), group_id=group_id)

    def _get_token_request_data(self) -> dict:
        request = {
            "grant_type": "password",
            "username": settings.KC_SVC_USER_NAME,
            "password": settings.KC_SVC_USER_PASSWORD,
            "client_id": settings.OIDC_CLIENT_ID,
            "client_secret": settings.OIDC_CLIENT_SECRET,
        }
        return request

    def _get_access_token(self) -> str:
        request = self._get_token_request_data()
        token_response = self.ka.users.get_token(
            oidc_token_url=f"{settings.OIDC_TOKEN_URL}", payload=request
        )
        self.access_token = token_response["access_token"]
        return self.access_token

    def _get_version(self) -> str:
        access_token = self._get_access_token()
        self.version = self.ka.groups.get_version(access_token)
        return self.version

    def get_members(self, path: str) -> list[User]:
        version = self._get_version()
        logging.info(f"Version: {version}")

        group_data = self.ka.groups.get_by_path(path, version)  # type:ignore
        if not group_data:
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        if version > "15.0.3" and "subGroups" in group_data and group_data["subGroups"]:
            # Use the id of the first subgroup
            group_id = group_data["subGroups"][0]["id"]
        else:
            # Use the group_data's id if no subgroups are present
            # In case of parent group
            group_id = group_data["id"]

        members = self.ka.groups.get_members(group_id, max_=-1)
        members_with_email = (u for u in members if u.get("email"))
        keycloak_members = parse_obj_as(list[KeycloakUser], members_with_email)
        return [from_keycloak(u, User) for u in keycloak_members]

    def get_many(self) -> list[Group]:
        hierarchy = self.ka.groups.get_many(brief_representation=False)
        groups_data = self._flatten_hierarchy(hierarchy)
        return parse_obj_as(list[Group], groups_data)

    def _flatten_hierarchy(
        self, hierarchy: list[dict[str, Any]]
    ) -> list[dict[str, Any]]:
        groups = []
        for g in hierarchy:
            groups.append(g)
            sub_groups = g["subGroups"]
            if sub_groups:
                groups.extend(self._flatten_hierarchy(sub_groups))
        return groups


class KeycloakRoleAPI(BaseKeycloakAPI, AbstractRoleAPI):
    def add(self, role: Role) -> Role:
        try:
            self.ka.realm_roles.create(jsonable_encoder(role))
        except KeycloakConflict:
            raise RoleAlreadyExists(role.name)
        role_data = self.ka.realm_roles.get_by_name(role.name)
        return parse_obj_as(Role, role_data)

    def get(self, name: str) -> Role | None:
        try:
            with wrap_not_found_exception(RoleNotFound(name)):
                role_data = self.ka.realm_roles.get_by_name(name)
            return parse_obj_as(Role, role_data)
        except RoleNotFound:
            return None

    def update(self, name: str, role_in: Role) -> Role:
        with wrap_not_found_exception(RoleNotFound(name)):
            self.ka.realm_roles.update_by_name(name, payload=jsonable_encoder(role_in))
        role = self.get(role_in.name)
        if not role:
            logging.error("Keycloak must return updated role")
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        return role

    def remove(self, name: str) -> None:
        with wrap_not_found_exception(RoleNotFound(name)):
            self.ka.client.delete(
                self.ka.get_admin_url(f"/roles/{name}"),
                headers=dict(authorization=f"Bearer {self.ka.pat}"),
            )

    def get_many(
        self,
        search: str | None = None,
        offset: int | None = None,
        limit: int | None = None,
    ) -> list[Role]:
        roles_data = self.ka.realm_roles.get_many(
            search=search, first=offset, limit=limit, brief_representation=False
        )
        return parse_obj_as(list[Role], roles_data)


class KeycloakIdentityProviderAPI(BaseKeycloakAPI, AbstractIdentityProviderAPI):
    def get_many(self) -> list[IdentityProvider]:
        result = self.ka.identity_providers.get_many()
        identity_providers = parse_obj_as(
            list[KeycloakIdentityProvider],
            result,
        )
        return [from_keycloak(idp, IdentityProvider) for idp in identity_providers]

    def get_by_alias(self, alias: str) -> IdentityProvider | None:
        return next(
            (idp for idp in self.get_many() if idp.alias == alias),
            None,
        )


class KeycloakClientScopeAPI(BaseKeycloakAPI, AbstractClientScopeAPI):
    def _get_many(self) -> list[ClientScope]:
        scopes = self.ka.client_scopes.get_many()
        return list(map(self._scope_from_keycloak, scopes))

    def add(self, client_scope: ClientScope) -> ClientScope:
        payload = self._scope_to_keycloak(client_scope)
        try:
            client_scope_id = self.ka.client_scopes.create(payload)
        except KeycloakConflict:
            raise ClientScopeAlreadyExists(client_scope.name)
        raw_client_scope = self.ka.client_scopes.get(client_scope_id)
        created = self._scope_from_keycloak(raw_client_scope)
        return created

    def remove(self, name: str) -> None:
        scope_id = self._get_id_by_name(name)
        if scope_id is None:
            raise ClientScopeNotFound(name)
        self.ka.client_scopes.delete(scope_id)

    def add_roles(self, name: str, role_names: list[str]) -> None:
        roles = _get_roles_by_names(self.ka, role_names)
        scope_id = self._get_id_by_name(name)
        if scope_id is None:
            raise ClientScopeNotFound(name)
        self.ka.client_scopes.add_realm_level_roles(scope_id, roles=roles)

    def get_roles(self, name: str) -> list[str]:
        scope_id = self._get_id_by_name(name)
        if scope_id is None:
            raise ClientScopeNotFound(name)
        roles_raw = self.ka.client_scopes.get_realm_level_roles(scope_id)
        return [role["name"] for role in roles_raw]

    def _get_id_by_name(self, name: str) -> str | None:
        scopes_raw = self.ka.client_scopes.get_many()
        scope = next((s for s in scopes_raw if s["name"] == name), None)
        if scope is None:
            return None
        return scope["id"]

    @classmethod
    def _scope_to_keycloak(cls, client_scope: ClientScope) -> dict[str, Any]:
        return dict(
            name=client_scope.name,
            description=client_scope.description,
            protocol="openid-connect",
            attributes=client_scope.attributes,
            protocolMappers=[
                cls._mapper_to_keycloak(mapper)
                for mapper in client_scope.protocol_mappers
            ],
        )

    @classmethod
    def _mapper_to_keycloak(cls, mapper: ProtocolMapper) -> dict[str, Any]:
        return dict(
            name=mapper.name,
            protocol="openid-connect",
            protocolMapper=mapper.type,
            consentRequired=mapper.consent_required,
            config=mapper.config,
        )

    @classmethod
    def _scope_from_keycloak(cls, raw_client_scope: dict[str, Any]) -> ClientScope:
        return ClientScope(
            name=raw_client_scope["name"],
            description=raw_client_scope.get("description"),
            attributes=raw_client_scope["attributes"],
            protocol_mappers=[
                cls._mapper_from_keycloak(mapper)
                for mapper in raw_client_scope.get("protocolMappers", [])
            ],
        )

    @classmethod
    def _mapper_from_keycloak(cls, raw_mapper: dict[str, Any]) -> ProtocolMapper:
        return ProtocolMapper(
            name=raw_mapper["name"],
            consent_required=raw_mapper.get("consentRequired", False),
            config=raw_mapper.get("config", {}),
            type=raw_mapper["protocolMapper"],
        )


class KeycloakClientAPI(BaseKeycloakAPI, AbstractClientAPI):
    def add(self, client_in: ClientBase) -> Client:
        k_client_in = to_keycloak(client_in, KeycloakClientBase)
        payload = jsonable_encoder(k_client_in, by_alias=True)
        try:
            client_uuid = self.ka.clients.create(payload)
            # Keycloak doesn't create a client secret at creation
            update_payload = dict(
                standardFlowEnabled=False,
                directAccessGrantsEnabled=False,
                fullScopeAllowed=False,
            )
            self.ka.clients.update(client_uuid, payload=update_payload)
        except KeycloakConflict:
            raise ClientAlreadyExists(client_in.client_id)
        raw_client = self.ka.clients.get(client_uuid)
        k_client = parse_obj_as(KeycloakClient, raw_client)
        return from_keycloak(k_client, Client)

    def get(self, client_id: str) -> Client | None:
        raw_client = self.ka.clients.get_by_client_id(client_id)
        if not raw_client:
            return None

        k_client = parse_obj_as(KeycloakClient, raw_client)
        return from_keycloak(k_client, Client)

    def update(self, client_in: ClientBase) -> Client:
        client_raw = self.ka.clients.get_by_client_id(client_in.client_id)
        if not client_raw:
            raise ClientNotFound(client_in.client_id)
        k_client = to_keycloak(client_in, KeycloakClient)
        client_raw.update(k_client.dict(by_alias=True))
        self.ka.clients.update(client_raw["id"], payload=client_raw)
        updated = self.get(client_in.client_id)
        if not updated:
            logging.error("Keycloak must return updated group")
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        return updated

    def remove(self, client_id: str) -> None:
        raw_client = self.ka.clients.get_by_client_id(client_id)
        if not raw_client:
            raise ClientNotFound(client_id)
        url = self.ka.clients.build_url(URL_CLIENT, id=raw_client["id"])
        self.ka.clients._delete(url)  # noqa

    def get_service_account_user(self, client_id: str) -> ServiceAccountUser:
        raw_client = self.ka.clients.get_by_client_id(client_id)
        if not raw_client:
            raise ClientNotFound(client_id)
        raw_user = self.ka.clients.get_service_account_user(raw_client["id"])
        keycloak_user = parse_obj_as(KeycloakServiceAccountUser, raw_user)
        return from_keycloak(keycloak_user, ServiceAccountUser)

    def update_service_account_user(
        self, user: ServiceAccountUser
    ) -> ServiceAccountUser:
        keycloak_user = to_keycloak(user, KeycloakServiceAccountUser)
        payload = jsonable_encoder(keycloak_user)
        with wrap_not_found_exception(UserNotFound(str(user.id))):
            self.ka.users.update(str(user.id), payload=payload)
        return user

    def add_default_client_scopes(self, client_id: str, scopes: list[str]) -> None:
        client_raw = self.ka.clients.get_by_client_id(client_id)
        if not client_raw:
            raise ClientNotFound(client_id)

        scopes_raw = self._get_raw_scopes(scopes)

        for scope in scopes_raw:
            self.ka.clients.add_default_client_scope(
                client_raw["id"], client_scope_id=scope["id"]
            )

    def add_optional_client_scopes(self, client_id: str, scopes: list[str]) -> None:
        client_raw = self.ka.clients.get_by_client_id(client_id)
        if not client_raw:
            raise ClientNotFound(client_id)

        scopes_raw = self._get_raw_scopes(scopes)

        for scope in scopes_raw:
            self.ka.clients.add_optional_client_scope(
                client_raw["id"],
                client_scope_id=scope["id"],
            )

    def _get_raw_scopes(self, scopes: list[str] | None) -> list[dict[str, Any]]:
        scopes_raw = self.ka.client_scopes.get_many()
        if scopes is None:
            return scopes_raw
        mapped_scopes = {s["name"]: s for s in scopes_raw}
        try:
            filtered_scopes = [mapped_scopes[name] for name in scopes]
        except KeyError as e:
            raise ClientScopeNotFound(e.args[0])
        else:
            return filtered_scopes

    def get_secret(self, client_id: str) -> str:
        client_data = self.ka.clients.get_by_client_id(client_id)
        if client_data is None:
            raise ClientNotFound(client_id)
        secret_data = self.ka.clients.get_secret(client_data["id"])
        return secret_data["value"]


class KeycloakPasswordPolicyAPI(BaseKeycloakAPI, AbstractPasswordPolicyAPI):
    def configure(self, policies: list[PasswordPolicy]) -> None:
        policy_string = self._make_policy_string(policies)
        realm = self.ka.realm.get()
        realm.update({"passwordPolicy": policy_string})
        self.ka.realm.update(realm)

    def get_current(self) -> list[PasswordPolicy]:
        realm = self.ka.realm.get()
        policy_str = realm.get("passwordPolicy", "")
        policies = list(self._read_policy_string(policy_str))
        return policies

    @classmethod
    def _read_policy_string(cls, policy_str: str) -> Iterator[PasswordPolicy]:
        policies_str = policy_str.split(" and ")
        for policy_str in policies_str:
            if not policy_str:
                continue
            id_, *other = policy_str.split("(", maxsplit=1)
            arg_str = other[0].removesuffix(")")
            arg = cls._parse_policy_value(arg_str)
            yield PasswordPolicy(id=id_, value=arg)

    @classmethod
    def _parse_policy_value(cls, val: str) -> None | int | str:
        if val == "undefined":
            return None

        try:
            return int(val)
        except ValueError:
            return val

    @classmethod
    def _make_policy_string(cls, policies: list[PasswordPolicy]) -> str:
        return " and ".join(map(cls._policy_to_string, policies))

    @classmethod
    def _policy_to_string(cls, policy: PasswordPolicy) -> str:
        val = "undefined" if policy.value is None else policy.value
        return f"{policy.id}({val})"


class KeycloakIAM(AbstractIAM):
    def __init__(self, ka: KeycloakAdmin):
        self.users = KeycloakUserAPI(ka)
        self.groups = KeycloakGroupAPI(ka)
        self.roles = KeycloakRoleAPI(ka)
        self.identity_providers = KeycloakIdentityProviderAPI(ka)
        self.client_scopes = KeycloakClientScopeAPI(ka)
        self.clients = KeycloakClientAPI(ka)
        self.password_policies = KeycloakPasswordPolicyAPI(ka)


def _get_roles_by_names(ka: KeycloakAdmin, roles: list[str]) -> list[dict[str, Any]]:
    roles_data = []
    logging.info(f"Getting roles: {roles}")
    for role_name in roles:
        with wrap_not_found_exception(RoleNotFound(role_name)):
            role = ka.realm_roles.get_by_name(role_name)
        roles_data.append(role)
    logging.info(f"Found roles: {roles_data}")
    return roles_data


@contextmanager
def wrap_not_found_exception(exc: Exception) -> Generator[None, None, None]:
    try:
        yield
    except KeycloakException as e:
        if e.response.status_code == status.HTTP_404_NOT_FOUND:
            raise exc
        raise e


T = TypeVar("T", bound=BaseModel)
KT = TypeVar("KT", bound=KeycloakBaseModel)


def to_keycloak(val: T, model: type[KT], **extra_fields: Any) -> KT:
    if not issubclass(model, type(val)):
        logging.error("Model must derive from the val class")
        raise HTTPException(status.HTTP_400_BAD_REQUEST)
    result = model.parse_obj({**val.dict(), **extra_fields})
    return cast(KT, result)


def from_keycloak(val: KT, model: type[T], **extra_fields: Any) -> T:
    if not issubclass(type(val), model):
        logging.error("val class must derive from the model")
        raise HTTPException(status.HTTP_400_BAD_REQUEST)
    return model.parse_obj({**val.dict(by_alias=False), **extra_fields})
