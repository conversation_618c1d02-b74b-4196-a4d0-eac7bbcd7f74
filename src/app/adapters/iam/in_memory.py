import time
import uuid
from collections import defaultdict
from dataclasses import dataclass, field
from typing import cast
from uuid import UUID

from pydantic import UUID<PERSON>, EmailStr, SecretStr

from app.adapters.iam.base import (
    AbstractClientAPI,
    AbstractClientScopeAPI,
    AbstractGroupAPI,
    AbstractIAM,
    AbstractIdentityProviderAPI,
    AbstractPasswordPolicyAPI,
    AbstractRoleAPI,
    AbstractUserAPI,
)
from app.adapters.iam.exc import (
    ClientAlreadyExists,
    ClientNotFound,
    ClientScopeAlreadyExists,
    ClientScopeNotFound,
    GroupNotFound,
    RoleAlreadyExists,
    RoleNotFound,
    UserNotFound,
)
from app.adapters.iam.schemas import (
    Client,
    ClientBase,
    ClientScope,
    FederatedIdentity,
    Group,
    GroupBase,
    GroupCreate,
    IdentityProvider,
    PasswordPolicy,
    Role,
    ServiceAccountUser,
    TokenRequest,
    TokenResponse,
    User,
    UserBrief,
    UserCreate,
    UserCredentials,
    UserUpdate,
)


@dataclass
class IAMStore:
    users: dict[UUID, User | ServiceAccountUser] = field(default_factory=dict)
    user_password: dict[UUID, tuple[SecretStr, bool]] = field(default_factory=dict)
    user_groups: defaultdict[UUID, set[str]] = field(
        default_factory=lambda: defaultdict(set)
    )
    user_roles: defaultdict[UUID, set[str]] = field(
        default_factory=lambda: defaultdict(set)
    )
    user_identity_providers: defaultdict[UUID, list[FederatedIdentity]] = field(
        default_factory=lambda: defaultdict(list)
    )
    groups: dict[str, Group] = field(default_factory=dict)
    roles: dict[str, Role] = field(default_factory=dict)
    client_scopes: dict[str, ClientScope] = field(default_factory=dict)
    client_scope_roles: dict[str, set[str]] = field(
        default_factory=lambda: defaultdict(set)
    )
    clients: dict[str, Client] = field(default_factory=dict)
    client_secrets: dict[str, str] = field(default_factory=dict)
    service_accounts: dict[str, UUID] = field(default_factory=dict)
    default_client_scopes: defaultdict[str, set[str]] = field(
        default_factory=lambda: defaultdict(set)
    )
    optional_client_scopes: defaultdict[str, set[str]] = field(
        default_factory=lambda: defaultdict(set)
    )
    identity_providers: list[IdentityProvider] = field(default_factory=list)
    password_policies: list[PasswordPolicy] = field(default_factory=list)


class BaseInMemoryAPI:
    def __init__(self, store: IAMStore):
        self.store = store


class InMemoryUserAPI(BaseInMemoryAPI, AbstractUserAPI):
    @classmethod
    def get_timestamp(cls) -> int:
        return int(time.time())

    def add(self, user: UserCreate) -> User:
        created = User(
            **user.dict(), id=uuid.uuid4(), created_timestamp=self.get_timestamp()
        )
        self.store.users[created.id] = created
        return created

    def _add(self, user: UserCreate) -> User:
        created = User(
            **user.dict(), id=uuid.uuid4(), created_timestamp=self.get_timestamp()
        )
        self.store.users[created.id] = created
        return created

    def get_by_id(self, user_id: UUID) -> User | None:
        try:
            user = cast(User, self.store.users[user_id])
            user.federated_identities = self.store.user_identity_providers[user_id]
            return user
        except KeyError:
            return None

    def get_by_email(self, email: EmailStr) -> User | None:
        user = next(
            (
                u
                for u in self.store.users.values()
                if isinstance(u, User) and u.email == email
            ),
            None,
        )
        if user is not None:
            user.federated_identities = self.store.user_identity_providers[user.id]
        return user

    def update(self, user_in: UserUpdate) -> User:
        old = self.get_by_id(user_in.id)
        if not old:
            raise UserNotFound(user_in.id)
        new = User.parse_obj({**old.dict(), **user_in.dict()})
        self.store.users[user_in.id] = new
        return new

    def remove(self, user_id: UUID) -> None:
        if user_id not in self.store.users:
            raise UserNotFound(user_id)
        self.store.users.pop(user_id)

        self.store.user_groups.pop(user_id, None)
        self.store.user_roles.pop(user_id, None)
        self.store.user_password.pop(user_id, None)
        self.store.user_identity_providers.pop(user_id, None)

    def get_many(
        self,
        email: str | None = None,
        createdTimestamp: int | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        exact: bool | None = None,
        search: str | None = None,
        email_verified: bool | None = None,
        enabled: bool | None = None,
        offset: int | None = None,
        limit: int | None = None,
    ) -> list[UserBrief]:
        params = [
            email,
            createdTimestamp,
            first_name,
            last_name,
            exact,
            search,
            email_verified,
            enabled,
            offset,
            limit,
        ]
        if any(params):
            raise NotImplementedError()
        return [UserBrief(**u.dict()) for u in self.store.users.values()]

    def set_password(self, user_id: UUID, password: SecretStr, temporary: bool) -> None:
        user = self.get_by_id(user_id)
        if not user:
            raise UserNotFound(user_id)
        self.store.user_password[user_id] = (password, temporary)

    def get_groups(self, user_id: UUID) -> list[Group]:
        user = self.get_by_id(user_id)
        if not user:
            raise UserNotFound(user_id)

        return [self.store.groups[p] for p in self.store.user_groups[user_id]]

    def add_roles(self, user_id: UUID, roles: list[str]) -> None:
        if user_id not in self.store.users:
            raise UserNotFound(user_id)
        self.store.user_roles[user_id].update(roles)

    def get_roles(self, user_id: UUID) -> list[str]:
        user = self.get_by_id(user_id)
        if not user:
            raise UserNotFound(user_id)
        return list(self.store.user_roles[user_id])

    def remove_roles(self, user_id: UUID, roles: list[str]) -> None:
        user = self.get_by_id(user_id)
        if not user:
            raise UserNotFound(user_id)
        self.store.user_roles[user_id].difference_update(roles)

    def add_federated_identity(
        self, user_id: UUID, federated_identity: FederatedIdentity
    ) -> None:
        if user_id not in self.store.users:
            raise UserNotFound(user_id)
        self.store.user_identity_providers[user_id].append(federated_identity)

    def revoke_token(self, user_id: UUID4) -> bool:
        pass

    def get_token(self, token_request: TokenRequest) -> TokenResponse:
        pass

    def get_user_role_details(self, user_id: UUID):
        pass

    def get_user_credentials(self, user_id: UUID) -> list[UserCredentials]:
        ...

    def remove_user_credential(self, user_id: UUID, credential_id: UUID) -> None:
        ...


class InMemoryGroupAPI(BaseInMemoryAPI, AbstractGroupAPI):
    def _add(self, group: GroupBase, parent_path: str | None) -> GroupCreate:
        if parent_path and parent_path not in self.store.groups:
            raise GroupNotFound(parent_path)
        created = GroupCreate(
            id=uuid.uuid4(),
            path=f'{parent_path or ""}/{group.name}',
            **group.dict(),
        )
        self.store.groups[created.path] = created
        return created

    def get(self, path: str) -> Group | None:
        return self.store.groups.get(path)

    def update(self, path: str, group: GroupBase) -> Group:
        existing_group = self.store.groups.get(path)
        if not existing_group:
            raise GroupNotFound(path)

        new_path = path.removesuffix(existing_group.name) + group.name
        updated_group = Group(path=new_path, **group.dict())
        self._replace_group_in_store(existing_group, updated_group)

        # Update sub-groups
        for group_path in list(self.store.groups.keys()):
            if not group_path.startswith(f"{path}/"):
                continue

            sub_group = self.store.groups[group_path]
            updated_sub_group = Group(
                path=new_path + sub_group.path.removeprefix(path),
                **sub_group.dict(exclude={"path"}),
            )
            self._replace_group_in_store(sub_group, updated_sub_group)
        return updated_group

    def _replace_group_in_store(self, old_group: Group, new_group: Group) -> None:
        for user_groups in self.store.user_groups.values():
            if old_group.path in user_groups:
                user_groups.remove(old_group.path)
                user_groups.add(new_group.path)
        del self.store.groups[old_group.path]
        self.store.groups[new_group.path] = new_group

    def remove(self, path: str) -> None:
        group = self.get(path)
        if not group:
            raise GroupNotFound(path)
        self.store.groups.pop(path)
        for groups in self.store.user_groups.values():
            if group.path in groups:
                groups.remove(group.path)

    def add_member(self, path: str, user_id: UUID) -> None:
        group = self.get(path)
        if not group:
            raise GroupNotFound(path)

        user_groups = self.store.user_groups[user_id]
        if group.path not in user_groups:
            user_groups.add(group.path)

    def remove_member(self, path: str, user_id: UUID) -> None:
        group = self.get(path)
        if not group:
            raise GroupNotFound(path)
        user_groups = self.store.user_groups[user_id]
        user_groups.remove(group.path)

    def get_members(self, path: str) -> list[User]:
        group = self.get(path)
        if not group:
            raise GroupNotFound(path)
        users = []
        for user_id, groups in self.store.user_groups.items():
            if group.path in groups:
                users.append(self.store.users[user_id])
        return cast(list[User], users)

    def get_many(self) -> list[Group]:
        return list(self.store.groups.values())


class InMemoryRoleAPI(BaseInMemoryAPI, AbstractRoleAPI):
    def add(self, role: Role) -> Role:
        existing_role = self.get(role.name)
        if existing_role:
            raise RoleAlreadyExists(role.name)
        self.store.roles[role.name] = role
        return role

    def get(self, name: str) -> Role | None:
        return self.store.roles.get(name, None)

    def update(self, name: str, role_in: Role) -> Role:
        existing_role = self.get(name)
        if not existing_role:
            raise RoleNotFound(name)
        self.store.roles.pop(name)
        self.store.roles[role_in.name] = role_in
        for roles in self.store.user_roles.values():
            if name in roles:
                roles.remove(name)
                roles.add(role_in.name)
        return role_in

    def remove(self, name: str) -> None:
        existing_role = self.get(name)
        if not existing_role:
            raise RoleNotFound(name)
        self.store.roles.pop(name)
        for roles in self.store.user_roles.values():
            if name in roles:
                roles.remove(name)

    def get_many(
        self,
        search: str | None = None,
        offset: int | None = None,
        limit: int | None = None,
    ) -> list[Role]:
        if any([search, offset, limit]):
            raise NotImplementedError()
        return list(self.store.roles.values())


class InMemoryClientScopeAPI(BaseInMemoryAPI, AbstractClientScopeAPI):
    def add(self, client_scope: ClientScope) -> ClientScope:
        if client_scope.name in self.store.client_scopes:
            raise ClientScopeAlreadyExists(client_scope.name)
        self.store.client_scopes[client_scope.name] = client_scope
        return client_scope

    def _get_many(self, names: list[str] | None = None) -> list[ClientScope]:
        return list(self.store.client_scopes.values())

    def remove(self, name: str) -> None:
        if name not in self.store.client_scopes:
            raise ClientScopeNotFound(name)
        self.store.client_scopes.pop(name)

    def add_roles(self, name: str, role_names: list[str]) -> None:
        if name not in self.store.client_scopes:
            raise ClientScopeNotFound(name)
        for role in role_names:
            if role not in self.store.roles:
                raise RoleNotFound(role)
        self.store.client_scope_roles[name].update(role_names)

    def get_roles(self, name: str) -> list[str]:
        if name not in self.store.client_scopes:
            raise ClientScopeNotFound(name)
        return list(self.store.client_scope_roles[name])


class InMemoryIdentityProviderAPI(BaseInMemoryAPI, AbstractIdentityProviderAPI):
    def get_many(self) -> list[IdentityProvider]:
        return list(self.store.identity_providers)

    def get_by_alias(self, alias: str) -> IdentityProvider | None:
        return next(
            (p for p in self.store.identity_providers if p.alias == alias), None
        )


class InMemoryPasswordPolicyAPI(BaseInMemoryAPI, AbstractPasswordPolicyAPI):
    def configure(self, policies: list[PasswordPolicy]) -> None:
        self.store.password_policies = policies

    def get_current(self) -> list[PasswordPolicy]:
        return self.store.password_policies


class InMemoryClientAPI(BaseInMemoryAPI, AbstractClientAPI):
    def add(self, client: ClientBase) -> Client:
        if client.client_id in self.store.clients:
            raise ClientAlreadyExists(client.client_id)
        self.store.clients[client.client_id] = Client.parse_obj(client.dict())
        self.store.client_secrets[client.client_id] = str(uuid.uuid4())
        if client.service_accounts_enabled:
            user_id = uuid.uuid4()
            self.store.users[user_id] = ServiceAccountUser(
                id=user_id,
                enabled=True,
                email_verified=False,
            )
            self.store.service_accounts[client.client_id] = user_id
        return self.store.clients[client.client_id]

    def get(self, client_id: str) -> Client | None:
        try:
            return self.store.clients[client_id]
        except KeyError:
            return None

    def update(self, client: ClientBase) -> Client:
        try:
            stored = self.store.clients[client.client_id]
        except KeyError:
            raise ClientNotFound(client.client_id)
        new = stored.copy(update=client.dict())
        self.store.clients[client.client_id] = new
        return new

    def remove(self, client_id: str) -> None:
        try:
            del self.store.clients[client_id]
            del self.store.client_secrets[client_id]
        except KeyError:
            raise ClientNotFound(client_id)

    def get_service_account_user(self, client_id: str) -> ServiceAccountUser:
        if client_id not in self.store.clients:
            raise ClientNotFound(client_id)
        user_id = self.store.service_accounts[client_id]
        return cast(ServiceAccountUser, self.store.users[user_id])

    def update_service_account_user(
        self, user: ServiceAccountUser
    ) -> ServiceAccountUser:
        self.store.users[user.id] = user
        return user

    def add_default_client_scopes(self, client_id: str, scopes: list[str]) -> None:
        for scope in scopes:
            if scope not in self.store.client_scopes:
                raise ClientScopeNotFound(scope)
        self.store.default_client_scopes[client_id].update(scopes)

    def add_optional_client_scopes(self, client_id: str, scopes: list[str]) -> None:
        for scope in scopes:
            if scope not in self.store.client_scopes:
                raise ClientScopeNotFound(scope)
        self.store.optional_client_scopes[client_id].update(scopes)

    def get_secret(self, client_id: str) -> str:
        try:
            return self.store.client_secrets[client_id]
        except KeyError:
            raise ClientNotFound(client_id)


class InMemoryIAM(AbstractIAM):
    def __init__(self, store: IAMStore):
        self.users = InMemoryUserAPI(store)
        self.groups = InMemoryGroupAPI(store)
        self.roles = InMemoryRoleAPI(store)
        self.client_scopes = InMemoryClientScopeAPI(store)
        self.clients = InMemoryClientAPI(store)
        self.identity_providers = InMemoryIdentityProviderAPI(store)
        self.password_policies = InMemoryPasswordPolicyAPI(store)
