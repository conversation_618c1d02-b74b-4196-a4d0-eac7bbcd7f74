import logging

import httpx
import tenacity
from fastapi.encoders import jsonable_encoder
from tenacity import (
    after_log,
    retry_if_exception_type,
    stop_after_attempt,
    stop_after_delay,
    wait_exponential,
)

from app import schemas
from app.adapters.core_api.base import AbstractCoreFacade
from app.adapters.core_api.exc import ServiceUnavailable
from app.core.config import settings
from app.schemas.mail import EmailMessage

logger = logging.getLogger(__name__)


class CoreAPI(AbstractCoreFacade):
    SEND_MAIL_URL = settings.EMAIL_DELIVERY_URL + "/email/send"
    PARTNER_USER_REGISTERED_URL = settings.PARTNER_APPS_URL + "/partners/user"
    CONTACTS_URL = settings.CONTACTS_URL + "/contacts/"

    def __init__(self, api_client: httpx.Client):
        self.api = api_client

    @tenacity.retry(
        reraise=True,
        retry=retry_if_exception_type(ServiceUnavailable),
        wait=wait_exponential(multiplier=1),
        stop=(stop_after_delay(10) | stop_after_attempt(5)),
        after=after_log(logger, logging.WARNING),
    )
    def send(self, message: EmailMessage):
        payload = jsonable_encoder(message)
        try:
            response = self.api.post(self.SEND_MAIL_URL, json=payload)
            response.raise_for_status()
        except (httpx.TransportError, httpx.HTTPStatusError) as e:
            raise ServiceUnavailable() from e

    def notify_user_registered(self, user: schemas.UserV2):
        payload = dict(
            id=user.id,
            email=user.email,
            organization_id=user.organization.id,
        )
        try:
            response = self.api.put(
                self.PARTNER_USER_REGISTERED_URL, json=jsonable_encoder(payload)
            )
            response.raise_for_status()
        except (httpx.TransportError, httpx.HTTPStatusError) as e:
            raise ServiceUnavailable() from e

    def add_contact(self, user: schemas.UserV2) -> str:
        payload = dict(
            source=self.DEFAULT_SOURCE,
            source_reference=str(user.id),
            email=user.email,
            organization=user.organization.name,
            first_name=user.first_name,
            last_name=user.last_name,
            phone=user.mobile_phone,
        )
        try:
            response = self.api.post(self.CONTACTS_URL, json=jsonable_encoder(payload))
            response.raise_for_status()
            data = response.json()
            return data["contact_id"]
        except (httpx.TransportError, httpx.HTTPStatusError) as e:
            raise ServiceUnavailable(str(e))
