from abc import abstractmethod
from typing import ClassVar, Protocol

from app import schemas
from app.schemas.mail import EmailMessage


class AbstractMailAPI(Protocol):
    @abstractmethod
    def send(self, message: EmailMessage):
        ...


class AbstractPartnerAPI(Protocol):
    @abstractmethod
    def notify_user_registered(self, user: schemas.UserV2):
        ...


class AbstractContactAPI(Protocol):
    DEFAULT_SOURCE: ClassVar[str] = "CP"

    @abstractmethod
    def add_contact(self, user: schemas.UserV2) -> str:
        ...


class AbstractCoreFacade(
    AbstractContactAPI, AbstractMailAPI, AbstractPartnerAPI, Protocol
):
    ...
