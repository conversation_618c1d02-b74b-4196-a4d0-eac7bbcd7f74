from dataclasses import dataclass

from app import schemas
from app.adapters.core_api.base import AbstractCoreFacade
from app.schemas.mail import EmailMessage


@dataclass
class InMemoryCoreAPI(AbstractCoreFacade):
    def __init__(self):
        self.contacts: list[schemas.UserV2] = []
        self.mails: list[EmailMessage] = []
        self.notifications: dict[str, schemas.UserV2] = {}

    def add_contact(self, user: schemas.UserV2) -> str:
        self.contacts.append(user)
        return str(len(self.contacts))

    def send(self, message: EmailMessage):
        self.mails.append(message)

    def notify_user_registered(self, user: schemas.UserV2):
        self.notifications[user.email] = user
