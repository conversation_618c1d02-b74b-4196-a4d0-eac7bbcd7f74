import json
import logging
from functools import partial
from typing import Any, Callable, ContextManager, Optional

import httpx
import typer
from pydantic import EmailStr, SecretStr
from sqlalchemy.orm import Session

from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import GroupAlreadyExists
from app.adapters.iam.schemas import Role, UserCreate
from app.common.exceptions import NotFound
from app.core.config import settings
from app.core.utils import get_db, get_keycloak_iam
from app.enums import OrganizationRoleEnum
from app.models import Organization
from app.services import authz_apps, mappers, org_management
from app.typedefs import Username


def inject_iam(ctx: typer.Context):
    ctx.ensure_object(dict)
    ctx.obj["get_iam"] = get_keycloak_iam


def get_iam(ctx: typer.Context) -> ContextManager[AbstractIAM]:
    return ctx.obj["get_iam"]()


app = typer.Typer()
app.callback()(inject_iam)


@app.command()
def create_root_org(
    ctx: typer.Context,
    organization_name: str = typer.Option(
        ...,
        prompt=True,
        envvar=["ROOT_ORG_NAME"],
    ),
    admin_email: str = typer.Option(..., prompt=True, envvar=["ROOT_ORG_ADMIN_EMAIL"]),
    admin_password: str = typer.Option(
        ...,
        prompt=True,
        confirmation_prompt=True,
        hide_input=True,
        envvar=["ROOT_ORG_ADMIN_PASSWORD"],
    ),
    ignore_existent: bool = typer.Option(False, is_flag=True),
) -> None:
    with get_db() as db, get_iam(ctx) as iam:
        try:
            org_management.create_root_organization(
                db,
                iam,
                organization_name=organization_name,
                admin_email=admin_email,
                admin_password=admin_password,
            )
        except org_management.OrganizationExistsInDb:
            msg = f'Organization "{_b(organization_name)}" exists in DB.'
            if ignore_existent:
                typer.echo(f"{msg} Creating Keycloak instance...")
                org = (
                    db.query(Organization)
                    .filter(Organization.name.ilike(organization_name))  # noqa
                    .first()
                )
                if not org:
                    raise NotFound()
                try:
                    org_management.create_organization_group(iam, org)
                except GroupAlreadyExists as e:
                    typer.echo(str(e))
                raise typer.Exit(0)
            else:
                typer.echo(msg)
                raise typer.Abort()
    msg = "\n".join(
        [
            f'Organization "{_b(organization_name)}" successfully created.',
            f'User with Email "{_b(admin_email)}" successfully created',
        ]
    )
    typer.echo(msg)


@app.command()
def remove_root_org(
    ctx: typer.Context,
    organization_name: str = typer.Option(..., prompt=True),
    delete_users: bool = typer.Option(False, is_flag=True),
    no_input: bool = typer.Option(False, is_flag=True),
) -> None:
    styled_org_name = typer.style(organization_name, bold=True)
    if not no_input:
        msg = f"Delete {styled_org_name} organization"
        if delete_users:
            msg += " and all its users"
        msg += "?"
        typer.confirm(text=msg, abort=True)
    with get_db() as db, get_iam(ctx) as iam:
        org_management.remove_root_organization(
            db,
            iam,
            organization_name=organization_name,
            delete_users=delete_users,
        )
    msg = f"Organization {styled_org_name} was successfully removed."
    if delete_users:
        msg += " All users have been deleted."
    typer.echo(msg)


@app.command()
def create_user(
    ctx: typer.Context,
    email: str = typer.Option(..., prompt=True),
    password: Optional[str] = typer.Option(None, prompt=True, hide_input=True),
    is_enabled: bool = typer.Option(True, is_flag=True),
    email_verified: bool = typer.Option(True, is_flag=False),
) -> None:
    with get_iam(ctx) as iam:
        user = iam.users.add(
            UserCreate(
                username=Username(email),
                email=EmailStr(email),
                enabled=is_enabled,
                email_verified=email_verified,
            )
        )
        if password:
            iam.users.set_password(user.id, SecretStr(password), temporary=False)
    typer.echo(f"User with Email {_b(email)} was created.")


@app.command()
def import_realm(
    realm_json: typer.FileText = typer.Option(...),
    keycloak_user: str = typer.Option(..., prompt=True, envvar=["KEYCLOAK_USER"]),
    keycloak_password: str = typer.Option(
        ..., prompt=True, hide_input=True, envvar=["KEYCLOAK_PASSWORD"]
    ),
) -> None:
    realm_data = json.load(realm_json)
    access_token = _get_access_token(keycloak_user, keycloak_password)

    response = httpx.post(
        settings.KEYCLOAK_URL + "/auth/admin/realms",
        headers=dict(authorization=f"Bearer {access_token}"),
        json=realm_data,
    )
    response.raise_for_status()


def _get_access_token(keycloak_user: str, keycloak_password: str) -> str:
    token_path = "/auth/realms/master/protocol/openid-connect/token"  # nosec B105
    # This is a false positive.
    # That path is just a well-known Keycloak token endpoint, not a secret.
    response = httpx.post(
        settings.KEYCLOAK_URL + token_path,
        data=dict(
            grant_type="password",
            username=keycloak_user,
            password=keycloak_password,
            client_id="admin-cli",
        ),
    )
    response.raise_for_status()
    token_data = response.json()
    access_token = token_data["access_token"]
    if not isinstance(access_token, str):
        logging.error(f"access_token is not str {access_token}")
        raise ValueError("Bad token")
    return access_token


@app.command()
def delete_realm(
    realm_name: str = typer.Argument(...),
    keycloak_user: str = typer.Option(..., prompt=True, envvar=["KEYCLOAK_USER"]),
    keycloak_password: str = typer.Option(
        ..., prompt=True, hide_input=True, envvar=["KEYCLOAK_PASSWORD"]
    ),
) -> None:
    access_token = _get_access_token(keycloak_user, keycloak_password)
    response = httpx.delete(
        settings.KEYCLOAK_URL + f"/auth/admin/realms/{realm_name}",
        headers=dict(authorization=f"Bearer {access_token}"),
    )
    response.raise_for_status()


@app.command()
def setup_organizations_authz(ctx: typer.Context) -> None:
    with get_iam(ctx) as iam, get_db() as db:
        update_organization_group(iam, db)
        check_keycloak_resource(
            iam,
            resource_name=str(OrganizationRoleEnum.PARTNER_USER.value),
            resource_type="role",
            get_resource=lambda _iam: iam.roles.get(
                name=OrganizationRoleEnum.PARTNER_USER
            ),
            create_resource=lambda _iam: iam.roles.add(
                role=Role(name=str(OrganizationRoleEnum.PARTNER_USER.value)),
            ),
        )


def update_organization_group(iam: AbstractIAM, db: Session) -> None:
    """
    Updates group attributes in Keycloak according to the current app schema
    """
    org: Organization
    for org in db.query(Organization).order_by("id").all():
        existing_group = iam.groups.get(org.path)
        new_group = mappers.organization_to_group(org)
        typer.echo(f"Updating group for {org.name}: {new_group.attributes}")
        if existing_group:
            iam.groups.update(existing_group.path, new_group)
        else:
            iam.groups.add(
                new_group,
                parent_path=org.parent.path if org.parent else None,
            )


_b = partial(typer.style, bold=True)


# APPLICATIONS


@app.command()
def setup_applications_authz(ctx: typer.Context) -> None:
    with get_iam(ctx) as iam:
        check_keycloak_resource(
            iam,
            resource_name=authz_apps.APP_ROLE_NAME,
            resource_type="role",
            get_resource=authz_apps.get_application_role,
            create_resource=authz_apps.create_application_role,
        )
        check_keycloak_resource(
            iam,
            resource_name=authz_apps.APP_MANAGEMENT_SCOPE_NAME,
            resource_type="client scope",
            get_resource=authz_apps.get_app_management_scope,
            create_resource=authz_apps.create_app_management_scope,
        )


def check_keycloak_resource(
    iam: AbstractIAM,
    resource_name: str,
    resource_type: str,
    get_resource: Callable[[AbstractIAM], Any | None],
    create_resource: Callable[[AbstractIAM], Any],
):
    typer.echo(f'Checking "{resource_name}" {resource_type}...', nl=False)
    resource = get_resource(iam)
    if resource is not None:
        typer.echo("OK, found.")
    else:
        typer.echo("not found. Creating...", nl=False)
        create_resource(iam)
        typer.echo("OK, created.")


if __name__ == "__main__":
    app()
