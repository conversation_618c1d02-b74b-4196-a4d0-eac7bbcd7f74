from enum import Enum


class OrganizationRoleEnum(str, Enum):
    CLIENT_ADMIN = "ClientAdmin"
    CLIENT_USER = "ClientUser"
    DISTRIBUTOR_ADMIN = "DistributorAdmin"
    DISTRIBUTOR_USER = "DistributorUser"
    PARTNER_USER = "PartnerUser"


class OrganizationTypeEnum(str, Enum):
    DISTRIBUTOR = "DISTRIBUTOR"
    CLIENT = "CLIENT"
    PARTNER = "PARTNER"

    @property
    def roles(self) -> list[OrganizationRoleEnum]:
        if self is OrganizationTypeEnum.DISTRIBUTOR:
            return [
                OrganizationRoleEnum.DISTRIBUTOR_ADMIN,
                OrganizationRoleEnum.DISTRIBUTOR_USER,
            ]
        elif self is OrganizationTypeEnum.CLIENT:
            return [OrganizationRoleEnum.CLIENT_USER, OrganizationRoleEnum.CLIENT_ADMIN]
        elif self is OrganizationTypeEnum.PARTNER:
            return [OrganizationRoleEnum.PARTNER_USER]
        else:
            raise AssertionError(f'Missing roles for organization type: "{self.value}"')


class OrganizationScopeEnum(str, Enum):
    READ = "org:read"
    UPDATE = "org:update"
    CREATE_CHILD = "org:create-child"
    DELETE = "org:delete"


class FileReportStatusEnum(str, Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    COMPLETED_WITH_ERRORS = "completed_with_errors"


class UserStatus(str, Enum):
    ACTIVATED = "Activated"
    DEACTIVATED = "Deactivated"
