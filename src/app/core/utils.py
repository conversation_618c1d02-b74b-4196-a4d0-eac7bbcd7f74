import inspect
from collections.abc import Generator, Iterable
from contextlib import contextmanager
from enum import Enum
from typing import Any, Callable, Literal, TypeVar, overload

import httpx
import keycloak_client
from fastapi import HTTPException
from fastapi.dependencies.utils import get_param_field
from fastapi.openapi.constants import REF_PREFIX
from fastapi.params import ParamTypes
from pydantic.schema import add_field_type_to_schema, field_schema
from sqlalchemy import select
from sqlalchemy.orm import Session
from sqlalchemy.orm.attributes import InstrumentedAttribute
from sqlalchemy.sql import Select
from starlette import status

from app.adapters.iam.keycloak import KeycloakIAM
from app.core.config import settings
from app.db.base import Base
from app.db.session import SessionLocal

T = TypeVar("T")


@contextmanager
def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@contextmanager
def get_keycloak() -> Generator[keycloak_client.KeycloakAdmin, None, None]:
    with httpx.Client() as http:
        kc = keycloak_client.KeycloakAdmin(
            http,
            realm=settings.KEYCLOAK_REALM,
            keycloak_url=settings.KEYCLOAK_URL,
            client_id=settings.OIDC_CLIENT_ID,
            client_secret=settings.OIDC_CLIENT_SECRET,
        )
        yield kc


@contextmanager
def get_keycloak_iam() -> Generator[KeycloakIAM, None, None]:
    with get_keycloak() as ka:
        yield KeycloakIAM(ka)


ModelT = TypeVar("ModelT", bound=Base)


@overload
def get_object_or_404(
    db: Session,
    model_or_select: type[ModelT] | Select,
    is_many: Literal[True],
    *criteria: Any,
    **kwargs: Any,
) -> list[ModelT]:
    ...


@overload
def get_object_or_404(
    db: Session,
    model_or_select: InstrumentedAttribute,
    is_many: Literal[True],
    *criteria: Any,
    **kwargs: Any,
) -> list[Any]:
    ...


@overload
def get_object_or_404(
    db: Session,
    model_or_select: type[ModelT] | Select | InstrumentedAttribute,
    is_many: Literal[False],
    *criteria: Any,
    **kwargs: Any,
) -> ModelT:
    ...


def get_object_or_404(
    db: Session,
    model_or_select: type[ModelT] | Select | InstrumentedAttribute,
    is_many: Literal[True, False],
    *criteria: Any,
    **kwargs: Any,
) -> ModelT | list[ModelT] | list[Any]:
    obj: list[ModelT] | list[Any] | ModelT | None = get_object(
        db, model_or_select, is_many, *criteria, **kwargs
    )
    if not obj:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    return obj


@overload
def get_object(
    db: Session,
    model_or_select: type[ModelT] | Select,
    is_many: Literal[True],
    *criteria: Any,
    **kwargs: Any,
) -> list[ModelT]:
    ...


@overload
def get_object(
    db: Session,
    model_or_select: type[ModelT] | Select,
    is_many: Literal[False],
    *criteria: Any,
    **kwargs: Any,
) -> ModelT | None:
    ...


@overload
def get_object(
    db: Session,
    model_or_select: InstrumentedAttribute,
    is_many: Literal[True],
    *criteria: Any,
    **kwargs: Any,
) -> list[Any]:
    ...


def get_object(
    db: Session,
    model_or_select: type[ModelT] | Select | InstrumentedAttribute,
    is_many: Literal[True, False],
    *criteria: Any,
    **kwargs: Any,
) -> ModelT | None | list[ModelT] | list[Any]:
    if isinstance(model_or_select, Select):
        base_query = model_or_select
    elif isinstance(model_or_select, InstrumentedAttribute):
        base_query = select(model_or_select)
    else:
        if issubclass(model_or_select, Base):
            base_query = select(model_or_select)

    filtered_query = base_query.filter(*criteria).filter_by(**kwargs)
    if is_many:
        return db.execute(filtered_query).scalars().all()
    else:
        return db.execute(filtered_query).scalar()


def parse_int_csv(v: str | None) -> list[int] | None:
    if v is None:
        return None
    return list(map(int, v.split(",")))


def csv_parameter_schema(
    name: str,
    *,
    required: bool = False,
    item_type: type = int,
    title: str | None = None,
    in_: ParamTypes = ParamTypes.query,
) -> dict[str, Any]:
    item_schema: dict[str, Any] = {}
    add_field_type_to_schema(item_type, item_schema)
    return {
        "name": name,
        "required": required,
        "in": in_.value,
        "schema": {
            "title": title or name.capitalize(),
            "type": "array",
            "items": item_schema,
        },
        "style": "form",
        "explode": False,
    }


def enum_parameter_schema(
    name: str,
    *,
    required: bool = False,
    field_type: type[Enum],
    in_: ParamTypes = ParamTypes.query,
) -> dict[str, Any]:
    param_field = get_param_field(
        param=inspect.Parameter(
            name=name,
            kind=inspect.Parameter.KEYWORD_ONLY,
            annotation=field_type if required else field_type | None,
            default=inspect.Parameter.empty if required else None,
        ),
        param_name=name,
    )
    schema = field_schema(
        param_field,
        model_name_map={field_type: field_type.__name__},
        ref_prefix=REF_PREFIX,
    )[0]
    return {
        "name": name,
        "in": in_.value,
        "required": required,
        "schema": schema,
    }


# https://more-itertools.readthedocs.io/en/stable/_modules/more_itertools/recipes.html#quantify
def quantify(iterable: Iterable[T], pred: Callable[[T], bool] = bool) -> int:
    """Return the how many times the predicate is true.

    >>> quantify([True, False, True])
    2

    """
    return sum(map(pred, iterable))
