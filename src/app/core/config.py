import logging
import secrets
import sys
from enum import Enum
from logging.config import dictConfig
from typing import Any, Literal

import keycloak_client
import yaml
from pydantic import AnyHttpUrl, AnyUrl, BaseSettings, validator

from app import constants


class AppEnvironment(str, Enum):
    """Enum of application environments"""

    PROD = "prod"
    LOCAL = "local"
    CI_TEST = "test"  # TODO: no use, needs to be passed from CI


class MetaSettings(BaseSettings):
    """Critical settings for application to start"""

    DEBUG: bool = False
    LOG_LEVEL: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "INFO"
    APPLICATION_ENV: AppEnvironment

    class Config:
        case_sensitive = True


def init_logging(config_filename: str) -> None:
    """Initialize logging system based on the config"""
    otel_formatter = "default_with_otel"
    with open(config_filename, "r") as stream:
        logging_config = yaml.safe_load(stream)
        logging_config["loggers"]["root"]["level"] = meta_settings.LOG_LEVEL
        if OTEL_ENABLED:
            logging_config["handlers"]["console"]["formatter"] = otel_formatter
        dictConfig(logging_config)

    logging.info(f"Logging system enabled, {OTEL_ENABLED=}.")
    if meta_settings.DEBUG:
        logging.info(f"Settings {meta_settings}, {settings}")


class Settings(BaseSettings):
    ROOT_PATH: str = ""
    DATABASE_URI: AnyUrl | None = None

    # AUTH
    KEYCLOAK_URL: AnyHttpUrl
    KEYCLOAK_REALM: str
    AUDIT_LOG_URI: AnyHttpUrl

    OIDC_AUTHORIZATION_URL: AnyUrl | None = None
    OIDC_TOKEN_URL: AnyUrl | None = None
    OIDC_TOKEN_INTROSPECTION_URL: AnyUrl | None = None
    OIDC_JWKS_URL: AnyUrl | None = None

    OIDC_AUDIENCE: str

    OIDC_CLIENT_ID: str
    OIDC_CLIENT_SECRET: str

    OIDC_UI_CLIENT_ID: str
    DEV_MODE: str = "0"
    USER_GUIDELINE_LINK: str | None = None
    CP_ENV_NAME: str | None = None
    EMAIL_ENV: str | None = None

    # Get Version
    KC_SVC_USER_NAME: str | None = None
    KC_SVC_USER_PASSWORD: str | None = None
    DEBUG: bool = False

    # Create service account user
    SERVICE_ACCOUNT_PASSWORD: str = ""

    # Redis env vars
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379

    PORTAL_URL: str | None = "https://connectedplatform.net"
    PORTAL_NAME: str | None = "Connected Platform"

    # S3 variables
    S3_BUCKET_NAME: str | None = None
    S3_REGION: str | None = None
    S3_KEY_ID: str | None = None
    S3_SECRET_KEY: str | None = None
    S3_ENDPOINT_URL: str | None = None

    REDIS_DB: int | None = 0

    SEND_USER_REGISTRATION_MAIL: bool | None = False

    @validator("OIDC_AUTHORIZATION_URL", pre=True)
    def assemble_authorization_url(cls, v: str | None, values: dict[str, Any]) -> str:
        if isinstance(v, str):
            return v
        return _build_kc_url(values, keycloak_client.urls.URL_OIDC_AUTHORIZATION)

    @validator("OIDC_TOKEN_URL", pre=True)
    def assemble_token_url(cls, v: str | None, values: dict[str, Any]) -> str:
        if isinstance(v, str):
            return v
        return _build_kc_url(values, keycloak_client.urls.URL_OIDC_TOKEN)

    @validator("OIDC_TOKEN_INTROSPECTION_URL", pre=True)
    def assemble_token_introspection_url(
        cls, v: str | None, values: dict[str, Any]
    ) -> str:
        if isinstance(v, str):
            return v
        return _build_kc_url(values, keycloak_client.urls.URL_OIDC_TOKEN_INTROSPECTION)

    @validator("OIDC_JWKS_URL", pre=True)
    def assemble_jwks_url(cls, v: str | None, values: dict[str, Any]) -> str:
        if isinstance(v, str):
            return v
        return _build_kc_url(values, keycloak_client.urls.URL_OIDC_JWKS)

    # Core services
    EMAIL_DELIVERY_URL: AnyHttpUrl
    PARTNER_APPS_URL: AnyHttpUrl
    CONTACTS_URL: AnyHttpUrl

    LANDING_PAGE_URL: AnyHttpUrl
    EXTERNAL_LANDING_PAGE: AnyHttpUrl
    EXTERNAL_IDP: str
    EXTERNAL_IDP_DOMAIN_TO_REMOVE: str = "nextgenclearing.com"
    SIGN_OUT_URL: AnyHttpUrl | None = None
    PRE_AUTH_TOKEN_SECRET: str

    @validator("SIGN_OUT_URL", pre=True)
    def assemble_sign_out_url(cls, v: str | None, values: dict[str, Any]) -> str:
        if isinstance(v, str):
            return v
        return values["LANDING_PAGE_URL"] + "/oauth2/sign_out"

    HEALTH_CHECK_SECRET: str = secrets.token_urlsafe(32)

    class Config:
        case_sensitive = True
        env_file = ".env"


def _build_kc_url(values: dict[str, Any], path: str) -> str:
    realm_path = keycloak_client.urls.URL_REALM.format(
        realm=values.get("KEYCLOAK_REALM")
    )
    return "".join([values["KEYCLOAK_URL"], realm_path, path])


class DevSettings(Settings):
    """Application settings specific for dev environment"""

    APP_HOST: str = "localhost"
    APP_PORT: int = 8000
    DEV_MODE: str = "0"
    APPLICATION_ENV = "local"


TEST_WITH_PYTEST = "pytest" in sys.modules
OTEL_ENABLED = "opentelemetry" in sys.modules

meta_settings = MetaSettings()
if meta_settings.APPLICATION_ENV == AppEnvironment.LOCAL:
    settings_class = DevSettings  # type: ignore
else:
    settings_class = Settings  # type: ignore

ENV_FILENAME = "test.env" if TEST_WITH_PYTEST else ".env"
settings = settings_class(_env_file=ENV_FILENAME)  # type: ignore

init_logging(str(constants.APP_DIR.parent / "logging.yaml"))
