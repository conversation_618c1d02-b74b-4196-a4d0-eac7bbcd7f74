import logging
import os
import pathlib
from abc import ABC, abstractmethod
from datetime import date

from botocore.exceptions import ClientError, ParamValidationError, ValidationError
from mypy_boto3_s3 import S3ServiceResource

from app.common.exceptions import NotFound
from app.core.config import settings


class AbstractFileStorage(ABC):
    @abstractmethod
    def upload_file(
        self,
        filename: str,
        file_path: str | None = None,
        file_obj: bytes | None = None,
        content_type: str | None = None,
    ) -> str:
        ...

    @abstractmethod
    def generate_file_url(self, file_key: str) -> str:
        ...

    @abstractmethod
    def file_exists(self, file_key) -> bool:
        ...

    @abstractmethod
    def read_csvfile(self, filename: str) -> bytes:
        ...


class LocalFileStorage(AbstractFileStorage):
    def __init__(self, root_path: str):
        self.root_path = pathlib.Path(root_path)

    def upload_file(
        self,
        filename: str,
        file_path: str | None = None,
        file_obj: bytes | None = None,
        content_type: str | None = None,
    ) -> str:
        target_path = pathlib.Path(self.root_path, filename)
        target_path.parent.mkdir(parents=True, exist_ok=True)
        if file_path is not None:
            with open(file_path, "rb") as f:
                with open(target_path, "wb") as dest:
                    dest.write(f.read())
                    os.remove(file_path)

        elif file_obj is not None:
            with open(target_path, "wb") as dest:
                dest.write(file_obj)
        return filename

    def generate_file_url(self, file_key: str) -> str:
        file_path = self.root_path / file_key
        return f"http://{settings.APP_HOST}:{settings.APP_PORT}/{file_path}"

    def file_exists(self, file_key: str) -> bool:
        file_path = self.root_path / file_key
        return file_path.exists() and file_path.is_file()

    def read_csvfile(self, filename: str) -> bytes:
        full_path = pathlib.Path(self.root_path) / filename
        with open(full_path, "rb") as f:
            return f.read()


class S3FileStorage(AbstractFileStorage):
    page_size: int = 20
    expiry: int = 3600

    def __init__(self, client: S3ServiceResource, bucket_name: str):
        self.client = client
        self.bucket = self.client.Bucket(bucket_name)

    def upload_file(
        self,
        filename: str,
        file_path: str | None = None,
        file_obj: bytes | None = None,
        content_type: str | None = None,
    ) -> str:
        file_key = f"{date.today()}/{filename}"
        if file_path is not None:
            self.bucket.upload_file(file_path, file_key)
            os.remove(file_path)
        elif file_obj is not None:
            if content_type:
                self.bucket.put_object(
                    Key=filename, Body=file_obj, ContentType=content_type
                )
            else:
                self.bucket.put_object(Key=file_key, Body=file_obj)
        return file_key

    def generate_file_url(self, file_key: str) -> str:
        return self.client.meta.client.generate_presigned_url(
            "get_object",
            Params={"Bucket": self.bucket.name, "Key": file_key},
            ExpiresIn=self.expiry,
        )

    def file_exists(self, file_key: str) -> bool:
        # https://stackoverflow.com/a/44979500
        try:
            self.client.Object(self.bucket.name, file_key).load()
        except ClientError as e:
            if int(e.response["Error"]["Code"]) == 404:
                return False
            else:
                raise e
        except (ValidationError, ParamValidationError) as e:
            logging.error(str(e))
            return False
        return True

    def read_csvfile(self, key: str) -> bytes:
        try:
            obj = self.client.Object(self.bucket.name, key)
            response = obj.get()
            return response["Body"].read()
        except FileNotFoundError:
            raise NotFound()
        except ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchKey":
                raise NotFound()
            else:
                raise
