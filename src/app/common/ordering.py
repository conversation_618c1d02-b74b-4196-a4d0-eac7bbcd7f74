from enum import Enum
from typing import Callable

from fastapi import HTTPException, Query
from pydantic import BaseModel


class OrderDirection(str, Enum):
    ASC = "asc"
    DESC = "desc"


class Ordering(BaseModel):
    field: str
    order: OrderDirection

    class Config:
        json_encoders = {OrderDirection: lambda v: v.value}

    @classmethod
    def query(
        cls,
        default_ordering: str,
        ordering_fields: tuple[str, ...],
    ) -> Callable[[], "Ordering"]:
        """Create query parameter with field/-field options"""

        # Create human-readable description
        available_fields = ", ".join(
            f"'{f}' (use '-{f}' for descending)" for f in ordering_fields
        )
        description = f"Sort by field. Available options: {available_fields}"

        def dependency(
            ordering: str = Query(
                default=default_ordering,
                description=description,
                example=default_ordering,
            )
        ) -> "Ordering":
            """Parse sorting parameter"""
            field = ordering.lstrip("-")
            if field not in ordering_fields:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid ordering field '{field}'."
                    f"Must be one of: {', '.join(ordering_fields)}",
                )
            if ordering.startswith("-"):
                return Ordering(field=ordering[1:], order=OrderDirection.DESC)
            return Ordering(field=ordering, order=OrderDirection.ASC)

        return dependency
