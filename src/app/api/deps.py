import logging
import secrets
from collections.abc import Generator, Iterator
from functools import lru_cache
from typing import Any, Callable, TypeVar

import authn
import httpx
from fastapi import Depends, HTTPException, Request, status
from fastapi.security import OAuth2AuthorizationCodeBearer
from keycloak_client import KeycloakAdmin
from platform_api_client import PlatformAPIClient
from pydantic import BaseModel, ValidationError
from sqlalchemy.engine import Connectable
from sqlalchemy.orm import Session

from app import schemas
from app.adapters.core_api.http import CoreAPI
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.keycloak import KeycloakIAM
from app.adapters.platform_api_client.audit_service import AbstractAuditAPI, AuditAPI
from app.core.config import meta_settings, settings
from app.core.file_storage import AbstractFileStorage, LocalFileStorage, S3FileStorage
from app.db.session import SessionLocal, engine
from app.oso import oso
from app.s3 import get_s3_client
from app.services.core_api_client import InternalAPIClient
from app.services.health_check import get_healthy_secret

if not settings.OIDC_TOKEN_URL or not settings.OIDC_AUTHORIZATION_URL:
    raise ValueError("Missing configuration")

oauth2_scheme = OAuth2AuthorizationCodeBearer(
    tokenUrl=settings.OIDC_TOKEN_URL,
    authorizationUrl=settings.OIDC_AUTHORIZATION_URL,
)
logging.info(f"oauth2:{oauth2_scheme}")


def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


@lru_cache()
def get_bind() -> Connectable:
    return engine


def get_http_client() -> Generator[httpx.Client, None, None]:
    with httpx.Client() as client:
        yield client


def get_keycloak_admin(
    http_client: httpx.Client = Depends(get_http_client),
) -> KeycloakAdmin:
    return KeycloakAdmin(
        http_client,
        keycloak_url=settings.KEYCLOAK_URL,
        realm=settings.KEYCLOAK_REALM,
        client_id=settings.OIDC_CLIENT_ID,
        client_secret=settings.OIDC_CLIENT_SECRET,
    )


def get_iam(ka: KeycloakAdmin = Depends(get_keycloak_admin)) -> AbstractIAM:
    return KeycloakIAM(ka)


def introspect_token(token: str = Depends(oauth2_scheme)) -> dict[str, Any]:
    try:
        payload = authn.introspect_token(
            token=token,
            url=str(settings.OIDC_TOKEN_INTROSPECTION_URL),
            client_id=settings.OIDC_CLIENT_ID,
            client_secret=settings.OIDC_CLIENT_SECRET,
        )
        logging.warning(msg=f"introspect_token: payload -{payload}")
        logging.info(f"token: {token}")
        if not payload.get("active", False):
            raise authn.AuthNError("Invalid token")

        expected_type = "Bearer"
        token_type = payload.get("typ")
        if not token_type:
            raise authn.AuthNError('Invalid token: missing "typ"')
        elif token_type != expected_type:
            raise authn.AuthNError(
                f'Invalid token type: "{token_type}".' f" Expected: {expected_type}"
            )
    except authn.AuthNError as e:
        logging.warning(f"Bad token: {e}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
    else:
        return payload


Actor = schemas.UserInfo | schemas.ServiceInfo


def get_actor(
    payload: dict[str, Any] = Depends(introspect_token),
) -> Actor:
    if "client_id" in payload:
        return get_service_info(payload)
    return get_user_info(payload)


def get_user_info(
    payload: dict[str, Any] = Depends(introspect_token),
) -> schemas.UserInfo:
    return _parse_token_payload(payload, schemas.UserInfo)


def get_service_info(
    payload: dict[str, Any] = Depends(introspect_token),
) -> schemas.ServiceInfo:
    logging.warning(msg=f"get_service_info:{payload}")
    return _parse_token_payload(payload, schemas.ServiceInfo)


ActorInfoT = TypeVar("ActorInfoT", bound=BaseModel)


def _parse_token_payload(
    payload: dict[str, Any],
    schema: type[ActorInfoT],
) -> ActorInfoT:
    if not isinstance(payload, dict):
        logging.warning(f"Bad token payload for {schema}")
        raise ValueError("Bad token")
    try:
        obj = schema.parse_obj(payload)
    except ValidationError as e:
        logging.warning(f"Bad token payload for {schema}:{e}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED)
    else:
        return obj


def get_external_user_client(
    token: str = Depends(oauth2_scheme),
) -> Generator[PlatformAPIClient, None, None]:
    """Return HTTP client for making requests on behalf of the user/service"""

    with PlatformAPIClient(auth_token=token) as client:
        yield client


def get_audit_service(
    client: PlatformAPIClient = Depends(get_external_user_client),
) -> AbstractAuditAPI:
    return AuditAPI(client=client, base_url=settings.AUDIT_LOG_URI)


def healthy_secret(request: Request) -> None:
    """
    Raises HTTPException(404) if valid health check secret is missing.
    """

    secret = get_healthy_secret(request)
    # https://fastapi.tiangolo.com/advanced/security/http-basic-auth/?h=compare_digest#timing-attacks
    secret_is_valid = secrets.compare_digest(secret, settings.HEALTH_CHECK_SECRET)
    if not secret_is_valid:
        raise HTTPException(status_code=404)


def authorize_request(request: Request, actor: Actor = Depends(get_actor)) -> None:
    try:
        logging.warning(msg=f"authorize_request: actor-{actor}, request-{request}")
        oso.authorize_request(actor, request)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)


def get_core_api() -> Iterator[CoreAPI]:
    client = InternalAPIClient.create()
    try:
        yield CoreAPI(client)
    finally:
        client.close()


def get_file_storage_object(bucket: str | None) -> Callable[[], AbstractFileStorage]:
    def generate_storage_object() -> AbstractFileStorage:
        if meta_settings.APPLICATION_ENV == "local":
            return LocalFileStorage("static")
        elif meta_settings.APPLICATION_ENV == "prod" or settings.S3_KEY_ID:
            if not bucket:
                raise RuntimeError("No S3 Bucket provided")
            return S3FileStorage(client=get_s3_client(), bucket_name=bucket)
        else:
            return LocalFileStorage("static")

    return generate_storage_object
