import logging

from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import UUID4
from sqlalchemy.orm import Session
from starlette import status

from app import schemas
from app.adapters.core_api.base import AbstractMailAPI
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import UserNotFound
from app.adapters.platform_api_client.audit_service import AbstractAuditAPI
from app.adapters.platform_api_client.schemas import AuditLogModel
from app.api import deps
from app.common.ordering import Ordering
from app.common.pagination import InvalidPage, PaginatedResponse, Pagination
from app.constants import POST, USER
from app.core.config import settings
from app.core.utils import get_object_or_404
from app.db.redis_service import RedisService
from app.enums import OrganizationRoleEnum, OrganizationScopeEnum
from app.models import Organization
from app.oso import oso
from app.services.users import (
    add_user,
    fetch_all_users,
    fetch_organization_user,
    fetch_organization_users,
    fetch_user_roles,
    get_keycloak_user_response,
    send_email,
    update_user,
    validate_user_in,
)

logger = logging.getLogger(__name__)

organization_router = APIRouter()
users_router = APIRouter()


@organization_router.post(
    "/{org_id}/users",
    response_model=schemas.KeycloakUser,
    status_code=status.HTTP_201_CREATED,
)
def create_organization_user(
    db: Session = Depends(deps.get_db),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    iam: AbstractIAM = Depends(deps.get_iam),
    mail_api: AbstractMailAPI = Depends(deps.get_core_api),
    *,
    org_id: int,
    user_in: schemas.UserCreateV2,
    request: Request,
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
) -> schemas.KeycloakUser:
    """_summary_

    This API allows you to create a user with at least 1 role and up to 2 roles.
    """
    logging.info(f"Request body: {user_in}")
    target_org = validate_user_in(db, user_info, org_id, user_in)
    logging.info(f"target_org: {target_org}")
    user, user_idp = add_user(db, iam, user_info, user_in, target_org)
    logging.info(f"user: {user}, user_idp: {user_idp}")
    redis_service = RedisService()
    user_data = redis_service.get_user(user_id=user.id)
    message = AuditLogModel(
        ipAddress=request.client.host,
        user=user_info.email,
        payload={"id": user.id},
        event=POST,
        source=USER,
        request=f"v2/organizations/{org_id}/users",
    )
    audit_service.add_audit(message)
    logging.info("Audit has been added for User")
    roles = fetch_user_roles(iam, user.id, user_data)

    logging.info(f"roles: {roles}")
    if settings.SEND_USER_REGISTRATION_MAIL and (
        OrganizationRoleEnum.DISTRIBUTOR_USER.value in roles
    ):
        send_email(iam, mail_api, user)
    response = get_keycloak_user_response(user, roles, user_idp)
    logging.info(f"response: {response}")

    return response


@users_router.get(
    "",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[schemas.KeyCloakUserDetails],
)
def read_users(
    request: Request,
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    pagination: Pagination = Depends(Pagination.query()),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    search: str | None = None,
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    ordering: Ordering = Depends(
        Ordering.query(
            default_ordering="createdTimestamp",
            ordering_fields=(
                "createdTimestamp",
                "firstName",
                "lastName",
                "email",
                "lastLoginTimestamp",
            ),
        )
    ),
) -> PaginatedResponse[schemas.KeyCloakUserDetails]:
    try:
        oso.authorize_request(user_info, request)
        result = fetch_all_users(
            db=db,
            iam=iam,
            pagination=pagination,
            search=search,
            ordering=ordering,
            audit_service=audit_service,
        )
        return result
    except InvalidPage as e:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(e))
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except Exception as e:
        logger.error(f"read_users: Exception {e}")
        raise e


@organization_router.get(
    "/{org_id}/users",
    response_model=PaginatedResponse[schemas.KeyCloakUserDetails],
    tags=["users"],
)
def read_organization_users(
    org_id: int,
    search: str | None = None,
    pagination: Pagination = Depends(Pagination.query()),
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    ordering: Ordering = Depends(
        Ordering.query(
            default_ordering="createdTimestamp",
            ordering_fields=(
                "createdTimestamp",
                "firstName",
                "lastName",
                "email",
                "lastLoginTimestamp",
            ),
        )
    ),
) -> PaginatedResponse[schemas.KeyCloakUserDetails]:
    organization = get_object_or_404(db, Organization, False, id=org_id)
    logging.info(f"Organization Path: {organization.path}")
    try:
        oso.authorize(user_info, OrganizationScopeEnum.READ, organization)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    try:
        organization_users = fetch_organization_users(
            iam, organization, pagination, search, ordering, audit_service=audit_service
        )
        return organization_users
    except InvalidPage as e:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(e))


@organization_router.put(
    "/{org_id}/user/{user_id}",
    response_model=schemas.KeycloakUser,
    status_code=status.HTTP_200_OK,
    tags=["users"],
)
def update_organization_user(
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
    *,
    org_id: int,
    user_id: UUID4,
    user_in: schemas.UserUpdatev2,
) -> None:
    target_org = validate_user_in(db, actor, org_id, user_in)
    logging.info(f"target_org: {target_org}")
    redis_service = RedisService()
    redis_user = redis_service.get_user(user_id=user_id)

    if not redis_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    if not redis_user.get("groups") == str(target_org.external_id):
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    if redis_user.get("email") == settings.KC_SVC_USER_NAME:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)
    response = update_user(iam, redis_service, user_in, user_id, redis_user)
    return response


@organization_router.get(
    "/user/bulk/{user_id}",
    status_code=status.HTTP_200_OK,
)
def read_hash(user_id: str) -> None:
    redis_service = RedisService()
    response = redis_service.redis_client.hgetall(user_id)
    return response


@organization_router.delete(
    "/user/bulk/{user_id}",
    status_code=status.HTTP_200_OK,
)
def delete_hash(user_id: str) -> None:
    redis_service = RedisService()
    response = redis_service.redis_client.delete(user_id)
    return response


@organization_router.get(
    "/{org_id}/user/{user_id}",
    response_model=schemas.KeyCloakUserDetails,
    tags=["users"],
)
def read_organization_user(
    org_id: int,
    user_id: UUID4,
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
) -> schemas.KeyCloakUserDetails:
    organization = get_object_or_404(db, Organization, False, id=org_id)
    logging.info(f"Organization Path: {organization.path}")
    redis_service = RedisService()
    user_data = redis_service.get_user(user_id=user_id)

    try:
        oso.authorize(user_info, OrganizationScopeEnum.READ, organization)
        if not user_data:
            raise UserNotFound
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except UserNotFound as e:
        logging.error(f"User not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found.")
    user = fetch_organization_user(iam, organization, audit_service, user_id, user_data)
    return user
