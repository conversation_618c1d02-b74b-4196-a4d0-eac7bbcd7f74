from fastapi import APIRouter, Depends

from app.api import deps
from app.api.api_v1.endpoints import (
    access_token,
    health_check,
    identity_providers,
    organizations,
    partners,
    service_accounts,
    users,
)

api_router = APIRouter()
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(
    organizations.router, prefix="/organizations", tags=["organizations"]
)
api_router.include_router(service_accounts.router, tags=["service-accounts"])
api_router.include_router(
    partners.router,
    prefix="/partners",
    tags=["partners"],
)
api_router.include_router(
    identity_providers.router,
    prefix="/identity-providers",
    tags=["identity-providers"],
)
api_router.include_router(
    health_check.router,
    dependencies=[Depends(deps.healthy_secret)],
    include_in_schema=False,
)
api_router.include_router(
    access_token.router, prefix="/organizations", tags=["access token"]
)
