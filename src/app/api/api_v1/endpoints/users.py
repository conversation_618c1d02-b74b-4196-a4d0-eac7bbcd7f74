import copy
import csv
import io
import logging

from fastapi import APIRouter, Depends, HTTPException, Response, status
from fastapi.responses import StreamingResponse
from pydantic import UUID4
from sqlalchemy.orm import Session

from app import schemas
from app.adapters.core_api.base import AbstractMail<PERSON><PERSON>
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import UserAlreadyExist, UserNotFound, UsersNotFound
from app.adapters.iam.keycloak import KeycloakUserBrief
from app.adapters.iam.schemas import UserBrief, UserCreate
from app.adapters.platform_api_client.audit_service import AbstractAuditAPI
from app.api import deps
from app.constants import CSV_HEADERS, NEVER_LOGGED_IN
from app.core.config import settings
from app.core.utils import get_object_or_404
from app.enums import OrganizationRoleEnum, OrganizationScopeEnum, UserStatus
from app.models import Organization
from app.oso import oso
from app.services import authz, mappers, org_management
from app.services.authz_password import render_password_email
from app.services.users import fetch_all_users, fetch_organization_users

router = APIRouter()


@router.get("/me", response_model=schemas.User)
def read_authenticated_user(
    db: Session = Depends(deps.get_db),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
) -> schemas.User:
    organization = get_object_or_404(
        db, Organization, False, id=user_info.organization.id
    )
    return mappers.user_info_to_user(user_info, organization)


@router.post(
    "/pre-authorize",
    response_model=schemas.PreAuthResult,
    dependencies=[(Depends(deps.authorize_request))],
)
def pre_authorize_user(
    iam: AbstractIAM = Depends(deps.get_iam),
    db: Session = Depends(deps.get_db),
    *,
    ctx: schemas.PreAuthContext,
    service_info: schemas.ServiceInfo = Depends(deps.get_service_info),
) -> schemas.PreAuthResult:
    if service_info.client_id != settings.OIDC_CLIENT_ID:
        raise HTTPException(status.HTTP_403_FORBIDDEN)

    login_url = authz.pre_authorize_url(iam, db, ctx)
    return schemas.PreAuthResult(login_url=login_url)


@router.get(
    "/password-policies",
    response_model=list[schemas.PasswordPolicy],
    dependencies=[(Depends(deps.authorize_request))],
)
def read_password_policies(
    iam: AbstractIAM = Depends(deps.get_iam),
) -> list[schemas.PasswordPolicy]:
    policies = authz.get_password_policies(iam)
    return list(map(mappers.password_policy_to_schema, policies))


@router.get(
    "/{user_id}/organization",
    response_model=schemas.OrganizationFull,
    tags=["organizations"],
)
def read_user_organization(
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
    *,
    user_id: UUID4,
) -> Organization:
    try:
        organization = org_management.get_user_org(db, iam, user_id=user_id)
        if not organization:
            raise HTTPException(status.HTTP_404_NOT_FOUND)

        oso.authorize(actor, OrganizationScopeEnum.READ, organization)
    except (UserNotFound, oso.not_found_error):
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    else:
        return organization


@router.get(
    "",
    # TODO: replace with list[UserBrief]
    response_model=list[KeycloakUserBrief],
)
def read_users(
    service_info: schemas.ServiceInfo = Depends(deps.get_service_info),
    iam: AbstractIAM = Depends(deps.get_iam),
    *,
    email: str | None = None,
    first_name: str | None = None,
    last_name: str | None = None,
    exact: bool | None = None,
    search: str | None = None,
    email_verified: bool | None = None,
    enabled: bool | None = None,
    offset: int | None = None,
    limit: int | None = None,
) -> list[UserBrief]:
    """
    Returns list of all users. Users without email are skipped.
    """
    if not service_info.client_id == settings.OIDC_CLIENT_ID:
        raise HTTPException(status.HTTP_403_FORBIDDEN)

    users = iam.users.get_many(
        email=email,
        first_name=first_name,
        last_name=last_name,
        exact=exact,
        search=search,
        email_verified=email_verified,
        enabled=enabled,
        offset=offset,
        limit=limit,
    )

    return users


@router.post("", response_model=UUID4, dependencies=[(Depends(deps.authorize_request))])
def create_user(
    iam: AbstractIAM = Depends(deps.get_iam),
    *,
    user_in: schemas.KeycloakUserCreate,
) -> UUID4:
    try:
        user = iam.users.add(
            UserCreate(
                email=user_in.email,
                username=user_in.username,
                first_name=user_in.firstName,
                last_name=user_in.lastName,
                enabled=user_in.enabled,
                email_verified=user_in.emailVerified,
            )
        )
        return user.id
    except UserAlreadyExist:
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            f'User with name "{user_in.email}" already exists.',
        )


@router.get(
    "/{user_id}",
    response_model=schemas.KeycloakUser,
    dependencies=[(Depends(deps.authorize_request))],
)
def read_user(
    iam: AbstractIAM = Depends(deps.get_iam),
    db: Session = Depends(deps.get_db),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    *,
    user_id: UUID4,
) -> schemas.KeycloakUser:
    user = iam.users.get_by_id(user_id)
    if not user:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    authenticated_user_org = get_object_or_404(
        db, Organization, False, id=user_info.organization.id
    )
    user_org = org_management.get_user_org(db, iam, user_id=user_id)
    if not authenticated_user_org.parent:
        # top-level distributor has access to all users
        pass
    elif not user_org:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    else:
        try:
            oso.authorize(user_info, OrganizationScopeEnum.READ, user_org)
        except oso.not_found_error:
            raise HTTPException(status.HTTP_404_NOT_FOUND)

    roles = org_management.fetch_user_org_roles(iam, user.id)

    return schemas.KeycloakUser(
        id=user.id,
        email=user.email,
        username=user.username,
        firstName=user.first_name,
        lastName=user.last_name,
        enabled=user.enabled,
        emailVerified=user.email_verified,
        createdTimestamp=user.created_timestamp,
        roles=roles,
    )


@router.delete(
    "/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
    dependencies=[(Depends(deps.authorize_request))],
)
def delete_user(iam: AbstractIAM = Depends(deps.get_iam), *, user_id: UUID4) -> None:
    # TODO(sub-distributors): check org access
    user_in = iam.users.get_by_id(user_id)
    if user_in and user_in.email == settings.KC_SVC_USER_NAME:
        raise HTTPException(status.HTTP_400_BAD_REQUEST)
    try:
        iam.users.remove(user_id)
    except UserNotFound:
        raise HTTPException(status.HTTP_404_NOT_FOUND)


@router.post(
    "/{user_id}/password",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
    dependencies=[(Depends(deps.authorize_request))],
)
def reset_user_password(
    iam: AbstractIAM = Depends(deps.get_iam),
    mail_api: AbstractMailAPI = Depends(deps.get_core_api),
    *,
    user_id: UUID4,
    options: schemas.ResetPasswordOptions,
) -> None:
    user = iam.users.get_by_id(user_id)
    if not user:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    password = authz.generate_password(iam, email=user.email)
    iam.users.set_password(user.id, password=password, temporary=options.temporary)
    try:
        message = render_password_email(user, password)
        mail_api.send(message)
    except Exception as e:
        logging.error(f"Unable to send an email: {e}")
        raise


@router.get(
    "/export/file",
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(deps.authorize_request)],
)
def export_users_csv(
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    org_id: int | None = None,
) -> StreamingResponse:
    """
    Export all users as a CSV file.

    This endpoint retrieves all users from Redis and exports them as a CSV file.
    The CSV includes user details such as ID, email, first name, last name,
    organization, roles, and status.
    """

    if org_id:
        target_org = get_object_or_404(db, Organization, False, id=org_id)
        users = fetch_organization_users(
            iam,
            target_org,
            pagination=None,
            search=None,
            ordering=None,
            audit_service=audit_service,
        )
    else:
        users = fetch_all_users(
            db,
            iam,
            pagination=None,
            search=None,
            ordering=None,
            audit_service=audit_service,
        )
    if not users:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="No users found to export"
        )
    headers = copy.deepcopy(CSV_HEADERS)
    try:
        output = io.StringIO()
        writer = csv.writer(output)
        user_type_list = {role.value for role in OrganizationRoleEnum}
        rows = []
        is_user_role = False
        for data in users:
            usertype = list(set(data.roles) & user_type_list)
            userrole = list(set(data.roles) - user_type_list)
            row = [
                data.firstName,
                data.lastName,
                data.email,
                data.organization["name"] if data.organization else None,
                ",".join(usertype),
                data.lastLoginTimestamp if data.lastLoginTimestamp else NEVER_LOGGED_IN,
                data.createdTimestamp,
                data.createdBy,
                data.identityProvider.displayName if data.identityProvider else None,
                UserStatus.ACTIVATED if data.enabled else UserStatus.DEACTIVATED,
            ]
            if userrole and not is_user_role:
                is_user_role = True
                row.append(",".join(userrole))
                headers.append("User Roles")

            if org_id:
                row.pop(3)
            rows.append(row)

        if org_id:
            headers.pop(3)
        writer.writerow(headers)
        writer.writerows(rows)

        output.seek(0)
        return StreamingResponse(
            iter([output.getvalue()]),
            media_type="text/csv",
            headers={"Content-Disposition": "attachment; filename=users_export.csv"},
        )

    except Exception as e:
        logging.error(f"export_users_csv: Exception {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export users: {str(e)}",
        )


@router.get(
    "/{user_id}/revoke-token",
    status_code=status.HTTP_200_OK,
    dependencies=[(Depends(deps.authorize_request))],
)
def revoke_token(
    user_id: UUID4,
    iam: AbstractIAM = Depends(deps.get_iam),
) -> bool:
    try:
        return iam.users.revoke_token(user_id)
    except UsersNotFound as e:
        logging.error(f"User not found error.: {str(e)}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="User not found.")
    except ValueError as e:
        logging.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process your request."
        )
    except Exception as e:
        logging.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
