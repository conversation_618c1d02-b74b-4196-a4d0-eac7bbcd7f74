import concurrent.futures
import logging
import uuid
from functools import partial
from typing import cast

from fastapi import (
    APIR<PERSON><PERSON>,
    BackgroundTasks,
    Depends,
    File,
    HTTPException,
    Query,
    Request,
    Response,
    UploadFile,
)
from fastapi.exceptions import RequestValidationError
from pydantic import UUID4
from pydantic.error_wrappers import <PERSON><PERSON>r<PERSON><PERSON>per
from sqlalchemy import or_
from sqlalchemy.orm import Session
from sqlalchemy.sql.elements import ClauseElement
from starlette import status

from app import schemas
from app.adapters.core_api.base import AbstractCoreFacade
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.exc import UserNotFound
from app.adapters.iam.schemas import IdentityProvider, UserCreate
from app.adapters.platform_api_client.audit_service import AbstractAuditAPI
from app.adapters.platform_api_client.schemas import AuditLogModel
from app.api import deps
from app.common.exceptions import NotFound
from app.common.pagination import InvalidPage, PaginatedResponse, Pagination
from app.constants import DELETE, ORGANIZATION, PATCH, POST, PUT, USER
from app.core.config import settings
from app.core.file_storage import AbstractFileStorage
from app.core.utils import (
    csv_parameter_schema,
    enum_parameter_schema,
    get_object_or_404,
    parse_int_csv,
)
from app.enums import OrganizationRoleEnum, OrganizationScopeEnum, OrganizationTypeEnum
from app.models import Client, Distributor, Domain, Organization, PMNCode
from app.oso import oso
from app.services import mappers, org_management, validation
from app.services.mappers import alias_to_identity_provider
from app.typedefs import UserDict

logger = logging.getLogger(__name__)
router = APIRouter()


# in this
@router.get(
    "/descendants",
    response_model=PaginatedResponse,
)
def read_organization_descendants(
    db: Session = Depends(deps.get_db),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    iam: AbstractIAM = Depends(deps.get_iam),
    pagination: Pagination = Depends(Pagination.query()),
    *,
    org_id: int
    | None = Query(
        None,
        description=(
            "ID of the top-level ancestor organization. "
            "The current user's organization is used by default."
        ),
    ),
    descendant_type: OrganizationTypeEnum | None = None,
    search: str | None = None,
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
) -> PaginatedResponse:
    org_id = org_id or user_info.organization.id
    organization = get_object_or_404(db, Distributor, False, id=org_id)
    try:
        oso.authorize(user_info, OrganizationScopeEnum.READ, organization)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    criteria: list[ClauseElement] = []
    if search:
        criteria.append(
            or_(
                Organization.name.ilike(f"%{search}%"),
                PMNCode.pmn_code.ilike(f"%{search}%"),
            )
        )
    if descendant_type:
        criteria.append(Organization.type == descendant_type)
    descendants = org_management.get_descendants(
        db,
        organization,
        pagination.page,
        pagination.page_size,
        False,
        Organization.created_at,
        *criteria,
    )
    logging.info(f"descendants: {descendants}")
    total_count = org_management.get_descendants(
        db, organization, pagination.page, pagination.page_size, True, None, *criteria
    )
    to_schema = mappers.create_descendant_mapper(
        get_users=partial(org_management.extract_org_members),
        get_impersonation_url=partial(org_management.get_impersonation_url, iam),
        get_created_by=partial(org_management.extract_org_created_by, audit_service),
    )
    results = parallel_map(to_schema, descendants)
    try:
        return PaginatedResponse.from_iterable(
            pagination=pagination, results=results, totalCount=total_count
        )
    except InvalidPage as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


def parallel_map(to_schema, descendants, max_workers=4):
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        result = list(executor.map(lambda d: to_schema(*d), descendants))
    return result


@router.get(
    path="",
    response_model=list[schemas.OrganizationSimple],
    openapi_extra={
        "parameters": [
            csv_parameter_schema("org_id"),
            enum_parameter_schema(
                "org_type", field_type=OrganizationTypeEnum, required=False
            ),
        ]
    },
)
def read_organizations(
    db: Session = Depends(deps.get_db),
    actor: deps.Actor = Depends(deps.get_actor),
    *,
    org_id: str | None = None,
    org_type: OrganizationTypeEnum | None = None,
    page: int | None = None,
    page_size: int | None = None,
) -> list[Organization]:
    match actor:
        case schemas.UserInfo(organization=user_org):
            target_organization = get_object_or_404(
                db, Organization, False, id=user_org.id
            )
        case schemas.ServiceInfo():
            target_organization = get_object_or_404(
                db, Organization, False, parent=None
            )
        case _:
            raise ValueError("expected UserInfo or ServiceInfo")
    try:
        oso.authorize(actor, OrganizationScopeEnum.READ, target_organization)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    criteria = []

    try:
        organization_ids = parse_int_csv(org_id)
    except ValueError:
        raise HTTPException(
            status_code=422,
            detail="org_id must be a comma-separated list of integers or empty.",
        )
    organization_ids = parse_int_csv(org_id)
    if organization_ids:
        criteria.append(Organization.id.in_(organization_ids))  # noqa
    if org_type is not None:
        criteria.append(Organization.type == org_type)
    organizations = org_management.get_descendants(
        db,
        target_organization,
        page,
        page_size,
        False,
        Organization.created_at,
        *criteria,
    )
    orgs = [row[0] for row in organizations]
    return orgs


@router.put(
    "/clients/{org_id}",
    response_model=schemas.OrganizationFull,
)
def update_client_organization(
    request: Request,
    db: Session = Depends(deps.get_db),
    actor: deps.Actor = Depends(deps.get_actor),
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    *,
    org_id: int,
    client_org_in: schemas.OrganizationUpdate,
) -> schemas.OrganizationFull:
    client_org = get_object_or_404(db, Client, False, id=org_id)
    try:
        oso.authorize(actor, OrganizationScopeEnum.UPDATE, client_org)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    try:
        organization = org_management.update_organization(
            db,
            iam,
            organization=client_org,
            organization_in=client_org_in,
        )
    except org_management.OrganizationExistsInDb:
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            f'Organization with name "{client_org_in.name}" already exists.',
        )
    except org_management.PMNCodeAlreadyExists as ex:
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            f"Duplicate PMN code {ex.pmn_codes}, already exists for a client",
        )
    if not oso.is_allowed(actor, OrganizationScopeEnum.READ, client_org.parent):
        del client_org.parent
    message = AuditLogModel(
        ipAddress=request.client.host,
        payload={"id": f"{org_id}"},
        user=user_info.email,
        request=f"organization/clients/{org_id}",
        event="PUT",
        source=ORGANIZATION,
    )
    audit_service.add_audit(message)
    return organization


@router.get(
    "/{org_id}",
    response_model=schemas.OrganizationFull,
)
def read_organization(
    db: Session = Depends(deps.get_db),
    actor: deps.Actor = Depends(deps.get_actor),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    *,
    org_id: int,
) -> schemas.OrganizationFull:
    organization = get_object_or_404(db, Organization, False, id=org_id)
    try:
        oso.authorize(actor, OrganizationScopeEnum.READ, organization)
    except (oso.forbidden_error, oso.not_found_error):
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    result = schemas.OrganizationFull.from_orm(organization)
    result.pmn_codes = cast(
        list[str], org_management.get_object(db, PMNCode.pmn_code, True, org_id=org_id)
    )
    result.allowed_domains = cast(
        list[str],
        org_management.get_object(db, Domain.domain_name, True, org_id=org_id),
    )
    created_by = audit_service.get_audit(id=org_id, event=POST)
    result.created_by = created_by.user if created_by else None

    return result


@router.delete(
    "/{org_id}",
    response_class=Response,
    status_code=status.HTTP_204_NO_CONTENT,
)
def delete_organization(
    request: Request,
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    *,
    org_id: int,
) -> None:
    # Only Client can be deleted
    client = get_object_or_404(db, Client, False, id=org_id)
    try:
        oso.authorize(user_info, OrganizationScopeEnum.DELETE, client)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    message = AuditLogModel(
        ipAddress=request.client.host,
        payload={"id": f"{org_id}"},
        user=user_info.email,
        request=f"organization/{org_id}",
        event=DELETE,
        source=USER,
    )
    audit_service.add_audit(message)
    org_management.delete_organization(db, iam, organization=client, delete_users=True)


@router.post(
    "/{org_id}/clients",
    status_code=status.HTTP_201_CREATED,
    response_model=schemas.OrganizationFull,
)
def create_client(
    request: Request,
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    *,
    org_id: int,
    organization_in: schemas.OrganizationCreate,
) -> schemas.OrganizationFull:
    parent = get_object_or_404(db, Distributor, False, id=org_id)
    try:
        oso.authorize(actor, OrganizationScopeEnum.CREATE_CHILD, parent)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    try:
        organization = org_management.create_organization(
            db,
            iam,
            organization_in=organization_in,
            parent=parent,
            org_type=OrganizationTypeEnum.CLIENT,
        )
    except org_management.OrganizationExistsInDb:
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            f'Organization with name "{organization_in.name}" already exists.',
        )
    except org_management.PMNCodeAlreadyExists as ex:
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            f"Duplicate PMN code {ex.pmn_codes}, already exists for a client",
        )
    message = AuditLogModel(
        ipAddress=request.client.host,
        payload={"id": f"{organization.id}"},
        user=user_info.email,
        request=f"organization/{org_id}/clients",
        event=POST,
        source=ORGANIZATION,
    )
    audit_service.add_audit(message)
    return organization


@router.get(
    "/{org_id}/users",
    response_model=list[schemas.KeycloakUser],
    tags=["users"],
)
def read_organization_users(
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    *,
    org_id: int,
) -> list[UserDict]:
    organization = get_object_or_404(db, Organization, False, id=org_id)
    logging.info(f"Organization Path: {organization.path}")
    try:
        oso.authorize(user_info, OrganizationScopeEnum.READ, organization)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    members = org_management.get_members(iam, organization, fetch_roles=True)
    return members


@router.get(
    "/{org_id}/users/roles",
    response_model=list[schemas.OrganizationRole],
    tags=["users"],
)
def read_organization_roles(
    db: Session = Depends(deps.get_db),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    *,
    org_id: int,
) -> list[schemas.OrganizationRole]:
    organization = get_object_or_404(db, Organization, False, id=org_id)
    try:
        oso.authorize(user_info, OrganizationScopeEnum.READ, organization)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    allowed_roles = org_management.get_allowed_roles(organization)

    default_role = "User"
    roles = sorted(allowed_roles, key=lambda r: r.display_name != default_role)

    return list(roles)


# need to add the autorizer of oso in this
@router.get(
    "/{org_id}/users/{user_id}",
    response_model=schemas.KeycloakUser,
    tags=["users"],
)
def read_organization_user(
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    *,
    org_id: int,
    user_id: UUID4,
) -> schemas.KeycloakUser:
    # TODO: add the ability to operate on all descendants
    organization = get_object_or_404(db, Organization, False, id=org_id)
    logging.info(f"Organization Path: {organization.path}")
    try:
        oso.authorize(user_info, OrganizationScopeEnum.READ, organization)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    try:
        user_groups = iam.users.get_groups(user_id)
    except UserNotFound:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    for group in user_groups:
        if group.path == organization.path:
            if not org_management.is_organization_group(group):
                raise ValueError(f"Expected organization group but got: {group}")
            break
    else:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    iam_user = iam.users.get_by_id(user_id)
    if not iam_user:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    roles = org_management.fetch_user_org_roles(iam, user_id)

    # TODO: IdP requests duplication
    user_idp = alias_to_identity_provider(iam, iam_user.identity_provider)
    user = schemas.KeycloakUser(
        id=iam_user.id,
        enabled=iam_user.enabled,
        emailVerified=iam_user.email_verified,
        createdTimestamp=iam_user.created_timestamp,
        email=iam_user.email,
        username=iam_user.username,
        firstName=iam_user.first_name,
        lastName=iam_user.last_name,
        roles=roles,
        identityProvider=user_idp,
    )
    return user


ERR_USER_IS_A_MEMBER_WITH_EMAIL = (
    "The user with the email address {email}"
    " is already a member of another organization."
)
ERR_USER_IS_A_MEMBER_WITH_EMAIL_AND_ORG_NAME = (
    "The user with the email address {email}"
    " is already a member of the {org_name}"
    " organization."
)

ERR_BAD_ROLE_FOR_ORG_TYPE = (
    # TODO: -> Role "(role)" in organization (id?name?) doesn't exist
    'Bad role for organization of type "{org_type}": "{role_name}"'
)


@router.post(
    "/{org_id}/users",
    response_model=schemas.KeycloakUser,
    status_code=status.HTTP_201_CREATED,
    tags=["users"],
)
def create_organization_user(
    request: Request,
    db: Session = Depends(deps.get_db),
    actor: deps.Actor = Depends(deps.get_actor),
    core_api: AbstractCoreFacade = Depends(deps.get_core_api),
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    *,
    org_id: int,
    user_in: schemas.UserCreate,
) -> schemas.KeycloakUser:
    target_org = get_object_or_404(db, Organization, False, id=org_id)
    try:
        oso.authorize(actor, OrganizationScopeEnum.UPDATE, target_org)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    check_org_role(target_org, user_in.role)

    user = iam.users.get_by_email(user_in.email)
    if user:
        existing_org = org_management.get_user_org(db, iam, user_id=user.id)
        if not existing_org:
            # TODO: Review after custom sign in is released
            # Covers use case when user was created automatically after login
            # via IdP
            pass
        elif oso.is_allowed(actor, OrganizationScopeEnum.READ, existing_org):
            error_msg = ERR_USER_IS_A_MEMBER_WITH_EMAIL_AND_ORG_NAME.format(
                org_name=existing_org.name, email=user_in.email
            )
            raise HTTPException(status.HTTP_409_CONFLICT, error_msg)
        else:
            error_msg = ERR_USER_IS_A_MEMBER_WITH_EMAIL.format(email=user_in.email)
            raise HTTPException(status.HTTP_409_CONFLICT, error_msg)
    else:
        user_schema = UserCreate(
            username=user_in.username,
            email=user_in.email,
            email_verified=user_in.emailVerified,
            enabled=user_in.enabled,
            first_name=user_in.firstName,
            last_name=user_in.lastName,
        )
        user = iam.users.add(user_schema)

    if user_in.identityProvider:
        identity_provider = validate_identity_provider(
            iam,
            user_in.identityProvider,
        )
        user.set_identity_provider(identity_provider.alias)
        iam.users.update(user.to_update())

    org_management.add_user_to_organization(
        iam, organization=target_org, user_id=user.id, organization_role=user_in.role
    )
    roles = org_management.fetch_user_org_roles(iam, user.id)
    user_v2 = schemas.UserV2(
        **user.dict(),
        organization=schemas.OrganizationSimple.from_orm(target_org),
    )
    message = AuditLogModel(
        ipAddress=request.client.host,
        payload={"id": f"{user.id}"},
        user=user_info.email,
        request=f"organization/{org_id}/users",
        event=POST,
        source=ORGANIZATION,
    )
    audit_service.add_audit(message)
    try:
        core_api.add_contact(user_v2)
    except Exception as e:
        logger.error(str(e))

    # TODO: IdP requests duplication
    user_idp = alias_to_identity_provider(iam, user.identity_provider)
    return schemas.KeycloakUser(
        id=user.id,
        email=user.email,
        username=user.username,
        firstName=user.first_name,
        lastName=user.last_name,
        enabled=user.enabled,
        emailVerified=user.email_verified,
        createdTimestamp=user.created_timestamp,
        roles=roles,
        identityProvider=user_idp,
    )


def check_org_role(
    organization: Organization,
    role: OrganizationRoleEnum,
) -> None:
    # TODO: use org_management.get_allowed_roles
    if role not in organization.type.roles:
        error_msg = ERR_BAD_ROLE_FOR_ORG_TYPE.format(
            role_name=role.value,
            org_type=organization.type.value,
        )
        raise HTTPException(status.HTTP_400_BAD_REQUEST, error_msg)


def validate_identity_provider(
    iam: AbstractIAM,
    idp_alias: str,
) -> IdentityProvider:
    """
    Checks whether provided idp exists in IAM service
    """
    idp = iam.identity_providers.get_by_alias(idp_alias)
    if idp is None:
        error = ErrorWrapper(
            ValueError(f"Bad identity provider: {idp_alias}"),
            loc=("body", "identityProvider"),
        )
        raise RequestValidationError(errors=[error])
    return idp


ERR_USER_IS_A_MEMBER = "The user is already a member of another organization."

ERR_USER_IS_A_MEMBER_WITH_ORG_NAME = (
    "The user is already a member of the {org_name} organization."
)


@router.put(
    "/{org_id}/users/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
    tags=["users"],
)
def set_organization_membership(
    request: Request,
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    *,
    org_id: int,
    user_id: UUID4,
    membership: schemas.OrganizationMembership,
) -> None:
    target_org = get_object_or_404(db, Organization, False, id=org_id)
    if not oso.is_allowed(user_info, OrganizationScopeEnum.UPDATE, target_org):
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    user_in = iam.users.get_by_id(user_id)
    if user_in and user_in.email == settings.KC_SVC_USER_NAME:
        raise HTTPException(status.HTTP_400_BAD_REQUEST)
    check_org_role(target_org, membership.role)
    try:
        user_org = org_management.get_user_org(db, iam, user_id=user_id)
    except UserNotFound:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    if not user_org:
        pass
    elif user_org.id == target_org.id:
        org_management.remove_user_roles(iam, user_id=user_id)
    elif oso.is_allowed(user_info, OrganizationScopeEnum.READ, user_org):
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            ERR_USER_IS_A_MEMBER_WITH_ORG_NAME.format(org_name=user_org.name),
        )
    else:
        raise HTTPException(status.HTTP_409_CONFLICT, ERR_USER_IS_A_MEMBER)

    org_management.add_user_to_organization(
        iam,
        organization=target_org,
        user_id=user_id,
        organization_role=membership.role,
    )
    message = AuditLogModel(
        ipAddress=request.client.host,
        payload={"id": f"{user_id}"},
        user=user_info.email,
        request=f"organization/{org_id}/users/{user_id}",
        event=PUT,
        source=USER,
    )
    audit_service.add_audit(message)


@router.patch(
    "/{org_id}/users/{user_id}",
    response_model=schemas.KeycloakUser,
    tags=["users"],
)
def update_organization_user(
    request: Request,
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    actor: deps.Actor = Depends(deps.get_actor),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    *,
    org_id: int,
    user_id: UUID4,
    data: schemas.UserUpdate,
) -> schemas.KeycloakUser:
    """
    Patch-update method for organization user (currently, supports only
    `identityProvider` field)
    """
    target_org = get_object_or_404(db, Organization, False, id=org_id)
    try:
        oso.authorize(actor, OrganizationScopeEnum.UPDATE, target_org)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    user = iam.users.get_by_id(user_id)

    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    if settings.KC_SVC_USER_NAME == user.username and user:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

    fields_to_update = data.dict(exclude_unset=True)
    if "identityProvider" not in fields_to_update:
        pass
    elif data.identityProvider is None:
        # `identityProvider` was passed with `null` value
        user.remove_identity_provider()
    else:
        idp = validate_identity_provider(iam, data.identityProvider)
        user.set_identity_provider(idp.alias)

    if "username" not in fields_to_update:
        pass
    elif data.username is None:
        # ignore the passed `null`
        pass
    else:
        user.username = data.username

    user = iam.users.update(user.to_update())
    roles = org_management.fetch_user_org_roles(iam, user.id)

    # TODO: IdP requests duplication
    user_idp = alias_to_identity_provider(iam, user.identity_provider)
    message = AuditLogModel(
        ipAddress=request.client.host,
        payload={"id": f"{user_id}"},
        user=user_info.email,
        request=f"organization/{org_id}/users/{user_id}",
        event=PATCH,
        source=USER,
    )
    audit_service.add_audit(message)
    return schemas.KeycloakUser(
        id=user.id,
        email=user.email,
        username=user.username,
        firstName=user.first_name,
        lastName=user.last_name,
        enabled=user.enabled,
        emailVerified=user.email_verified,
        createdTimestamp=user.created_timestamp,
        roles=roles,
        identityProvider=user_idp,
    )


ERR_USER_SELF_REMOVE_ATTEMPT = "It is forbidden to remove yourself."


@router.delete(
    "/{org_id}/users/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
)
def remove_organization_member(
    request: Request,
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
    *,
    org_id: int,
    user_id: UUID4,
) -> None:
    organization = get_object_or_404(db, Organization, False, id=org_id)
    try:
        oso.authorize(user_info, OrganizationScopeEnum.UPDATE, organization)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    if user_info.id == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=ERR_USER_SELF_REMOVE_ATTEMPT,
        )
    user_in = iam.users.get_by_id(user_id)
    if user_in and user_in.email == settings.KC_SVC_USER_NAME:
        raise HTTPException(status.HTTP_400_BAD_REQUEST)

    try:
        user_organization = org_management.get_user_org(db, iam, user_id=user_id)
        if not user_organization or user_organization != organization:
            raise HTTPException(status.HTTP_404_NOT_FOUND)
        iam.users.remove(user_id)
        message = AuditLogModel(
            ipAddress=request.client.host,
            payload={"id": f"{user_id}"},
            user=user_info.email,
            request=f"organization/{org_id}/users/{user_id}",
            event=DELETE,
            source=USER,
        )
        audit_service.add_audit(message)
    except UserNotFound:
        raise HTTPException(status.HTTP_404_NOT_FOUND)


@router.post(
    "/{org_id}/users/bulk",
    response_model=schemas.BulkUserCreateResponse,
    status_code=status.HTTP_202_ACCEPTED,
    tags=["organization"],
)
def create_bulk_organization_users(
    background_tasks: BackgroundTasks,
    org_id: int,
    data: schemas.BulkUserCreateRequest,
    request: Request,
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    storage: AbstractFileStorage = Depends(
        deps.get_file_storage_object(settings.S3_BUCKET_NAME)
    ),
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
) -> schemas.BulkUserCreateResponse:
    """
    Create multiple users in an organization by uploading a CSV file.
    #"""
    target_org = get_object_or_404(db, Organization, False, id=org_id)
    try:
        oso.authorize(user_info, OrganizationScopeEnum.UPDATE, target_org)

    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    if data.identityProvider:
        validate_identity_provider(iam, data.identityProvider)

    try:
        response = storage.read_csvfile(data.fileName)
        message_for_user_creation = AuditLogModel(
            ipAddress=request.client.host,
            user=user_info.email,
            request=f"organization/{org_id}/users/bulk",
            event=POST,
            source=USER,
            payload=None,
        )
        user_file_id = org_management.process_bulk_user_creation(
            db=db,
            iam=iam,
            organization=target_org,
            response=response,
            data=data,
            background_tasks=background_tasks,
            audit_service=audit_service,
            message=message_for_user_creation,
            user_info=user_info,
        )
        message_for_user_creation.payload = {"id": user_file_id}
        audit_service.add_audit(message_for_user_creation)

        return schemas.BulkUserCreateResponse(userFileId=user_file_id)
    except NotFound:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        logger.error(f"Error processing bulk user creation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error processing CSV file",
        )


@router.post(
    "/document",
    response_model=schemas.CSVUploadResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["users"],
)
def upload_users_csv(
    db: Session = Depends(deps.get_db),
    file: UploadFile = File(...),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    storage: AbstractFileStorage = Depends(
        deps.get_file_storage_object(settings.S3_BUCKET_NAME)
    ),
) -> schemas.CSVUploadResponse:
    """
    Upload a CSV file with user data.

    """
    target_org = get_object_or_404(
        db, Organization, False, id=user_info.organization.id
    )

    try:
        oso.authorize(user_info, OrganizationScopeEnum.UPDATE, target_org)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    try:
        csv_validator = validation.CSV(file=file)
        validation_result = csv_validator.validate()
        filename = f"{str(uuid.uuid4())[:6]}_{file.filename}"
        s3_key = storage.upload_file(
            filename=filename,
            file_obj=validation_result["content"].encode("utf-8"),
            content_type="text/csv",
        )
        return schemas.CSVUploadResponse(
            key=s3_key, fileName=filename, url=storage.generate_file_url(s3_key)
        )

    except HTTPException:
        # Re-raise HTTP exceptions from the validator
        raise
    except Exception as e:
        logger.error(f"Error processing CSV: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing CSV file: {str(e)}",
        )


@router.get(
    "/{org_id}/users/bulk/status",
    response_model=PaginatedResponse[schemas.BulkUserNotificationStatus],
    tags=["users"],
)
def read_bulk_user_file_status(
    db: Session = Depends(deps.get_db),
    actor: deps.Actor = Depends(deps.get_actor),
    pagination: Pagination = Depends(Pagination.query()),
    *,
    org_id: int,
    audit_service: AbstractAuditAPI = Depends(deps.get_audit_service),
) -> PaginatedResponse[schemas.BulkUserNotificationStatus]:
    target_org = get_object_or_404(db, Organization, False, id=org_id)
    try:
        oso.authorize(actor, OrganizationScopeEnum.READ, target_org)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    try:
        results, total_count = org_management.get_user_file_status(
            db=db,
            organization_id=org_id,
            pagination=pagination,
            audit_service=audit_service,
        )

        return PaginatedResponse.from_iterable(
            pagination=pagination, results=results, totalCount=total_count
        )
    except NotFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.get(
    "/{org_id}/users/bulk/{user_file_id}",
    response_model=schemas.BulkUserNotificationDetail,
    tags=["users"],
)
def read_bulk_user_file_reports(
    db: Session = Depends(deps.get_db),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    *,
    org_id: int,
    user_file_id: UUID4,
) -> schemas.BulkUserNotificationDetail:
    """
    Get detailed information about a specific bulk user creation notification.
    """
    target_org = get_object_or_404(db, Organization, False, id=org_id)
    try:
        oso.authorize(user_info, OrganizationScopeEnum.UPDATE, target_org)
    except oso.forbidden_error:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    except oso.not_found_error:
        raise HTTPException(status.HTTP_404_NOT_FOUND)

    details = org_management.get_user_file_report(
        db=db, organization_id=org_id, user_file_id=user_file_id
    )

    if not details:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="User file report not found"
        )

    return schemas.BulkUserNotificationDetail(**details)
