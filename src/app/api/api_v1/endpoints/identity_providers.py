from fastapi import APIRouter, Depends

from app import schemas
from app.adapters.iam.base import AbstractIAM
from app.api import deps
from app.schemas import KeycloakIdentityProvider

router = APIRouter()


@router.get(
    path="",
    response_model=list[schemas.KeycloakIdentityProvider],
    response_model_by_alias=False,
    dependencies=[Depends(deps.authorize_request)],
)
def read_identity_providers(
    iam: AbstractIAM = Depends(deps.get_iam),
) -> list[KeycloakIdentityProvider]:
    identity_providers = iam.identity_providers.get_many()
    return [
        schemas.KeycloakIdentityProvider(
            alias=idp.alias,
            displayName=idp.display_name,
        )
        for idp in identity_providers
    ]
