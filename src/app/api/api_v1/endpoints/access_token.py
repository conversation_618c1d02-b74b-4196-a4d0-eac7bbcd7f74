import logging

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app import schemas
from app.adapters.iam.base import AbstractIAM
from app.adapters.iam.schemas import TokenResponse
from app.api import deps
from app.core.utils import get_object_or_404
from app.models import Organization
from app.services import access_token

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get(
    "/api/token",
    response_model=TokenResponse,
)
def generate_access_token(
    db: Session = Depends(deps.get_db),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    iam: AbstractIAM = Depends(deps.get_iam),
) -> TokenResponse:
    organization = get_object_or_404(
        db, Organization, False, id=user_info.organization.id
    )
    result = access_token.generate_access_token(
        iam,
        user_info=user_info,
        target_org=organization,
    )
    return result
