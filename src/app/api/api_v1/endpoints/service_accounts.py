from fastapi import APIRouter, Depends, HTTPException, Response, status
from sqlalchemy.orm import Session

from app import schemas
from app.adapters.iam.base import AbstractIAM
from app.api import deps
from app.core.config import settings
from app.enums import OrganizationRoleEnum
from app.services import authz, authz_service_account, org_management

router = APIRouter()


@router.post(
    "/service-accounts/{client_id}/enable",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
)
def setup_service_account(
    client_id: str,
    service_account: schemas.ServiceAccountConfig,
    *,
    iam: AbstractIAM = Depends(deps.get_iam),
    service_info: schemas.ServiceInfo = Depends(deps.get_service_info)
):
    """
    Enables the service account for the OAuth client.
    Which allows to perform service-to-service API calls.
    """

    if service_info.client_id != settings.OIDC_CLIENT_ID:
        raise HTTPException(status.HTTP_403_FORBIDDEN)

    try:
        authz_service_account.setup_service_account(
            iam,
            client_id,
            config=service_account,
        )
    except authz_service_account.ClientNotFound as e:
        raise HTTPException(status.HTTP_404_NOT_FOUND, str(e))
    except authz.BaseAuthZException as e:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, str(e))


@router.post(
    "/service-accounts",
    status_code=status.HTTP_201_CREATED,
)
def create_service_account(
    iam: AbstractIAM = Depends(deps.get_iam),
    user_info: schemas.UserInfo = Depends(deps.get_user_info),
    db: Session = Depends(deps.get_db),
    *,
    service_account_in: schemas.ServiceAccountCreate
):
    # Only root distributor admin
    roles = org_management.extract_organization_roles(user_info.realm_access.roles)
    if OrganizationRoleEnum.DISTRIBUTOR_ADMIN not in roles:
        raise HTTPException(status.HTTP_403_FORBIDDEN)
    organization = org_management.get_user_org(
        db,
        iam,
        user_id=user_info.id,
    )
    if not organization or organization.parent is not None:
        raise HTTPException(status.HTTP_403_FORBIDDEN)

    try:
        service_account = authz_service_account.create_service_account(
            iam,
            service_account_in,
            organization,
        )
    except authz.BaseAuthZException as e:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(e))
    return service_account
