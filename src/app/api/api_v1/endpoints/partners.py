from fastapi import APIRouter, Depends, HTTPException, Response
from pydantic import UUID4
from sqlalchemy.orm import Session
from starlette import status

from app import schemas
from app.adapters.core_api.base import AbstractCoreFacade
from app.adapters.iam.base import AbstractIAM
from app.api import deps
from app.services import org_management

router = APIRouter()


@router.post(
    "",
    status_code=status.HTTP_201_CREATED,
    response_model=schemas.UserV2,
    dependencies=[Depends(deps.authorize_request)],
)
def create_user_for_partner(
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    core_api: AbstractCoreFacade = Depends(deps.get_core_api),
    *,
    user_in: schemas.CreatePartnerUser,
) -> schemas.UserV2:
    try:
        user = org_management.add_partner_user(
            db,
            iam,
            core_api,
            user_in=user_in,
        )
    except org_management.UserAlreadyExists as e:
        raise HTTPException(status.HTTP_409_CONFLICT, detail=str(e)) from e
    except org_management.OperationNotAllowed as e:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(e)) from e

    return user


@router.post(
    "/{user_id}/enable",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
    dependencies=[Depends(deps.authorize_request)],
)
def enable_partner_user(
    db: Session = Depends(deps.get_db),
    iam: AbstractIAM = Depends(deps.get_iam),
    *,
    user_id: UUID4,
    cmd: schemas.EnablePartnerUser,
) -> None:
    try:
        org_management.enable_partner_user(db, iam, user_id, cmd)
    except org_management.OperationNotAllowed as e:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(e)) from e
