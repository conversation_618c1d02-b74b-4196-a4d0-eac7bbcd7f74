import logging

from fastapi import APIRouter, Depends, Response, status
from keycloak_client import KeycloakAdmin
from sqlalchemy import select
from sqlalchemy.engine import Connectable
from sqlalchemy.orm import Session

from app.api import deps
from app.services.health_check import ErrorHandler

router = APIRouter()


@router.get("/ping", response_model=str)
def ping() -> str:
    return "pong"


@router.get(
    "/healthy",
    response_model=None,
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
)
def healthy(
    db: Session = Depends(deps.get_db),
    kc: KeycloakAdmin = Depends(deps.get_keycloak_admin),
) -> None:
    handler = ErrorHandler()

    if not isinstance(db.bind, Connectable):
        logging.error(f"Expected db.bind to be Connectable, got {type(db.bind)}")
        raise TypeError()
    with handler(service_name=db.bind.engine.name):
        db.scalar(select([1]))

    with handler(service_name="keycloak"):
        kc.groups.get_many(max_=1)

    handler.raise_for_errors()
