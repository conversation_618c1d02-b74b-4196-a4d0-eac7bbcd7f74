FROM uk-london-1.ocir.io/lrpfi3ly7ayq/nv2/images/fastapi:3.10-poetry

ARG PIP_INDEX_URL
WORKDIR /app
RUN addgroup --system nv2-core-organizations-api && adduser --system --ingroup nv2-core-organizations-api nv2-core-organizations-api
COPY pyproject.toml poetry.lock ./
ENV POETRY_VIRTUALENVS_CREATE=false
RUN curl -sSL https://install.python-poetry.org | POETRY_VERSION=2.1.1 python3
ENV PATH="/root/.local/bin:$PATH"
RUN ln -s /root/.local/bin/poetry /usr/local/bin/poetry
RUN poetry install --no-root --only main

COPY src .
COPY manage.sh .
RUN chmod +x manage.sh
RUN chown -R nv2-core-organizations-api:nv2-core-organizations-api /app
USER nv2-core-organizations-api

ENTRYPOINT ["./manage.sh"]
CMD ["start_service"]
