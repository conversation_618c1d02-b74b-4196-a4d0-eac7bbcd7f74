stages:
  - build
  - test
  - trivy
  - secscan
  - push
  - release
  - deploy
  # - integration_test

variables:
  # set to 'true' if you want continous delivery to DEV
  AUTO_DEPLOY_MASTER: 'false'
  # set how long to wait for deployment operations to complete
  DEPLOYMENT_TIMEOUT: 360s
  # set to 'true' if you want back deploy to OPS button
  DEPLOY_OPS: 'true'
  # exclude lint job from pipeline when set to 'true'
  # TEST_SKIP_LINT: 'false'
  # exclude mypy job from pipeline when set to 'true'
  # TEST_SKIP_MYPY: 'false'
  # exclude pytest job from pipeline when set to 'true'
  # TEST_SKIP_PYTEST: 'false'
  # set to 'true' if tests require database
  TEST_REQUIRES_POSTGRES: 'true'
  # set to 'true' if tests require Keycloak (implies previous variable)
  TEST_REQUIRES_KEYCLOAK: 'true'
  # use below variables to overwrite command of the corresponding jobs with your own
  # also variable ${COMMAND} is available and will be evaluated to default command from shared pipeline
  # TEST_COMMAND_LINT:
  # TEST_COMMAND_MYPY:
  TEST_COMMAND_PYTEST: >
    python -m tests_pre_start; 
    coverage erase;
    coverage run --include=src/app/* -m pytest -ra --maxfail=2 --junitxml=${CI_JOB_NAME}/junit.xml;
    coverage report -m;
    coverage xml -o ${CI_JOB_NAME}/coverage.xml
  TEST_COMMAND_REQUIREMENTS: make install-test-deps
  CLUSTER_ENVIRONMENT:
    value: qa
    description: Please enter qa

#Generate Reports for Distributors Admin
integration_test:
  stage: test
  image:
    name: postman/newman:alpine
    entrypoint: [""]
  before_script:
    - newman --version
    - npm install -g newman-reporter-html
    - npm install -g newman-reporter-htmlextra
  script:
    - set -e  # Stop on first error
    - newman run tests/integration_tests/CP-DA-Organization-Management.postman_collection.json -e tests/integration_tests/CP_Env_DA.postman_environment.json --env-var "Token_Username=0oa6iscigwIXJV4uv5d7" --env-var "Token_Password=Zr8WaYUXdvAvlu7P0L_elwcXPlM84U0Q-LHlzL2X" --reporters cli,htmlextra --reporter-htmlextra-export ./Organization_DA.html || true 
    - newman run tests/integration_tests/CP-CA-Organization-Management.postman_collection.json -e tests/integration_tests/CP_Env_CA.postman_environment.json --env-var "Token_Username=0oa6iscigwIXJV4uv5d7" --env-var "Token_Password=Zr8WaYUXdvAvlu7P0L_elwcXPlM84U0Q-LHlzL2X" --reporters cli,htmlextra --reporter-htmlextra-export ./Organization_CA.html
  allow_failure: true
  artifacts:
    when: always
    paths:
      - Organization_DA.html
      - Organization_CA.html
  rules:
    - if: '$CI_COMMIT_BRANCH == "CP_503_Add_integration_test_for_validation" && $CLUSTER_ENVIRONMENT == "qa"'
      when: manual
    # - when: always



include:
  - project: devops/build
    file: /pipelines/build-docker-image.yaml
  # For information on shared pipeline with tests pleas read the document:
  # https://nextgenclearing.atlassian.net/wiki/spaces/DEVOPS/pages/454262803/How+to+customize+included+CI+test+stage
  - project: devops/test
    file: /pipelines/sec-scan.yaml
  - project: devops/test
    file: /pipelines/python-tests.yaml
  - project: devops/test
    file: /pipelines/trivy-scan.yaml
  - project: devops/tools
    file: /pipelines/tag-release-docker-image.yaml
  - project: devops/tools
    file: /pipelines/create-gitlab-release.yaml
  - project: devops/deploy
    file: /pipelines/trigger-deployment.yaml
