# Organizations API

## Local setup actions
1. Create a database
2. Create the `.env` file:
```shell
cd <path-to-project-directory>
cp example.env src/.env
vim src/.env
# fill in the required variables
```
3. Install [poetry](https://python-poetry.org/docs/#installation)
4. (Optional) configure poetry to use the [existing virtual environment](https://python-poetry.org/docs/managing-environments/)
5. Activate the virtual environment(poetry will create one if not configured in previous step):
```shell
poetry shell
```
6. Install dependencies
```shell
make install-all-deps
```
7. Install pre-commit
```shell
pre-commit install
```
8. Run alembic migrations
```shell
cd src
alembic upgrade head
```
9. Start application
```shell
cd src
python -m app.main
```

### Examine documentation
Go to http://localhost:8000 and click the `Authorize` button

###Migrations

As during local development your app directory is mounted as a volume inside the container, you can also run the migrations with `alembic` commands inside the container and the migration code will be in your app directory (instead of being only inside the container). So you can add it to your git repository.

Make sure you create a "revision" of your models and that you "upgrade" your database with that revision every time you change them. As this is what will update the tables in your database. Otherwise, your application will have errors.


* If you created a new model in `./backend/app/app/models/`, make sure to import it in `./backend/app/app/db/base.py`, that Python module (`base.py`) that imports all the models will be used by Alembic.

* After changing a model (for example, adding a column), inside the container, create a revision, e.g.:

```console
$ alembic revision --autogenerate -m "Add column last_name to User model"
```

* Commit to the git repository the files generated in the alembic directory.

* After creating the revision, run the migration in the database (this is what will actually change the database):

```console
$ alembic upgrade head
```

If you don't want to use migrations at all, uncomment the line in the file at `./backend/app/db/init_db.py` with:

```python
Base.metadata.create_all(bind=engine)
```

and comment the line in the file `prestart.sh` that contains:

```console
$ alembic upgrade head
```

If you don't want to start with the default models and want to remove them / modify them, from the beginning, without
having any previous revision, you can remove the revision files (`.py` Python files)
under `./backend/app/alembic/versions/`. And then create a first migration as described above.

## Architecture Decision Records (ADRs).

[adr-tools](https://github.com/npryce/adr-tools) is used to log ADRs for this project.

## Running locally with OTEL instrumentation
Before running application via `opentelementry-instrumentation` export next environment variables:

```shell
export OTEL_SERVICE_NAME="organizations-api"
export OTEL_PYTHON_LOG_CORRELATION=true

# will not export any data
export OTEL_LOGS_EXPORTER="none"
export OTEL_TRACES_EXPORTER="none"
export OTEL_METRICS_EXPORTER="none"
```

### SEND USER REGISTRATION MAIL TOGGLE
We have introduced an env variable named SEND_USER_REGISTRATION_MAIL which is by default set as False. This disables
send_email functionality while creating new user in Connected Platform. If we enable it or set it as True, a reset password
email will be sent to user. In this case when new client admin is created, impersonation of that user will redirect us to
reset password page, if password is not already set by the admin created.

### Sengrep check:
```shell
pip install semgrep
semgrep --version
semgrep scan .
```

### Bandit check:
We are only checking bandit for src folder.
```shell
pre-commit run bandit --all-files
```
