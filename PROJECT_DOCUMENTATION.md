# Organizations API - Comprehensive Project Documentation

## Project Overview

The **Organizations API** is a FastAPI-based microservice that manages organizational hierarchies, user memberships, and access control within the Connected Platform ecosystem. It serves as the central authority for organization management, handling distributors, clients, partners, and their relationships.

### Key Purpose
- Manage hierarchical organization structures (Distributors → Clients/Partners)
- Handle user memberships and role assignments within organizations
- Provide authorization and access control through integration with Keycloak
- Support service account management for inter-service communication
- Manage organization-specific configurations (domains, PMN codes, file uploads)

## Architecture & Technology Stack

### Core Technologies
- **FastAPI** (0.68.x) - Modern, fast web framework for building APIs
- **Python 3.10+** - Programming language
- **SQLAlchemy** (1.4.x) - ORM for database operations
- **PostgreSQL** - Primary database
- **Redis** - Caching and session storage
- **Alembic** - Database migration management

### Authentication & Authorization
- **Keycloak** - Identity and Access Management (IAM)
- **OAuth2/OIDC** - Authentication protocol
- **Oso** - Policy-based authorization engine with Polar policies
- **JWT** - Token-based authentication

### Infrastructure & Deployment
- **Docker** - Containerization
- **Gunicorn + Uvicorn** - ASGI server setup
- **Poetry** - Dependency management
- **OpenTelemetry** - Observability and monitoring
- **Prometheus** - Metrics collection

## Key Features & Capabilities

### 1. Organization Management
- **Hierarchical Structure**: Distributors can have sub-distributors and clients
- **Organization Types**: Distributor, Client, Partner
- **CRUD Operations**: Create, read, update organizations
- **Path-based Organization Identification**: Hierarchical path representation

### 2. User Management
- **Organization Membership**: Assign users to organizations with specific roles
- **Role-based Access Control**: Different roles (Admin, User, etc.) with varying permissions
- **User Impersonation**: Support for admin impersonation capabilities
- **Bulk User Operations**: File-based user import/export

### 3. Service Account Management
- **OAuth2 Service Accounts**: Create and manage service accounts for applications
- **Scope-based Permissions**: Configure specific scopes for service accounts
- **Client Credentials Flow**: Support for machine-to-machine authentication

### 4. File Management
- **S3 Integration**: Store and manage user files
- **File Reports**: Track file processing status and validation results
- **User Reports**: Generate reports on user data from uploaded files

## Database Models & Relationships

### Core Models

#### Organization Model (`src/app/models/organization.py`)
```python
class Organization(Base):
    id: int = Column(Integer, Identity(), primary_key=True)
    type: OrganizationTypeEnum = Column(Enum(OrganizationTypeEnum))
    name = Column(String(length=256), unique=True, nullable=False)
    parent_id = Column(Integer)
    parent_type = Column(Enum(OrganizationTypeEnum))
    external_id = Column(GUID, index=True, nullable=True)
    group_id = Column(GUID, index=True, nullable=True)
    created_at = Column(TZTimestamp(timezone=False))
```

### Organization Hierarchy
- **Distributor**: Top-level organizations that can have sub-distributors and clients
- **Client**: Organizations under distributors
- **Partner**: Independent partner organizations

### Related Models
- **Domain**: Email domains associated with organizations
- **PMNCode**: Payment network codes for organizations
- **UserFile**: File uploads associated with organizations
- **FileReport**: Processing status of uploaded files
- **UserReport**: User data extracted from files

## API Endpoints Structure

### V1 API Endpoints (`/v1`)
- **Organizations** (`/organizations`)
  - GET `/` - List organizations with filtering
  - GET `/{org_id}` - Get organization details
  - POST `/{org_id}/clients` - Create client organization
  - GET `/{org_id}/users` - List organization users
  - PUT `/{org_id}/users/{user_id}` - Update user membership
  - DELETE `/{org_id}/users/{user_id}` - Remove user from organization

- **Users** (`/users`) - User management endpoints
- **Service Accounts** (`/service-accounts`) - Service account creation and management
- **Partners** (`/partners`) - Partner organization management
- **Identity Providers** (`/identity-providers`) - External identity provider configuration

### V2 API Endpoints (`/v2`)
- Enhanced versions of V1 endpoints with improved functionality

## Authentication & Authorization

### Authentication Flow
1. **OAuth2/OIDC** integration with Keycloak
2. **JWT tokens** for API access
3. **Service tokens** for inter-service communication

### Authorization Model
- **Oso Policy Engine**: Declarative authorization using Polar policies (`src/app/policy.polar`)
- **Role-based Access Control**: Organization-specific roles and permissions
- **Scope-based Authorization**: Fine-grained permissions for different operations

#### Authorization Setup (`src/app/oso.py`)
```python
oso = Oso()
register_models(oso, Base)
oso.register_class(schemas.UserInfo)
oso.register_class(schemas.ServiceInfo)
oso.load_files([(APP_DIR / "policy.polar")])
```

## External Service Integrations

### Core Services Integration
- **Email Delivery API**: Send notification emails
- **Partner Apps API**: Manage partner applications
- **Contacts API**: User contact management
- **Audit Log Service**: Track all operations for compliance

### Identity & Access Management
- **Keycloak Admin API**: User and group management
- **External Identity Providers**: Support for external OIDC providers (Okta, etc.)

### Storage & Infrastructure
- **AWS S3**: File storage for user uploads
- **Redis**: Caching and session management
- **PostgreSQL**: Primary data storage

## Development Setup

### Prerequisites
- Python 3.10+
- PostgreSQL database
- Redis server
- Poetry for dependency management

### Local Setup Steps
1. **Clone and setup environment**:
   ```bash
   cp example.env src/.env
   # Edit .env with your configuration
   ```

2. **Install dependencies**:
   ```bash
   make install-all-deps
   ```

3. **Database setup**:
   ```bash
   cd src
   alembic upgrade head
   ```

4. **Start application**:
   ```bash
   python -m app.main
   ```

### Development Tools
- **Pre-commit hooks**: Code quality checks
- **Flake8**: Code linting
- **MyPy**: Static type checking
- **Bandit**: Security vulnerability scanning
- **Semgrep**: Additional security scanning

## Testing Approach

### Test Structure
- **Unit Tests**: Individual component testing
- **Integration Tests**: API endpoint testing
- **Factory Pattern**: Test data generation using Factory Boy

### Test Categories
- **API Endpoint Tests**: Comprehensive API testing (`tests/test_api_v1_endpoints/`)
- **Service Layer Tests**: Business logic testing (`tests/test_services/`)
- **Model Tests**: Database model validation (`tests/test_models/`)
- **Adapter Tests**: External service integration testing (`tests/test_adapters/`)

### Running Tests
```bash
make test          # Run all tests
make coverage      # Run tests with coverage report
make tox           # Run tests in multiple environments
```

## Configuration & Deployment

### Environment Configuration
- **Development**: Local development with hot reload
- **Production**: Containerized deployment with Gunicorn
- **Testing**: Isolated test environment

### Key Configuration Areas (`src/app/core/config.py`)
- **Database**: PostgreSQL connection settings
- **Authentication**: Keycloak and OAuth2 configuration
- **External Services**: URLs and credentials for integrated services
- **Storage**: S3 bucket configuration
- **Monitoring**: OpenTelemetry and Prometheus settings

### Deployment Process
1. **Docker Build**: Multi-stage Docker build process (`Dockerfile`)
2. **Database Migration**: Automatic Alembic migrations
3. **Service Startup**: Gunicorn with Uvicorn workers
4. **Health Checks**: Built-in health check endpoints

### Management Commands (`manage.sh`)
The application includes a comprehensive management script with commands for:
- Database operations (migrate, rollback)
- Organization setup (create root organization)
- Service startup and configuration

## Security Features

### Security Measures
- **JWT Token Validation**: Secure token-based authentication
- **Role-based Authorization**: Granular permission control
- **Input Validation**: Pydantic schema validation
- **SQL Injection Prevention**: SQLAlchemy ORM protection
- **Security Scanning**: Bandit and Semgrep integration

### Compliance & Auditing
- **Audit Logging**: All operations logged for compliance
- **User Activity Tracking**: Comprehensive user action logging
- **Data Privacy**: Secure handling of sensitive user data

## Architecture Decision Records (ADRs)

The project maintains ADRs in `doc/architecture/decisions/` covering key decisions:
- Service account management approach
- Authorization scope and role setup
- Application-specific Keycloak configuration

## Architecture Diagrams

Comprehensive visual documentation is available in `ARCHITECTURE_DIAGRAMS.md` including:
- **System Architecture**: High-level component interactions
- **Database ERD**: Entity relationships and data model
- **Organization Hierarchy**: Business structure visualization
- **Authentication Flow**: Security and authorization processes
- **API Request Flow**: Request processing pipeline
- **Deployment Architecture**: Infrastructure and scaling
- **User Management Workflow**: User lifecycle operations
- **Service Account Flow**: Service-to-service authentication
- **File Processing Workflow**: Upload and background processing
- **Code Layer Structure**: Application architecture and organization

These diagrams provide visual references for development, deployment, and troubleshooting activities.

## File Structure Overview

```
├── src/app/                    # Main application code
│   ├── api/                    # API endpoints (v1, v2)
│   ├── core/                   # Core configuration and utilities
│   ├── models/                 # SQLAlchemy database models
│   ├── schemas/                # Pydantic schemas for validation
│   ├── services/               # Business logic layer
│   ├── adapters/               # External service adapters
│   └── db/                     # Database configuration
├── tests/                      # Test suite
├── doc/architecture/decisions/ # Architecture Decision Records
├── pyproject.toml             # Poetry configuration
├── Dockerfile                 # Container configuration
└── manage.sh                  # Management script
```

## Key Code Components

### Main Application Entry Point (`src/app/main.py`)
- FastAPI application setup with OAuth2 configuration
- Middleware for request logging and token handling
- Exception handlers for Keycloak integration
- Prometheus metrics endpoint
- API router inclusion for v1 and v2 endpoints

### Dependency Injection (`src/app/api/deps.py`)
- Database session management
- Authentication token validation
- Actor (User/Service) information extraction
- External service client creation (Keycloak, Platform API)
- Authorization dependency injection

### Business Logic Services (`src/app/services/`)
- **org_management.py**: Core organization CRUD operations
- **authz.py**: Authorization and permission management
- **users.py**: User management operations
- **validation.py**: Input validation and business rules
- **mappers.py**: Data transformation between layers

### Data Models (`src/app/models/organization.py`)
- Polymorphic inheritance for organization types
- Self-referential relationships for hierarchy
- Constraint enforcement at database level
- Audit trail with timestamps

### API Schemas (`src/app/schemas/`)
- Pydantic models for request/response validation
- Organization, user, and authentication schemas
- Type-safe data transfer objects

## Environment Variables

### Required Configuration
```bash
# Database
DATABASE_URI=postgresql://user:pass@host:port/dbname

# Keycloak Authentication
KEYCLOAK_URL=https://auth.domain.com
KEYCLOAK_REALM=realm-name
OIDC_CLIENT_ID=organization-management
OIDC_CLIENT_SECRET=secret
OIDC_AUDIENCE=organization-management

# External Services
EMAIL_DELIVERY_URL=http://email-service:8000/v1
PARTNER_APPS_URL=http://partner-apps:8000/v1
CONTACTS_URL=http://contacts:8000/v1
AUDIT_LOG_URI=http://audit:8000/v1

# Storage
S3_BUCKET_NAME=org-files
S3_REGION=us-east-1
S3_KEY_ID=access-key
S3_SECRET_KEY=secret-key

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

### Optional Configuration
```bash
# Development
DEV_MODE=1
DEBUG=true
LOG_LEVEL=DEBUG

# UI Integration
OIDC_UI_CLIENT_ID=organization-management-ui
PORTAL_URL=https://portal.domain.com
PORTAL_NAME="Connected Platform"

# Email Features
SEND_USER_REGISTRATION_MAIL=true
USER_GUIDELINE_LINK=https://docs.domain.com

# Health Checks
HEALTH_CHECK_SECRET=secret-for-k8s-probes
```

## API Usage Examples

### Authentication
All API calls require Bearer token authentication:
```bash
curl -H "Authorization: Bearer <jwt-token>" \
     https://api.domain.com/v1/organizations
```

### Common Operations
```bash
# List organizations
GET /v1/organizations

# Get organization details
GET /v1/organizations/{org_id}

# Create client organization
POST /v1/organizations/{parent_id}/clients
{
  "name": "New Client",
  "pmn_codes": ["12345"],
  "allowed_domains": ["client.com"]
}

# Manage user membership
PUT /v1/organizations/{org_id}/users/{user_id}
{
  "role": "Admin"
}
```

## Monitoring & Observability

### Metrics
- Prometheus metrics available at `/metrics`
- Request/response metrics via FastAPI instrumentator
- Custom business metrics for organization operations

### Logging
- Structured logging with OpenTelemetry correlation
- Request/response logging middleware
- Audit trail for all operations

### Health Checks
- Kubernetes-compatible health check endpoints
- Database connectivity verification
- External service dependency checks

## Troubleshooting

### Common Issues
1. **Database Connection**: Check DATABASE_URI and PostgreSQL availability
2. **Keycloak Integration**: Verify OIDC configuration and realm settings
3. **Permission Errors**: Review Oso policies and user roles
4. **File Upload Issues**: Check S3 configuration and permissions

### Debug Mode
Enable debug logging:
```bash
export DEBUG=1
export LOG_LEVEL=DEBUG
```

This Organizations API serves as a critical component in the Connected Platform ecosystem, providing robust organization management, user access control, and service integration capabilities with enterprise-grade security and scalability features.
