#!/bin/sh -e

# Set PYTHONPATH if 'src' folder exists
test ! -d src || export PYTHONPATH=${PYTHONPATH:-src}

###############################################################################
# Environment variables assignment and re-assignment
# Use the below link to find out variables set by deployment:
# https://gitlabnv2.flyaps.com/devops/deploy/-/tree/master/environments
# PLEASE UPDATE THE FOLLOWING VARS ACCORDINGLY TO YOUR APPLICATION REQUIREMENTS

export APPLICATION_ENV=${APP_ENV:-"prod"}
export PYTHON_BIN=${PYTHON_BIN:-"$(which python3|xargs realpath|xargs basename)"}
export GUNICORN_BIN=${GUNICORN_BIN:-"gunicorn"}
export APP_HOST=${APP_HOST:-"0.0.0.0"}
export APP_PORT=${APP_PORT:-"8000"}
export GUNICORN_WORKERS=${GUNICORN_WORKERS:-"4"}
export DEV_MODE=${DEV_MODE:-"0"}

DB_URI="${DB_SCHEME}://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
export DATABASE_URI=${SQLALCHEMY_DATABASE_URI:-"${DB_URI}"}

export CONTACTS_URL=${CONTACTS_URL:-"http://nv2-core-contacts-api:8000/v1"}
export EMAIL_DELIVERY_URL=${EMAIL_DELIVERY_URL:-"http://nv2-core-email-delivery-api:8000/v1"}
export PARTNER_APPS_URL=${PARTNER_APPS_URL:-"http://nv2-core-partner-apps-api:8000/v1/partner-apps"}
export LANDING_PAGE_URL=${LANDING_PAGE_URL:-"https://${DOMAIN}"}
export EXTERNAL_LANDING_PAGE=${EXTERNAL_LANDING_PAGE:-"https://dev-75259403.okta.com"}
export EXTERNAL_IDP=${EXTERNAL_IDP:-"okta-oidc"}
export SIGN_OUT_URL=${SIGN_OUT_URL:-"${LANDING_PAGE_URL}/oauth2/sign_out"}
export PRE_AUTH_TOKEN_SECRET=${PRE_AUTH_TOKEN_SECRET:-"Zm9vOmJhcgo="}

export KEYCLOAK_URL=${KEYCLOAK_URL:-"https://${AUTH_HOST}"}
export KEYCLOAK_REALM=${KEYCLOAK_REALM:-"${AUTH_REALM}"}
export OIDC_AUDIENCE=${OIDC_AUDIENCE:-"organization-management"}
export OIDC_CLIENT_ID=${OIDC_CLIENT_ID:-"organization-management"}
export OIDC_CLIENT_SECRET=${OIDC_CLIENT_SECRET:-"org-management-secret"}
export OIDC_UI_CLIENT_ID=${OIDC_UI_CLIENT_ID:-"organization-management-ui"}

export ROOT_ORG_NAME=${ROOT_ORG_NAME:-"Nextgen Clearing"}
export ROOT_ORG_ADMIN_EMAIL=${ROOT_ORG_ADMIN_EMAIL:-"<EMAIL>"}
export ROOT_ORG_ADMIN_PASSWORD=${ROOT_ORG_ADMIN_PASSWORD:-"changeme"}

# OpenTelemetry variables
USE_OTEL=${USE_OTEL:-0}
OTEL_INSTRUMENTATOR=${USE_OTEL:+"opentelemetry-instrument"}
export OTEL_SERVICE_NAME="organizations-api"
export OTEL_PYTHON_LOG_CORRELATION=true
export OTEL_LOGS_EXPORTER="none"
export OTEL_TRACES_EXPORTER="none"
export OTEL_METRICS_EXPORTER="none"

###############################################################################
# Service management commands implemented as functions start here
# Configure them as needed, but do NOT modify their names
###############################################################################

no_effect() {
	mcmd="${1}"
	echo "Management command '${mcmd}' has no effect for this service"
	return 0
}

render_dot_env() {
	rm -f .env
	VARS=$(egrep '^export' manage.sh | awk '{print $2}' | awk -F= '{print $1}')
	for V in ${VARS}; do echo "${V}='$(eval echo \"\$${V}\")'" >> .env; done
	echo "Created .env file"
}

init_db() {
	migrate_db
}

populate_db() {
	no_effect populate_db
}

migrate_db() {
	${PYTHON_BIN} -m alembic upgrade head

}

rollback_db() {
	rollback_db_revision="${1}"
	if test -z "${rollback_db_revision}"; then
		echo "ERROR: Management command 'rollback_db' requires revision id"
		exit 1
	fi
	${PYTHON_BIN} -m alembic downgrade "${rollback_db_revision}"
}

purge_db() {
	no_effect purge_db
}

run_cmd() {
	/bin/sh -ec "${@}"
}

create_root_org() {
	# Creating root Organization:
	${PYTHON_BIN} -m app.manage create-root-org --ignore-existent

	# Setup organizations authorization:
	${PYTHON_BIN} -m app.manage setup-organizations-authz

	# Setup applications authorization
	${PYTHON_BIN} -m app.manage setup-applications-authz
}

start_app() {
	$OTEL_INSTRUMENTATOR "${GUNICORN_BIN}" app.main:app -w "${GUNICORN_WORKERS}" -k uvicorn.workers.UvicornWorker -b "${APP_HOST}:${APP_PORT}"
}

start_service() {
	render_dot_env
#	create_root_org
	migrate_db && start_app
}

###############################################################################
# Service management commands end here, no updates below this line
###############################################################################

manage_script="${0}"
if test "${#}" = "0"; then
	echo "ERROR: ${manage_script} requires at least one argument as management command"
	exit 1
fi
manage_command="${1}"
shift 1

case "${manage_command}" in
	create_root_org|start_service|start_app|run_cmd|init_db|populate_db|migrate_db|rollback_db|purge_db|render_dot_env)
		"${manage_command}" "${@}"
		;;
	*)
		echo "ERROR: unknown management command '${manage_command}'"
		exit 1
		;;
esac
